###
 # @Author: lxggit <EMAIL>
 # @Date: 2025-04-09 14:34:28
 # @LastEditors: lxggit <EMAIL>
 # @LastEditTime: 2025-04-11 09:41:51
 # @FilePath: \sampling-web-to-b\.env.test
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 
# 编译模式
NODE_ENV = 'production'
# 开发联调环境配置
MODE = 'test'

# 开发环境
VUE_APP_BASE_API = 'http://*************:9998'
VUE_APP_WS_API = 'ws://cloud.cyznzs.com/ws'
VUE_APP_LOGIN_API = 'http://*************:9998/auth/login'
# 图片上传基础域名
IMG_BASE_URL = 'http://cloud.cyznzs.com/api'



# 部署路径
VUE_APP_PUBLIC_PATH=/

# 页面 title 前缀
VUE_APP_TITLE=信睿