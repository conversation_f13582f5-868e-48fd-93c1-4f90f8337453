/**
 * 检查Node版本的脚本
 * 提醒用户切换到兼容的Node版本
 */

// 项目所需的Node版本
const REQUIRED_NODE_VERSION = '14.21.3';

// 获取当前Node版本
const currentVersion = process.version;

// 检查是否为所需版本
const isCorrectVersion = currentVersion.startsWith(`v${REQUIRED_NODE_VERSION}`);

if (!isCorrectVersion) {
  // 输出红色警告文字
  console.log('\x1b[31m%s\x1b[0m', '警告: 当前Node版本不匹配!');
  console.log('\x1b[31m%s\x1b[0m', `当前版本: ${currentVersion}`);
  console.log('\x1b[31m%s\x1b[0m', `需要版本: v${REQUIRED_NODE_VERSION}`);
  console.log('\x1b[31m%s\x1b[0m', '请先执行以下命令切换Node版本:');
  console.log('\x1b[36m%s\x1b[0m', `nvm use ${REQUIRED_NODE_VERSION}`);
  
  // 如果想在版本不匹配时阻止项目启动，可以取消下面这行的注释
  // process.exit(1);
} else {
  console.log('\x1b[32m%s\x1b[0m', `✓ 当前Node版本 ${currentVersion} 符合要求，正在启动项目...`);
}