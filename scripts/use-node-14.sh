#!/bin/bash

# 输出提示信息
echo "正在切换到 Node v14.21.3..."

# 检查是否安装了 nvm
if [ -z "$(command -v nvm)" ]; then
  echo "错误: 未找到 nvm 命令"
  echo "请确保您已安装 nvm 并正确配置环境"
  echo "安装指南: https://github.com/nvm-sh/nvm#installing-and-updating"
  exit 1
fi

# 尝试切换到 Node v14.21.3
nvm use 14.21.3

# 检查命令是否成功
if [ $? -ne 0 ]; then
  echo "错误: 切换失败"
  echo "您可能没有安装 Node v14.21.3"
  echo "请尝试运行: nvm install 14.21.3"
  exit 1
fi

echo "已成功切换到 Node v14.21.3"
echo "您现在可以使用 npm start 启动项目"