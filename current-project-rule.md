# 样例采集平台前端项目规范

## 项目概述

这是一个基于 Vue2 的企业级前端项目，使用 Element UI 作为 UI 框架，Vuex 进行状态管理，Vue Router 进行路由管理。项目遵循模块化和组件化开发理念，代码结构清晰，便于维护和扩展。

## 技术栈

- **框架**: Vue 2.6
- **UI组件库**: Element UI 2.15
- **状态管理**: Vuex 3.4
- **路由管理**: Vue Router 3.2
- **CSS预处理器**: SCSS
- **HTTP请求**: 封装的Axios模块
- **构建工具**: Vue CLI 4
- **代码规范**: ESLint

## 目录结构规范

```
src/
  ├── api/              # API请求模块
  │   ├── modules/      # 按功能模块分类的API
  │   ├── request.js    # Axios封装和拦截器
  │   └── index.js      # API统一出口
  ├── assets/           # 静态资源文件
  ├── components/       # 全局公共组件
  ├── config/           # 全局配置
  ├── pages/            # 页面组件
  ├── router/           # 路由配置
  ├── store/            # Vuex状态管理
  ├── style/            # 全局样式
  ├── utils/            # 工具函数
  └── views/            # 视图组件
```

## 命名规范

### 文件命名

1. **组件文件**: 使用 PascalCase (首字母大写的驼峰命名)
   - 例如: `UserProfile.vue`, `HeaderNavbar.vue`

2. **JS工具文件**: 使用 camelCase (首字母小写的驼峰命名)
   - 例如: `fileUtils.js`, `dateFormatter.js`

3. **API模块文件**: 使用功能模块名的 camelCase
   - 例如: `sysUser.js`, `organization.js`

### 变量命名

1. **普通变量**: 使用 camelCase
   - 例如: `userData`, `orderList`

2. **常量**: 使用全大写下划线分隔
   - 例如: `MAX_COUNT`, `API_URL`

3. **私有变量**: 使用下划线前缀
   - 例如: `_privateMethod`, `_internalState`

## 组件规范

### 组件结构

组件应遵循以下结构：

```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
// 导入部分
import { mapGetters } from 'vuex'
import ComponentA from '@/components/ComponentA'

export default {
  name: 'ComponentName',
  
  components: {
    ComponentA
  },
  
  props: {
    propA: {
      type: String,
      required: true
    }
  },
  
  data() {
    return {
      // 组件数据
    }
  },
  
  computed: {
    // 计算属性
  },
  
  watch: {
    // 监听属性变化
  },
  
  created() {
    // 组件创建时的钩子
  },
  
  mounted() {
    // 组件挂载时的钩子
  },
  
  methods: {
    // 方法定义
  }
}
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```

### Props 定义

- 始终使用详细的Prop定义，包括类型、是否必须和默认值
- 使用驼峰命名法定义props
- 在模板中使用kebab-case传递props

```js
props: {
  status: {
    type: String,
    required: true,
    validator: value => ['success', 'warning', 'error'].includes(value)
  },
  userList: {
    type: Array,
    default: () => []
  }
}
```

## API 请求规范

### API 模块化

- 按功能模块组织API请求文件
- 所有API函数使用命名导出
- 函数名应表明其用途和请求类型

```js
// src/api/modules/sysUser.js
import { http } from '@/api/request'

/**
 * 获取用户列表
 * @param {Object} params 查询参数
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getUserList(params) {
  return http.get('/user/list', params)
}

/**
 * 修改用户信息
 * @param {Object} data 用户数据
 * @returns {Promise<AxiosResponse<any>>}
 */
export function updateUser(data) {
  return http.put('/user/update', data)
}
```

### API 调用规范

- 不使用直接导入的axios，始终使用封装好的API函数
- 统一处理请求成功和失败的情况
- 利用async/await简化Promise处理

```js
import { getUserInfo } from '@/api/modules/sysUser'

export default {
  methods: {
    async fetchUserInfo() {
      try {
        this.loading = true
        const res = await getUserInfo({ id: this.userId })
        if (res.code === 200) {
          this.userInfo = res.data
          this.$message.success('获取用户信息成功')
        } else {
          this.$message.error(res.message || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.$message.error('获取用户信息失败，请检查网络连接')
      } finally {
        this.loading = false
      }
    }
  }
}
```

## 状态管理规范

### Vuex Module

- 按功能模块分割Vuex状态
- 使用命名空间隔离不同模块
- 保持action、mutation和getter的命名一致性

```js
// src/store/modules/user.js
export default {
  namespaced: true,
  
  state: {
    userInfo: {},
    permissions: []
  },
  
  mutations: {
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo
    },
    SET_PERMISSIONS(state, permissions) {
      state.permissions = permissions
    }
  },
  
  actions: {
    // 异步获取用户信息
    async getUserInfo({ commit }, userId) {
      try {
        const res = await getUserInfo({ id: userId })
        if (res.code === 200) {
          commit('SET_USER_INFO', res.data)
          return res.data
        }
        return null
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return null
      }
    }
  },
  
  getters: {
    userRole: state => state.userInfo.role || '',
    hasPermission: state => permission => state.permissions.includes(permission)
  }
}
```

### 状态访问

- 组件中使用mapState、mapGetters、mapActions等辅助函数
- 避免直接修改state，始终通过mutation修改

## 样式规范

### 全局样式

- 全局样式定义在 `src/style` 目录
- 使用变量管理颜色、字体、尺寸等
- 避免过多的全局样式，优先使用局部样式

### 组件样式

- 组件样式使用 `scoped` 属性隔离
- 使用BEM命名规范组织CSS类名
- 使用 SCSS 嵌套提高可维护性

```scss
.user-card {
  margin: 10px;
  
  &__header {
    font-size: 16px;
    font-weight: bold;
  }
  
  &__content {
    padding: 15px;
  }
  
  &--highlighted {
    border: 1px solid $primary-color;
  }
}
```

## 路由规范

### 路由配置

- 路由配置按功能模块组织
- 使用路由元信息(meta)定义页面属性
- 懒加载非首屏路由组件

```js
const routes = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/index'),
    meta: {
      title: '控制台',
      icon: 'dashboard',
      auth: true,
      keepAlive: true
    }
  }
]
```

## 注释规范

### 文件注释

每个文件顶部应有文件说明：

```js
/**
 * 用户管理相关的API
 * <AUTHOR>
 * @date 2023-11-20
 */
```

### 函数注释

使用JSDoc风格的函数注释：

```js
/**
 * 格式化日期时间
 * @param {Date|string|number} date 日期对象或可转换为日期的值
 * @param {string} [format='YYYY-MM-DD HH:mm:ss'] 输出格式
 * @returns {string} 格式化后的日期字符串
 */
export function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  // 实现逻辑
}
```

## 异常处理规范

- 所有API请求应有try/catch处理
- 使用统一的错误提示方式
- 记录关键错误信息到控制台

## 提交规范

遵循Angular提交规范：

- `feat`: 新特性
- `fix`: 修复Bug
- `docs`: 文档更新
- `style`: 代码风格变更（不影响功能）
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变动

示例：
```
feat: 添加用户管理页面的批量删除功能
fix: 修复数据表格排序功能异常
```

## 性能优化建议

1. 合理使用v-if和v-show
2. 大列表使用虚拟滚动
3. 路由组件使用懒加载
4. 合理分割组件，避免过大组件
5. 使用keep-alive缓存频繁切换的组件

## 安全实践

1. 防止XSS攻击，过滤用户输入
2. 敏感信息（如token）存储在安全位置
3. 防止CSRF攻击

## 附录

### 常用工具函数

- `src/utils/request.js`: 网络请求封装
- `src/utils/auth.js`: 身份验证相关
- `src/utils/validate.js`: 表单验证

### 相关文档

- Vue2官方文档: [https://v2.vuejs.org/](https://v2.vuejs.org/)
- Element UI文档: [https://element.eleme.io/](https://element.eleme.io/)
- Vuex文档: [https://vuex.vuejs.org/](https://vuex.vuejs.org/)
- Vue Router文档: [https://router.vuejs.org/](https://router.vuejs.org/)