###
 # @Author: lxggit <EMAIL>
 # @Date: 2025-04-15 10:05:50
 # @LastEditors: lxggit <EMAIL>
 # @LastEditTime: 2025-04-17 09:15:53
 # @FilePath: \sampling-web-to-b\.env.development
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 
# 编译模式
NODE_ENV = development
# 开发联调环境配置
MODE = dev

# 开发环境
# 殷杰
# VUE_APP_BASE_API = http://*************:9998
# 陈庆
# VUE_APP_BASE_API = http://***************:9998
# 齐峰
# VUE_APP_BASE_API = http://***************:9998
# 线上测试环境
# VUE_APP_BASE_API = http://*************:9998
# 线上生成环境
VUE_APP_BASE_API = https://cloud.cyznzs.com/api
VUE_APP_WS_API = ws://cloud.cyznzs.com/ws
# VUE_APP_LOGIN_API = https://login.ejclims.com/service/external/getUserByNamePwdLims
VUE_APP_LOGIN_API = http://*************:9998/auth/login
# 图片上传基础域名
IMG_BASE_URL = http://cloud.cyznzs.com/api

# 部署路径
VUE_APP_PUBLIC_PATH=/

# 页面 title 前缀
VUE_APP_TITLE=信睿
