#!/bin/bash

# 检查是否安装了 nvm
if [ -z "$(command -v nvm 2>/dev/null)" ]; then
  # 尝试加载 nvm
  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
  
  # 再次检查是否有 nvm
  if [ -z "$(command -v nvm 2>/dev/null)" ]; then
    echo "错误: 未找到 nvm 命令"
    echo "请确保您已安装 nvm 并正确配置环境"
    echo "安装指南: https://github.com/nvm-sh/nvm#installing-and-updating"
    exit 1
  fi
fi

# 切换到项目所需的 Node 版本
echo "正在切换到 Node v14.21.3..."
nvm use 14.21.3

# 检查是否成功切换
if [ $? -ne 0 ]; then
  echo "错误: 未找到 Node v14.21.3，尝试安装..."
  nvm install 14.21.3
  
  if [ $? -ne 0 ]; then
    echo "安装 Node v14.21.3 失败，请手动安装后再试"
    exit 1
  fi
  
  nvm use 14.21.3
fi

# 检查当前 Node 版本
NODE_VERSION=$(node -v)
if [[ $NODE_VERSION != v14.21.3* ]]; then
  echo "警告: 节点版本不匹配! 当前版本: $NODE_VERSION"
  exit 1
fi

echo "✓ 已切换到 Node $NODE_VERSION"

# 启动项目
echo "正在启动项目..."
npm run serve