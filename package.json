{"name": "sampling-helper-web", "version": "1.0.0", "private": true, "scripts": {"prestart": "node scripts/check-node-version.js", "start": "npm run serve", "start:n14": "bash ./start.sh", "serve": "vue-cli-service serve --mode development", "test": "vue-cli-service build --mode test", "prod": "vue-cli-service build --mode prod", "build": "vue-cli-service build", "lint": "eslint --ext .js,.vue src", "fix": "eslint --fix src", "tailwind": "npx tailwindcss -i ./src/style/tailwind.css -o ./src/style/output.css --watch", "use-node-14": "bash scripts/use-node-14.sh"}, "lint-staged": {"*.{js,vue}": ["eslint --fix src", "eslint --ext .js,.vue src", "git add"]}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@babel/eslint-parser": "^7.17.0", "@riophae/vue-treeselect": "^0.4.0", "axios": "^0.21.1", "clipboard": "^2.0.8", "compressorjs": "^1.1.1", "core-js": "^3.6.5", "countup.js": "^2.8.0", "dayjs": "^1.11.10", "echarts": "^5.3.0", "element-ui": "^2.15.6", "file-saver": "^2.0.5", "fuse.js": "^6.4.6", "html2canvas": "^1.1.4", "js-cookie": "^3.0.1", "jsencrypt": "^3.3.2", "nprogress": "^0.2.0", "qrcode": "^1.5.4", "screenfull": "^5.1.0", "style-resources-loader": "^1.4.1", "svgo": "^1.2.0", "swiper": "^5.4.5", "umy-ui": "^1.1.6", "update": "^0.7.4", "vue": "^2.6.11", "vue-awesome-swiper": "^4.1.1", "vue-countup-v2": "^4.0.0", "vue-drag-resize": "^1.5.4", "vue-pdf": "^4.2.0", "vue-qr": "^2.5.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.2.0", "vue-ueditor-wrap": "^2.5.6", "vuex": "^3.4.0", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^8.3.6", "@commitlint/config-conventional": "^8.3.6", "@vue/cli-plugin-babel": "^4.5.0", "@vue/cli-plugin-eslint": "^4.5.0", "@vue/cli-plugin-router": "^4.5.0", "@vue/cli-plugin-vuex": "^4.5.0", "@vue/cli-service": "^4.5.0", "eslint": "^7.1.0", "eslint-config-standard": "^16.0.3", "eslint-friendly-formatter": "^4.0.1", "eslint-plugin-vue": "^8.5.0", "eslint-webpack-plugin": "^3.1.1", "husky": "^4.3.8", "lint-staged": "^12.3.4", "node-sass": "^4.14.1", "postcss": "^8.2.7", "postcss-px-to-viewport": "^1.1.1", "postcss-pxtorem": "^5.1.1", "prettier": "^2.5.1", "sass-loader": "^7.3.1", "svg-sprite-loader": "^4.1.3", "tailwindcss": "^3.3.6", "vue-template-compiler": "^2.6.11"}}