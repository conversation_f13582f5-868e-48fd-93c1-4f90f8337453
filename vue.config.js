// const { defineConfig } = require("@vue/cli-service");
// module.exports = defineConfig({
//   transpileDependencies: true,
// });
const path = require("path");

const port = process.env.port || process.env.npm_config_port || 9080; // 端口

function addStyleResource(rule) {
  rule
    .use("style-resource")
    .loader("style-resources-loader")
    .options({
      patterns: [
        // 需要全局导入的sass路径，自己修改，我这里引入了两个sass文件
        resolve("src/style/variables.scss"),
        resolve("src/style/mixin.scss"),
      ],
    });
}
function resolve(url) {
  return path.resolve(__dirname, url);
}

module.exports = {
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  publicPath: process.env.NODE_ENV === "production" ? "./" : "/",
  configureWebpack: {
    devtool: "source map",
    ...(process.env.NODE_ENV === "production"
      ? {
          optimization: {
            minimizer: [
              new (require("terser-webpack-plugin"))({
                sourceMap: false,
                terserOptions: {
                  compress: {
                    pure_funcs: [
                      "console.log",
                      "console.info",
                      "console.debug",
                      "console.warn",
                    ],
                    drop_console: false,
                    warnings: false,
                  },
                  output: {
                    beautify: false,
                    comments: false,
                  },
                },
              }),
            ],
          },
        }
      : {}),
  },
  outputDir: "dist",
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: "static",
  // 不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: true,
  // webpack-dev-server 相关配置
  lintOnSave: false,
  devServer: {
    overlay: {
      warnings: false,
      errors: true,
    },
    host: "0.0.0.0",
    port,
    // 直接连接API，不使用代理
    // proxy: {
    //   // 使用绝对路径，不使用环境变量
    //   "/api": {
    //     target: "http://*************:9998", // 直接连接线上API
    //     changeOrigin: true,
    //     pathRewrite: {
    //       "^/api": "", // 将/api前缀移除
    //     },
    //   },
    // },
    disableHostCheck: true,
  },
  chainWebpack(config) {
    const types = ["vue-modules", "vue", "normal-modules", "normal"];
    types.forEach((type) =>
      addStyleResource(config.module.rule("scss").oneOf(type))
    );

    config
      .plugin("define")
      .tap((args) => {
        args[0]["process.env"].MODE = JSON.stringify(process.env.MODE);
        return args;
      })
      .end();
    config.module.rule("svg").exclude.add(resolve("src/assets/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();
  },
  pluginOptions: {
    "style-resources-loader": {
      preProcessor: "scss",
      patterns: [
        // 注意：scss不能使用别名路径
        path.resolve(__dirname, "./src/style/variables.scss"),
        path.resolve(__dirname, "./src/style/mixin.scss"),
      ],
    },
  },
};
