module.exports = {
  presets: ["@vue/cli-plugin-babel/preset"],
  // 生产环境下删除console.log
  env: {
    production: {
      // 使用内置的babel-plugin-transform-member-expression-literals转换
      // 将console.log转为undefined.log，但保留console.error
      plugins: [
        function () {
          return {
            visitor: {
              CallExpression(path) {
                const callee = path.get("callee");

                if (callee.isMemberExpression()) {
                  const object = callee.get("object");
                  const property = callee.get("property");

                  if (
                    object.isIdentifier() &&
                    object.node.name === "console" &&
                    property.isIdentifier() &&
                    ["log", "info", "debug", "warn"].includes(
                      property.node.name
                    )
                  ) {
                    // 不处理console.error
                    if (property.node.name === "error") return;

                    // 替换console为空对象，这样调用不会报错，但不会有输出
                    object.replaceWith({
                      type: "ObjectExpression",
                      properties: [],
                    });
                  }
                }
              },
            },
          };
        },
      ],
    },
  },
};
