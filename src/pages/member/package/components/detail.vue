<template>
  <div class='wrapper'>
    <el-dialog
      title='查看'
      :visible.sync='dialogVisible'
      v-loading='loading'
      width='60%'>
      <el-form :model='form' ref='form' label-width='180px' label-position='left' size='small'
               class='dialog-form col-18'>
        <el-form-item label='会员类型：' class='item'><p>{{ form.memberTypeName }}</p></el-form-item>
        <el-form-item label='套餐类型：' class='item'><p>{{ form.memberMealName }}</p></el-form-item>
        <!-- <el-form-item label='会员套餐名称：' class='item'><p>{{ form.memberMealName }}</p></el-form-item> -->
        <el-form-item label='划线价格（元）：' class='item'><p>{{ form.scribePrice }}</p></el-form-item>
        <el-form-item label='套餐实际价格（元）：' class='item'><p>{{ form.mealPrice }}</p></el-form-item>
        <!-- <el-form-item label='会员天数（天）：' class='item'><p>{{ form.memberDays }}</p></el-form-item> -->
        <el-form-item label='新用户是否优惠：' class='item'><p>{{ form.newUserLabel }}</p></el-form-item>
        <el-form-item label='老用户重新购买是否优惠：' class='item'><p>{{ form.oldUserLabel }}</p></el-form-item>
        <el-form-item label='状态：' class='item'><p>已上架</p></el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>

import {detailMemberPackage} from '@/api/modules/member'

export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      form: {
        statusLabel: null,//状态 0 待上架1 已上架
        // memberMealName: null,//套餐名称
        mealType: null, //套餐类型
        memberTypeName: null,//会员类型（1 VIP会员 2 SVIP会员）
        scribePrice: null,//划线价格
        mealPrice: null,//套餐价格
        // memberDays: null,//会员天数
        newUserLabel: null,//新用户是否优惠显示label
        oldUserLabel: null//老用户是否优惠显示label
      }
    }
  },
  methods: {
    loadData() {
      detailMemberPackage(this.id).then(res => {
        this.form = res.data
      })
    },
    open(id) {
      this.id = id
      this.form = {
        statusLabel: null,//状态 0 待上架1 已上架
        // memberMealName: null,//套餐名称
        mealType: null, //套餐类型
        memberTypeName: null,//会员类型（1 VIP会员 2 SVIP会员）
        scribePrice: null,//划线价格
        mealPrice: null,//套餐价格
        // memberDays: null,//会员天数
        newUserLabel: null,//新用户是否优惠显示label
        oldUserLabel: null//老用户是否优惠显示label
      }
      this.dialogVisible = true
      this.loadData()
    },
    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 25%;

  .item {
    margin-bottom: 0;
  }

  .sub {
    font-weight: bold;
    color: #333333;
    font-size: 16px;
    margin: 20px 0 10px 0;
  }
}

</style>
