<template>
  <div class='wrapper'>
    <el-dialog
      title='编辑会员套餐'
      :visible.sync='dialogVisible'
      width='60%'
      @close='close'>
      <el-form :model='form' :rules='rules' ref='form' label-width='180px' label-position='left' size='small'
               class='dialog-form'>
        <el-form-item class='item' label='会员类型：' prop='memberType'>
          <el-select v-model='form.memberType' placeholder='请选择' class='col-24'>
            <el-option
              v-for='item in optionMemberType'
              :key='item.value'
              :label='item.label'
              :value='item.value'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class='item' label='会员类型：' prop='mealType'>
          <el-select v-model='form.mealType' placeholder='请选择' class='col-24'>
            <el-option
              v-for='item in mealTypeArr'
              :key='item.value'
              :label='item.label'
              :value='item.value'>
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item class='item' label='会员套餐名称：' prop='memberMealName'>
          <el-input v-model='form.memberMealName'></el-input>
        </el-form-item> -->
        <el-form-item class='item' label='划线价格：' prop='scribePrice'>
          <div class='content'>
            <el-input v-model='form.scribePrice' class='in' v-Int></el-input>
            <label class='ml10 mr10'>元</label>
            <label>请输入≥0的整数</label>
          </div>
        </el-form-item>
        <el-form-item class='item' label='套餐实际价格：' prop='mealPrice'>
          <div class='content'>
            <el-input v-model='form.mealPrice' class='in' v-Int></el-input>
            <label class='ml10 mr10'>元</label>
            <label>请输入≥0的整数</label>
          </div>
        </el-form-item>
        <!-- <el-form-item class='item' label='会员天数：' prop='memberDays'>
          <div class='content'>
            <el-input v-model='form.memberDays' class='in' v-Int></el-input>
            <label class='ml10 mr10'>天</label>
            <label>请输入≥0的整数</label>
          </div>
        </el-form-item> -->
        <el-form-item class='item' label='新用户是否优惠：'>
          <div class='content'>
            <el-radio-group v-model='form.newUserDiscount' class='rg1'>
              <el-radio :label='0' class='ra'>无优惠</el-radio>
              <el-radio :label='1' class='ra'>首月购买价格</el-radio>
            </el-radio-group>
            <div class='rg-content'>
              <el-input v-model='form.newDiscountPrice' maxlength='6' style='width: 80px' v-Int></el-input>
              <label class='ml10 mr10'>元</label>
              <label>请输入≥0的整数</label>
            </div>
          </div>
        </el-form-item>
        <el-form-item label='老用户重新购买是否优惠：'>
          <div class='flex'>
            <el-radio-group v-model='form.oldUserDiscount' class='rg2'>
              <el-radio :label='0' class='ra'>无优惠</el-radio>
              <el-radio :label='1' class='ra'>间隔</el-radio>
            </el-radio-group>
            <div class='rg-content'>
              <el-input v-model='form.intervalMonthCount' maxlength='6' style='width: 80px' v-Int></el-input>
              <label class='ml10 mr10'>个月，重新购买会员首月价格</label>
              <el-input v-model='form.oldDiscountPrice' maxlength='6' style='width: 80px' v-Int></el-input>
              <label class='ml10 mr10'>元</label>
              <label>请输入≥0的整数</label>
            </div>
          </div>
        </el-form-item>
        <el-form-item class='item' label='状态'>
          <el-switch
            v-model='status' />
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {preMemberPackage, updateMemberPackage} from '@/api/modules/member'

export default {
  data() {
    return {
      id: '',
      dialogVisible: false,
      loading: false,
      status: false,
      form: {
        // memberMealName: null,//套餐名称
        mealType: null,//套餐类型 0月 1季度 2年
        memberType: null,//会员类型（1 VIP会员 2 SVIP会员）
        scribePrice: null,//划线价格
        mealPrice: null,//套餐价格
        // memberDays: null,//会员天数
        newUserDiscount: 0,//新用户是否优惠  0  无  1 优惠
        newDiscountPrice: null,//优惠价格 （1 必填）
        oldUserDiscount: 0,//老用户重新购买是否优惠(0 无  1 是)
        intervalMonthCount: null,//间隔月数(1 必填)
        oldDiscountPrice: null,//老用户重新购买价格(1 必填)
        status: null//状态 0 待上架 1 已上架
      },
      rules: {
        memberType: [
          {required: true, message: '请选择会员类型', trigger: 'change'}
        ],
        mealType: [
          {required: true, message: '请选择套餐类型', trigger: 'change'}
        ],
        // memberMealName: [
        //   {required: true, message: '请输入套餐名称', trigger: 'blur'}
        // ],
        scribePrice: [
          {required: true, message: '请输入划线价格', trigger: 'blur'}
        ],
        mealPrice: [
          {required: true, message: '请输入套餐价格', trigger: 'blur'}
        ],
        // memberDays: [
        //   {required: true, message: '请输入会员天数', trigger: 'blur'}
        // ]
      },
      optionMemberType: [
        {value: 1, label: 'VIP会员'},
        {value: 2, label: 'SVIP会员'}
      ],
      mealTypeArr: [
        {value: 0, label: '月度会员'},
        {value: 1, label: '季度会员'},
        {value: 2, label: '年度会员'}
      ]
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.newUserDiscount === 1) {
            if (!this.form.newDiscountPrice) {
              this.$message.error('请输入新用户首月购买价格')
              return
            }
          }
          if (this.form.oldUserDiscount === 1) {
            if (!this.form.intervalMonthCount) {
              this.$message.error('请输入老用户重新购买优惠间隔')
              return
            }
            if (!this.form.oldDiscountPrice) {
              this.$message.error('请输入老用户重新购买优惠价格')
              return
            }
          }
          this.form.status = this.status ? 1 : 0
          this.form.id = this.id

          this.loading = true
          updateMemberPackage(this.form).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
              this.$emit('confirm')
            }, 500)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    loadData() {
      preMemberPackage(this.id).then(res => {
        this.form = res.data
      })
    },
    open(id) {
      this.id = id
      this.form = {
        // memberMealName: null,//套餐名称
        mealType: null,//套餐类型 0月 1季度 2年
        memberType: null,//会员类型（1 VIP会员 2 SVIP会员）
        scribePrice: null,//划线价格
        mealPrice: null,//套餐价格
        // memberDays: null,//会员天数
        newUserDiscount: 0,//新用户是否优惠  0  无  1 优惠
        newDiscountPrice: null,//优惠价格 （1 必填）
        oldUserDiscount: 0,//老用户重新购买是否优惠(0 无  1 是)
        intervalMonthCount: null,//间隔月数(1 必填)
        oldDiscountPrice: null,//老用户重新购买价格(1 必填)
        status: null//状态 0 待上架 1 已上架
      }
      this.status = false
      this.dialogVisible = true
      this.loadData()
    },
    close() {
      this.dialogVisible = false
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 20%;

  .item {
    width: 70%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.rg1 {
  width: 118px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg2 {
  width: 62px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg-content {
  display: flex;
  align-items: center;
  margin-top: 32px;
}
</style>
