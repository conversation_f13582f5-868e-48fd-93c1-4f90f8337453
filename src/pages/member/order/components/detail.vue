<template>
  <div class='wrapper'>
    <el-dialog
      title='查看详情'
      :visible.sync='dialogVisible'
      width='60%'>
      <el-form :model='form' ref='form' label-width='90px' label-position='left' size='small'
               class='dialog-form'>
        <el-form-item label='交易单号' class='item'><p>{{ form.orderNumber }}</p></el-form-item>
        <el-form-item label='商品名称' class='item'><p>{{ form.mealName }}</p></el-form-item>
        <el-form-item label='单价（元）' class='item'><p>{{ form.mealPrice }}</p></el-form-item>
        <el-form-item label='交易状态' class='item'><p>{{ form.statusLabel }}</p></el-form-item>
        <el-form-item label='支付方式' class='item'><p>{{ form.payTypeName }}</p></el-form-item>
        <el-form-item label='应付金额' class='item'><p>{{ form.copeWithPriceLabel }}</p></el-form-item>
        <el-form-item label='优惠券' class='item'><p>{{ form.discountPriceLabel }}</p></el-form-item>
        <el-form-item label='实付金额' class='item'><p>{{ form.paidInPriceLabel }}</p></el-form-item>
        <el-form-item label='交易时间' class='item'><p>{{ form.transactionTime }}</p></el-form-item>
        <el-form-item label='用户昵称' class='item'><p>{{ form.nickName }}</p></el-form-item>
        <el-form-item label='真实姓名' class='item'><p>{{ form.userName }}</p></el-form-item>
        <el-form-item label='联系方式' class='item'><p>{{ form.phone }}</p></el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {detailMemberOrder} from '@/api/modules/member'

export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      form: {
        orderNumber: null,//订单编号
        nickName: null,//用户昵称
        phone: null,//手机号
        userName: null,//真实姓名
        mealName: null,//套餐名称
        mealPrice: null,//单价
        payTypeName: null,//支付类型
        discountPriceLabel: null,//优惠label
        copeWithPriceLabel: null,//应付金额label
        paidInPriceLabel: null,//实付金额label
        transactionTime: null,//交易时间
        statusLabel: null//状态
      }
    }
  },
  methods: {
    loadData() {
      detailMemberOrder(this.id).then(res => {
        this.form = res.data
        console.log(this.form)
      })
    },
    open(id) {
      this.id = id
      this.form = {
        orderNumber: null,//订单编号
        nickName: null,//用户昵称
        phone: null,//手机号
        userName: null,//真实姓名
        mealName: null,//套餐名称
        mealPrice: null,//单价
        payTypeName: null,//支付类型
        discountPriceLabel: null,//优惠label
        copeWithPriceLabel: null,//应付金额label
        paidInPriceLabel: null,//实付金额label
        transactionTime: null,//交易时间
        statusLabel: null//状态
      }
      this.dialogVisible = true
      this.loadData()
    },
    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  width: 65%;
  margin: 20px 0 20px 35%;

  .item {
    margin-bottom: 0;
  }

  .sub {
    font-weight: bold;
    color: #333333;
    font-size: 16px;
    margin: 20px 0 10px 0;
  }
}

</style>
