<template>
  <div class='wrapper'>
    <el-dialog
      title='新增会员权益'
      :visible.sync='dialogVisible'
      width='60%'
      @close='close'>
      <el-form :model='form' ref='form' :rules='rules' label-width='180px' label-position='left' size='small'
               class='dialog-form'>
        <el-form-item class='item' label='会员类型：' prop='memberType'>
          <el-select v-model='form.memberType' placeholder='请选择' class='col-24'>
            <el-option
              v-for='item in optionMemberType'
              :key='item.value'
              :label='item.label'
              :value='item.value'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class='item' label='权益类型：'>
          <div class='content'>
            <el-select v-model='form.equityTypeId' placeholder='请选择' class='col-24'>
              <el-option
                v-for='item in optionType'
                :key='item.id'
                :label='item.equityName'
                :value='item.id'>
              </el-option>
            </el-select>
            <el-button type='text' class='ml20' size='medium' @click='handleInsertType'>添加权益类型</el-button>
          </div>
        </el-form-item>
        <el-form-item class='item' label='会员权益名称：' prop='equityName'>
          <el-input v-model='form.equityName'></el-input>
        </el-form-item>
        <el-form-item class='item' label='会员权益描述：'>
          <el-input v-model='form.equityDescribe'></el-input>
        </el-form-item>
        <el-form-item class='item' label='会员权益图片：' prop='imageUrl'>
          <el-upload
            class='img-uploader'
            :action='uploadUrl'
            :headers='headers'
            :show-file-list='false'
            :on-success='handleUploadSuccess'
            :on-remove='handleUploadRemove'
            accept='.png,.jpg,.jpge,.webp'>
            <img v-if='imageUrl' :src='imageUrl' class='img'>
            <i v-else class='el-icon-plus img-uploader-icon'></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getToken} from '@/utils/auth'
import {insertEquity, listEquityType} from '@/api/modules/member'

export default {
  data() {
    let validateUpload = (rule, value, callback) => {
      if (!this.form.imageUrl) {
        callback(new Error('请上传权益图片'))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false,
      loading: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/oss/endpoint/put-public-file',
      headers: {
        Authorization: getToken()
      },
      form: {
        memberType: null,//会员类型（1 VIP会员 2 SVIP会员）
        equityTypeId: null,//权益类型id
        equityName: null,//权益名称
        equityDescribe: null,//权益描述
        imageUrl: null//权益图片（一张）
      },
      rules: {
        memberType: [
          {required: true, message: '请选择会员类型', trigger: 'change'}
        ],
        equityName: [
          {required: true, message: '请输入权益名称', trigger: 'blur'}
        ],
        imageUrl: [
          {required: true, validator: validateUpload, trigger: 'change'}
        ]
      },
      imageUrl: null,
      optionMemberType: [
        {value: 1, label: 'VIP会员'},
        {value: 2, label: 'SVIP会员'}
      ],
      optionType: []
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          insertEquity(this.form).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
              this.$emit('confirm')
            }, 500)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    open(data) {
      if (data) {
        this.form = data
      } else {
        this.form = {
          memberType: null,//会员类型（1 VIP会员 2 SVIP会员）
          equityTypeId: null,//权益类型id
          equityName: null,//权益名称
          equityDescribe: null,//权益描述
          imageUrl: null//权益图片（一张）
        }
        this.imageUrl = this.form.imageUrl
      }
      this.loadType()
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
      this.imageUrl = null
      this.$refs.form.resetFields()
    },
    loadType() {
      listEquityType().then(res => {
        this.optionType = res.data
      })
    },
    handleInsertType() {
      this.close()
      this.$emit('insertType', this.form, 1)
    },
    handleUploadSuccess(res) {
      this.imageUrl = res.data.link
      this.form.imageUrl = res.data.link
      this.$refs.form.validateField('imageUrl')
    },
    handleUploadRemove() {
      this.imageUrl = null
      this.form.imageUrl = null
      this.$refs.form.validate()
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 20%;

  .item {
    width: 70%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.rg1 {
  width: 160px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg2 {
  width: 80px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg-content {
  display: flex;
  align-items: center;
  margin-top: 32px;
}

.img-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 88px;
  height: 88px;
}

.img-uploader:hover {
  border-color: #409EFF;
}

.img-uploader-icon {
  font-size: 14px;
  color: #8c939d;
  width: 88px;
  height: 88px;
  line-height: 88px;
  text-align: center;
}

.img {
  width: 88px;
  height: 88px;
  display: block;
}
</style>
