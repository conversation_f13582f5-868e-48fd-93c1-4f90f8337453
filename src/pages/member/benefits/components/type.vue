<template>
  <div class='wrapper'>
    <el-dialog
      title='新增权益类型'
      :visible.sync='dialogVisible'
      width='60%'
      @close='close'>
      <el-form :model='form' ref='form' :rules='rules' label-width='180px' label-position='left' size='small'
               class='dialog-form'>
        <el-form-item class='item' label='权益类型名称：'>
          <el-input v-model='form.equityName'></el-input>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import {getToken} from '@/utils/auth'
import {insertEquityType} from '@/api/modules/member'

export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      form: {
        equityName: ''
      },
      rules: {
        equityName: [
          {required: true, message: '请输入权益类型名称', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          insertEquityType(this.form).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
            }, 500)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    open(a, b) {
      this.a = a
      this.b = b
      this.form = {
        equityName: ''
      }
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
      this.$emit('confirm', this.a, this.b)
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 20%;

  .item {
    width: 70%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.rg1 {
  width: 160px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg2 {
  width: 80px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg-content {
  display: flex;
  align-items: center;
  margin-top: 32px;
}
</style>
