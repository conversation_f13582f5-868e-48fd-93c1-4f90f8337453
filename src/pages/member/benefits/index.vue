<template>
  <div class='wrapper'>
    <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot='search'>
        <el-form :inline='true' :model='form'>
          <el-form-item label='权益名称' class='ml20'>
            <el-input v-model='form.equityName'></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot='bottom-options'>
        <el-button type='primary' size='mini' @click='handleInsert'>新增会员权益</el-button>
      </div>
      <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
        <div slot='options' slot-scope='scope'>
          <el-button type='text' size='mini' @click='handleEdit(scope.row)'>编辑</el-button>
          <el-button type='text' size='mini' @click='handleDel(scope.row)'>删除</el-button>
        </div>
      </simple-table>
    </simple-list-page>
    <insert-dialog ref='insert' @confirm='loadData' @insertType='handleInsertType' />
    <edit-dialog ref='edit' @confirm='loadData' @insertType='handleInsertType' />
    <type-dialog ref='type' @confirm='backTo' />
  </div>
</template>

<script>
import InsertDialog from './components/insert'
import EditDialog from './components/edit'
import TypeDialog from './components/type'
import {delEquity, delMemberPackage, pageEquity} from '@/api/modules/member'

export default {
  components: {InsertDialog, EditDialog, TypeDialog},
  data() {
    return {
      form: {equityName: null},
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      columns: [
        {
          label: '会员类型',
          prop: 'memberTypeName'
        },
        {
          label: '权益类型',
          prop: 'equityTypeName'
        },
        {
          label: '权益名称',
          prop: 'equityName'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        }
      ],
      list: []
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {
        memberMealName: null,
        memberType: null
      }
      this.handleQuery()
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageEquity(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleInsert() {
      this.$refs.insert.open()
    },
    handleEdit(row) {
      this.$refs.edit.open(row.id)
    },
    handleDel(row) {
      this.$confirm('确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        delEquity(row.id).then(res => {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.loadData()
        })
      }).catch(() => {
      })
    },
    handleInsertType(a, b) {
      this.$refs.type.open(a, b)
    },
    backTo(a, b) {
      if (b === 1) {
        this.$refs.insert.open(a)
      } else {
        this.$refs.edit.open(null, a)
      }
    }
  }
}
</script>

<style lang='scss' scoped></style>
