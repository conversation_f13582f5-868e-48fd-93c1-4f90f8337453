<template>
  <el-dialog
    :title="operator === 'add' ? '新增数据' : '编辑数据'"
    :visible.sync="visibleDialog"
    width="800px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="small"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="食品大类：" prop="foodBigCategory">
            <el-input
              v-model="form.foodBigCategory"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="食品亚类：" prop="foodSubCategory">
            <el-input
              v-model="form.foodSubCategory"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="食品次亚类：" prop="foodNextSubCategory">
            <el-input
              v-model="form.foodNextSubCategory"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="食品细类：" prop="foodDetailCategory">
            <el-input
              v-model="form.foodDetailCategory"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="抽样环节：" prop="samplingLink">
            <el-input
              v-model="form.samplingLink"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="型号规格：" prop="modelSpec">
            <el-input v-model="form.modelSpec" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最少抽样数量≥" prop="minSamplingNum">
            <el-input-number
              v-model="form.minSamplingNum"
              :min="0"
              controls-position="right"
              placeholder="请输入"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最少抽样重量>" prop="minSamplingWeight">
            <el-input
              v-model="form.minSamplingWeight"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最少备样重量>" prop="minReserveWeight">
            <el-input
              v-model="form.minReserveWeight"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="风险等级：" prop="riskLevel">
            <el-input v-model="form.riskLevel" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="存储条件：" prop="storageCondition">
            <el-input
              v-model="form.storageCondition"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="抽检项目：" prop="inspectionItems">
            <el-input
              v-model="form.inspectionItems"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="产品执行标准：" prop="productStandard">
            <el-input
              v-model="form.productStandard"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注：" prop="remark">
            <el-input v-model="form.remark" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="举例：" prop="example">
            <el-input v-model="form.example" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
/**
 * 数据维护 - 新增/编辑表单
 */
import {
  addFoodCategory,
  updateFoodCategory,
} from "@/api/modules/foodCategory";

export default {
  name: "DataForm",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    operator: {
      type: String,
      default: "add", // add-新增，edit-编辑
    },
    categoryOptions: {
      type: Array,
      default: () => [],
    },
    formData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      visibleDialog: this.visible,
      loading: false,
      form: {
        id: "",
        foodBigCategory: "", // 食品大类
        foodSubCategory: "", // 食品亚类
        foodNextSubCategory: "", // 食品次亚类
        foodDetailCategory: "", // 食品细类
        samplingLink: "", // 抽样环节
        modelSpec: "", // 型号规格
        minSamplingNum: 0, // 最少抽样数量
        minSamplingWeight: "", // 最少抽样重量
        minReserveWeight: "", // 最少备样重量
        riskLevel: "", // 风险等级
        storageCondition: "", // 存储条件
        inspectionItems: "", // 抽检项目
        productStandard: "", // 产品执行标准
        remark: "", // 备注
        example: "", // 举例
      },
      rules: {
        foodBigCategory: [
          { required: true, message: "请输入食品大类", trigger: "blur" },
        ],
        foodSubCategory: [
          { required: true, message: "请输入食品亚类", trigger: "blur" },
        ],
        foodNextSubCategory: [
          { required: true, message: "请输入食品次亚类", trigger: "blur" },
        ],
        foodDetailCategory: [
          { required: true, message: "请输入食品细类", trigger: "blur" },
        ],
        samplingLink: [
          { required: true, message: "请输入抽样环节", trigger: "blur" },
        ],
        minSamplingNum: [
          { required: true, message: "请输入最少抽样数量", trigger: "blur" },
        ],
        minSamplingWeight: [
          { required: true, message: "请输入最少抽样重量", trigger: "blur" },
        ],
        minReserveWeight: [
          { required: true, message: "请输入最少备样重量", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    visible(val) {
      this.visibleDialog = val;
    },
    visibleDialog(val) {
      this.$emit("update:visible", val);
    },
    formData: {
      handler(val) {
        if (val) {
          this.initFormData();
        } else {
          this.resetForm();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 初始化表单数据（编辑模式）
    initFormData() {
      if (!this.formData) return;

      this.form = {
        ...this.formData,
      };
    },

    // 重置表单
    resetForm() {
      this.form = {
        id: "",
        foodBigCategory: "", // 食品大类
        foodSubCategory: "", // 食品亚类
        foodNextSubCategory: "", // 食品次亚类
        foodDetailCategory: "", // 食品细类
        samplingLink: "", // 抽样环节
        modelSpec: "", // 型号规格
        minSamplingNum: 0, // 最少抽样数量
        minSamplingWeight: "", // 最少抽样重量
        minReserveWeight: "", // 最少备样重量
        riskLevel: "", // 风险等级
        storageCondition: "", // 存储条件
        inspectionItems: "", // 抽检项目
        productStandard: "", // 产品执行标准
        remark: "", // 备注
        example: "", // 举例
      };
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },

    // 提交表单
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;

        this.loading = true;

        // 构建提交的参数
        const params = { ...this.form };

        // 正常情况下应该调用API
        // const request = this.operator === 'add' ? addFoodCategory(params) : updateFoodCategory(params)
        // request.then(res => {
        //   this.$message.success(this.operator === 'add' ? '新增成功' : '编辑成功')
        //   this.$emit('success')
        // }).catch(error => {
        //   console.error(error)
        // }).finally(() => {
        //   this.loading = false
        // })

        // 使用mock数据
        setTimeout(() => {
          this.$message.success(
            this.operator === "add" ? "新增成功" : "编辑成功"
          );
          this.$emit("success", params);
          this.loading = false;
        }, 500);
      });
    },

    // 关闭弹窗
    handleClose() {
      this.visibleDialog = false;
      this.resetForm();
    },
  },
};
</script>

<style lang="scss" scoped>
.form-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: center;
}
</style>
