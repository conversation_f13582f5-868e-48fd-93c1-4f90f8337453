<template>
  <div class="data-maintenance-page">
    <el-card class="data-card">
      <div slot="header" class="data-header">
        <span class="data-title">筛选条件</span>
      </div>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form
          :inline="true"
          :model="form"
          class="demo-form-inline"
          size="small"
        >
          <el-form-item label="食品类别：" style="margin-right: 20px">
            <el-cascader
              v-model="form.foodCategory"
              :options="categoryOptions"
              :props="{
                expandTrigger: 'hover',
                checkStrictly: true,
                value: 'id',
                label: 'categoryName',
                children: 'children',
              }"
              placeholder="请选择"
              clearable
              style="width: 360px"
            ></el-cascader>
          </el-form-item>
          <el-form-item style="margin-right: 0">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleQuery"
              size="small"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="handleReset" size="small"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据详情 -->
      <div class="data-detail-section">
        <div class="data-title">数据维护</div>

        <!-- 操作按钮 -->
        <div class="operation-buttons">
          <el-button
            type="success"
            size="small"
            icon="el-icon-upload2"
            @click="showImportDialog"
            >导入</el-button
          >
          <el-button
            type="primary"
            size="small"
            icon="el-icon-download"
            @click="handleExport"
            :loading="exportLoading"
            >导出</el-button
          >
          <!-- <el-button
            type="info"
            size="small"
            icon="el-icon-document"
            @click="downloadTemplate"
            >下载模板</el-button
          > -->
        </div>

        <!-- 表格 -->
        <div class="table-container">
          <el-table
            v-loading="loading"
            :data="list"
            style="width: 100%; margin-bottom: 20px"
            :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
          >
            <el-table-column
              type="index"
              label="序号"
              width="80"
              align="center"
              fixed="left"
            ></el-table-column>
            <el-table-column
              prop="foodCategory"
              label="食品大类"
              align="center"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="foodSubcategory"
              label="食品亚类"
              align="center"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="foodVariety"
              label="食品次亚类"
              align="center"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="foodDetail"
              label="食品细类"
              align="center"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="specialCategory"
              label="特殊类别"
              align="center"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="samplingLink"
              label="抽样环节"
              align="center"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="specification"
              label="型号规格"
              align="center"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="minSampleQuantity"
              label="最少抽样数量≥"
              align="center"
              width="140"
            ></el-table-column>
            <el-table-column
              prop="minSampleWeight"
              label="最少抽样重量>"
              align="center"
              width="140"
            ></el-table-column>
            <el-table-column
              prop="minBackupWeight"
              label="最少备样重量>"
              align="center"
              width="140"
            ></el-table-column>
            <el-table-column
              prop="samplingMethod"
              label="抽样方法及数量"
              align="center"
              width="450"
            ></el-table-column>
            <el-table-column
              prop="riskLevel"
              label="风险等级"
              align="center"
              width="100"
            ></el-table-column>
            <el-table-column
              prop="storageCondition"
              label="存储条件"
              align="center"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="executionStandards"
              label="产品执行标准"
              align="center"
              width="150"
            ></el-table-column>
            <el-table-column
              prop="project"
              label="抽检项目"
              align="center"
              width="150"
            ></el-table-column>
            <el-table-column
              prop="executionStandards"
              label="产品执行标准"
              align="center"
              width="600"
            ></el-table-column>
            <el-table-column
              prop="remarks"
              label="备注信息"
              align="center"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="commonConfusion"
              label="常见易混"
              align="center"
              width="320"
            ></el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <pagination
          v-show="page.total > 0"
          :total="+page.total"
          :page.sync="page.current"
          :limit.sync="page.size"
          @pagination="handlePage"
        />
      </div>
    </el-card>

    <!-- 引入新增/编辑弹窗组件 -->
    <data-form
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :operator="operatorType"
      :categoryOptions="categoryOptions"
      :formData="currentRow"
      @success="handleSuccess"
    ></data-form>

    <!-- 导入Excel弹窗 -->
    <el-dialog
      title="导入数据"
      :visible.sync="importDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="import-dialog-content">
        <el-upload
          class="upload-excel"
          drag
          action="#"
          :http-request="handleUpload"
          :before-upload="beforeUpload"
          :show-file-list="true"
          :limit="1"
          :file-list="fileList"
          :on-exceed="handleExceed"
          :on-remove="handleRemove"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            <div style="text-align: center; margin-bottom: 8px">
              <el-link type="primary" @click="handleExport"
                >下载导入模板</el-link
              >
            </div>
            只能上传Excel文件，且不超过10MB
          </div>
        </el-upload>

        <div class="import-notice">
          <div class="notice-title">注意：</div>
          <ol class="notice-list">
            <li>请使用模板导入。</li>
            <li>导入数据会删除线上所有数据，以现在上传的Excel的数据为准。</li>
            <li>
              注意Excel文字中包特殊隐藏符号类空格、空格、/t
              、/n，会导致数据无法筛选。
            </li>
          </ol>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitImport" :loading="importLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
/**
 * 数据维护模块
 */
import Pagination from "@/components/Pagination";
import DataForm from "./components/DataForm.vue";
import {
  getFoodCategoryList,
  deleteFoodCategory,
  getFoodCategoryCascade,
  getFoodCategoryListNew,
  exportFoodCategoryData,
} from "@/api/modules/foodCategory";
import { http } from "@/api/request";

export default {
  name: "DataMaintenance",
  components: { Pagination, DataForm },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      operatorType: "add", // add-新增 edit-编辑
      form: {
        foodCategory: [],
      },
      list: [], // 表格数据
      page: {
        total: 0,
        current: 1,
        size: 10,
      },
      categoryOptions: [], // 类别级联选项
      currentRow: null, // 当前选中行数据
      importDialogVisible: false,
      importLoading: false,
      exportLoading: false, // 添加导出加载状态
      fileList: [],
      uploadFile: null,
      // mock数据 - 将在实际API接入后移除
      mockCategoryOptions: [
        {
          id: 1001,
          categoryName: "粮食加工品",
          children: [
            {
              id: 1002,
              categoryName: "小麦粉",
              children: [
                {
                  id: 1003,
                  categoryName: "小麦粉",
                  children: [
                    {
                      id: 1004,
                      categoryName: "小麦粉",
                    },
                  ],
                },
              ],
            },
            {
              id: 1005,
              categoryName: "大米",
              children: [
                {
                  id: 1006,
                  categoryName: "大米",
                  children: [
                    {
                      id: 1007,
                      categoryName: "大米",
                    },
                  ],
                },
              ],
            },
            {
              id: 1008,
              categoryName: "挂面",
              children: [
                {
                  id: 1009,
                  categoryName: "挂面",
                  children: [
                    {
                      id: 1010,
                      categoryName: "挂面",
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: 2001,
          categoryName: "食用油、油脂及其制品",
          children: [
            {
              id: 2002,
              categoryName: "食用植物油",
              children: [
                {
                  id: 2003,
                  categoryName: "花生油",
                  children: [
                    {
                      id: 2004,
                      categoryName: "一级花生油",
                    },
                    {
                      id: 2005,
                      categoryName: "二级花生油",
                    },
                  ],
                },
                {
                  id: 2006,
                  categoryName: "大豆油",
                  children: [
                    {
                      id: 2007,
                      categoryName: "一级大豆油",
                    },
                    {
                      id: 2008,
                      categoryName: "二级大豆油",
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    };
  },
  mounted() {
    this.loadCategoryOptions();
    this.loadData();
  },
  methods: {
    // 加载级联选择器数据
    loadCategoryOptions() {
      // 正常情况下应该调用API获取数据，但目前使用mock数据
      // getFoodCategoryCascade().then(res => {
      //   this.categoryOptions = res.data || []
      // })

      // 使用mock数据
      this.categoryOptions = this.mockCategoryOptions;
    },

    // 加载表格数据
    loadData() {
      this.loading = true;

      const params = {
        pageNum: this.page.current,
        pageSize: this.page.size,
      };

      // 处理筛选条件
      if (this.form.foodCategory && this.form.foodCategory.length) {
        // 根据级联深度决定使用哪个参数
        const level = this.form.foodCategory.length;
        const lastCategoryId = this.form.foodCategory[level - 1];
        const lastCategory = this.findCategoryName(
          this.categoryOptions,
          lastCategoryId
        );

        if (level === 1) {
          params.foodCategory = lastCategory;
        } else if (level === 2) {
          params.foodSubcategory = lastCategory;
        } else if (level === 3) {
          params.foodVariety = lastCategory;
        } else if (level === 4) {
          params.foodDetail = lastCategory;
        }
      }

      getFoodCategoryListNew(params)
        .then((res) => {
          if (res.code === 200 && res.success) {
            this.list = res.data.records || [];
            this.page.total = res.data.total || 0;
          } else {
            this.$message.error(res.msg || "获取数据失败");
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 根据ID查找类别名称
    findCategoryName(options, id) {
      for (const option of options) {
        if (option.id === id) {
          return option.categoryName;
        }
        if (option.children && option.children.length) {
          const found = this.findCategoryName(option.children, id);
          if (found) return found;
        }
      }
      return null;
    },

    // 分页变化
    handlePage() {
      this.loadData();
    },

    // 查询
    handleQuery() {
      this.page.current = 1;
      this.loadData();
    },

    // 重置
    handleReset() {
      this.form = {
        foodCategory: [],
      };
      this.handleQuery();
    },

    // 新增
    handleAdd() {
      this.operatorType = "add";
      this.currentRow = null;
      this.dialogVisible = true;
    },

    // 编辑
    handleEdit(row) {
      this.operatorType = "edit";
      this.currentRow = JSON.parse(JSON.stringify(row));
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm("确定要删除此条数据吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 正常情况下应调用API
          // deleteFoodCategory({ id: row.id }).then(res => {
          //   this.$message.success('删除成功')
          //   this.loadData()
          // })

          // mock删除
          this.mockData = this.mockData.filter((item) => item.id !== row.id);
          this.$message.success("删除成功");
          this.loadData();
        })
        .catch(() => {});
    },

    // 新增/编辑成功回调
    handleSuccess(data) {
      this.dialogVisible = false;

      if (this.operatorType === "add") {
        // 生成一个新的ID
        const newId = Math.max(...this.mockData.map((item) => item.id)) + 1;
        this.mockData.push({
          ...data,
          id: newId,
        });
      } else {
        // 编辑
        const index = this.mockData.findIndex((item) => item.id === data.id);
        if (index !== -1) {
          this.mockData.splice(index, 1, data);
        }
      }

      this.loadData();
    },

    // 显示导入对话框
    showImportDialog() {
      this.importDialogVisible = true;
      this.fileList = [];
      this.uploadFile = null;
    },

    // 下载导入模板
    downloadTemplate() {
      this.$message.info("正在准备下载模板...");

      // 调用接口下载模板
      const url =
        process.env.VUE_APP_BASE_API + "/foodCategory/template/download";

      const link = document.createElement("a");
      link.href = url;
      link.download = "食品类别导入模板.xlsx";
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.$message.success("模板下载成功");
    },

    // 处理导出功能
    handleExport() {
      if (this.exportLoading) return; // 防止重复点击
      this.exportLoading = true;
      this.$message.info("正在下载模版...");

      // 调用不带任何参数的导出接口
      exportFoodCategoryData()
        .then((res) => {
          // 兼容 axios blob 响应
          let blob = res;
          let fileName = "食品类别数据.xlsx";

          // 兼容 axios 直接返回 response.data 的情况
          if (res instanceof Blob) {
            blob = res;
          } else if (res && res.data) {
            blob = res.data;
          }

          // 尝试从响应头获取文件名
          if (res && res.headers && res.headers["content-disposition"]) {
            const match =
              res.headers["content-disposition"].match(/filename="(.+)"/);
            if (match && match[1]) {
              fileName = decodeURIComponent(match[1]);
            }
          }

          // 创建下载链接
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = fileName;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(link.href);
          this.$message.success("数据导出成功");
        })
        .catch((err) => {
          console.error("导出失败:", err);
          // 处理不同类型的错误
          if (err.response) {
            this.$message.error(
              `导出失败: ${err.response.status} ${err.response.statusText}`
            );
          } else if (err.request) {
            this.$message.error("导出失败: 服务器无响应，请检查网络连接");
          } else {
            this.$message.error(`导出失败: ${err.message}`);
          }
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },

    // 处理上传前的验证
    beforeUpload(file) {
      const isExcel =
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel";
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message.error("上传文件只能是Excel格式!");
        return false;
      }

      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过10MB!");
        return false;
      }

      this.uploadFile = file;
      return true;
    },

    // 自定义上传
    handleUpload(options) {
      this.uploadFile = options.file;
      // 这里不进行实际上传，只保存文件对象，提交时再处理
    },

    // 处理超过限制
    handleExceed(files, fileList) {
      this.$message.warning(
        `最多只能上传1个文件，已选择了 ${fileList.length} 个文件`
      );
    },

    // 处理移除
    handleRemove(file) {
      this.uploadFile = null;
      this.fileList = [];
    },

    // 提交导入
    submitImport() {
      if (!this.uploadFile) {
        this.$message.warning("请先选择要上传的Excel文件");
        return;
      }

      this.importLoading = true;

      // 调用API上传文件
      const formData = new FormData();
      formData.append("file", this.uploadFile);

      // 导入接口
      http
        .post("/foodCategory/import", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((res) => {
          if (res.code === 200) {
            this.$message.success(res.msg || "导入成功");
            this.importDialogVisible = false;
            this.loadData(); // 重新加载数据
          } else {
            this.$message.error(res.msg || "导入失败");
          }
        })
        .catch((err) => {
          this.$message.error("导入失败：" + err.message);
        })
        .finally(() => {
          this.importLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.data-maintenance-page {
  padding: 10px 0;
  background-color: #f0f2f5;
  min-height: calc(100vh - 120px);

  .data-card {
    margin-bottom: 20px;

    .data-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .data-title {
        font-size: 18px;
        font-weight: bold;
      }
    }

    .filter-section {
      margin-bottom: 20px;
    }

    .data-detail-section {
      .data-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
        border-left: 4px solid #409eff;
        padding-left: 10px;
      }

      .operation-buttons {
        margin-bottom: 15px;
      }

      .table-container {
        width: 100%;
        overflow-x: auto;
      }
    }
  }
}

.action-container {
  white-space: nowrap;
  display: flex;
  justify-content: center;
}

.action-link {
  margin-left: 15px;
}

.import-dialog-content {
  padding: 10px 0;
  text-align: center;

  .import-tips {
    margin-bottom: 20px;

    p {
      margin: 5px 0;
      font-size: 14px;
      color: #606266;
    }
  }

  .upload-excel {
    width: 100%;

    .el-upload {
      width: 100%;

      .el-upload-dragger {
        width: 100%;
        height: 160px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        i {
          font-size: 48px;
          color: #c0c4cc;
          margin-bottom: 10px;
        }

        .el-upload__text {
          font-size: 14px;
          color: #606266;

          em {
            color: #409eff;
            font-style: normal;
          }
        }
      }
    }

    .el-upload__tip {
      margin-top: 10px;
      font-size: 12px;
      color: #909399;
    }
  }
}

.dialog-footer {
  text-align: right;
}

.import-notice {
  margin-top: 20px;
  padding: 0 50px;
  text-align: left;

  .notice-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .notice-list {
    font-size: 13px;
    list-style-type: decimal;
    padding-left: 20px;
    margin: 0;

    li {
      margin-bottom: 4px;
    }
  }
}
</style>
