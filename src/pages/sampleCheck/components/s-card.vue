<template>
  <div
    class="s-card"
    :style="{ width: width, display: inline ? 'inline-block' : 'block' }"
  >
    <header v-if="$slots.operation || title" @click="toggleExpand">
      <div class="title">{{ title }}</div>
      <div>
        <slot name="operation"></slot>
      </div>
      <div v-if="expand">
        <i v-if="isFold" class="el-icon-arrow-right"></i>
        <i v-else class="el-icon-arrow-down"></i>
      </div>
    </header>
    <!-- 搜索 -->
    <el-row ref="content" class="content">
      <slot></slot>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      // card头部标题
      type: String,
      default: "",
    },
    width: {
      //宽度
      type: String,
      default: "100%",
    },
    inline: {
      //是否以行内形式显示
      type: Boolean,
      default: false,
    },
    expand: {
      //是否折叠
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isFold: false,
    };
  },
  mounted() {},
  methods: {
    //折叠展开面板
    toggleExpand() {
      if (!this.expand) {
        return;
      }
      if (this.isFold) {
        this.$refs.content.$el.style.height = "auto";
        this.$refs.content.$el.style.padding = "5px 12px";
      } else {
        this.$refs.content.$el.style.height = 0;
        this.$refs.content.$el.style.padding = 0;
      }
      this.isFold = !this.isFold;
    },
  },
};
</script>

<style scoped>
.s-card {
  width: 100%;
  border: 1px solid #DEE2E6;
  background: #fff;
  border-radius: 5px;
  margin-bottom: 10px; /* Please don't add margin and padding, it will cause height calculation error, and scrollbar will appear */
}

.s-card > header {
  display: flex;
  padding: 10px 0;
  padding-right: 1em;
  border-bottom: 1px solid #ECF5FF;
  justify-content: space-between;
  align-items: center;
  height: 40px;
}

.s-card > header:hover {
  background: #F8F9FA;
  cursor: pointer;
}

.s-card > header.active {
  cursor: pointer;
  transition: background-color 0.3s;
}

.s-card > header.active:hover {
  background: #F8F9FA;
}

.s-card > header .title {
  padding-left: 1.5em;
  color: #369;
  font-weight: bolder;
  border-left: 4px solid #409eff;
}

.s-card > .content {
  position: relative;
  padding: 5px 12px;
  overflow: hidden;
}

.s-card > .content.active {
  padding: 0rem !important;
  height: 0px !important;
}
</style>
