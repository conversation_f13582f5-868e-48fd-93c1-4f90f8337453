/* 年度/季度/自定义配置批次组件 */
<template>
  <el-row class="s-annual-quarterlybatch">
    <el-checkbox-group v-model="checkboxArr" @change="handleCheckbox">
      <el-row class="checkbox-group">
        <el-col :span="24">
          <el-checkbox label="全年">
            <div>
              <span>全年（当前年份）超过&nbsp;</span>
              <el-input
                size="small"
                v-model="formInfo.fullYear.max"
                class="input-no-border"
              ></el-input>
              <span>&nbsp;批次，系统给出提示；</span>
            </div>
          </el-checkbox>
        </el-col>
        <el-col :span="24">
          <el-checkbox label="季度">
            <div>
              <span>季度&nbsp;</span>
              <el-select v-model="formInfo.quarter.type" size="mini">
                <el-option
                  v-for="item in optionsQuarter"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
              <span>&nbsp;超过</span>
              <el-input
                size="small"
                v-model="formInfo.quarter.max"
                class="input-no-border"
              ></el-input>
              <span>批次，系统给出提示；</span>
            </div>
          </el-checkbox>
        </el-col>
        <el-col :span="24">
          <el-checkbox label="自定义">
            <div>
              <span>自定义&nbsp;</span>
              <el-date-picker
                v-model="formInfo.custom.range"
                class="date-picker"
                size="mini"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
              <span>&nbsp;超过</span>
              <el-input
                size="small"
                v-model="formInfo.custom.max"
                class="input-no-border"
              ></el-input>
              <span>批次，系统给出提示；</span>
            </div>
          </el-checkbox>
        </el-col>
      </el-row>
    </el-checkbox-group>
  </el-row>
</template>

<script>
import dayjs from "dayjs"; // 引入 dayjs 库

export default {
  props: {
    dataInfo: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      model: "",
      optionsQuarter: ["当前季度", "一季度", "二季度", "三季度", "四季度"],
      checkboxArr: [],
      formInfo: {},
      resultData: [],
    };
  },
  watch: {
    checkboxArr: {
      handler(val) {
        this.updateResultData()
      },
      deep: true,
    },
    formInfo: {
      handler(val) {
        this.updateResultData();
      },
      deep: true,
    },
    dataInfo: {
      handler(val) {
        this.init();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 初始化选中
    init() {
      this.checkboxArr = []; // 初始化前清空
      if (this.dataInfo.length > 0) {
        // 初始化数据回显
        this.dataInfo.forEach((item) => {
          if (item.type === "全年") {
            this.checkboxArr.push("全年");
            this.formInfo.fullYear.max = item.max;
          } else if (item.type?.includes("季度")) {
            this.checkboxArr.push("季度");
            this.formInfo.quarter.type = item.type;
            this.formInfo.quarter.max = item.max;
          } else if (item.type === "自定义") {
            this.checkboxArr.push("自定义");
            this.formInfo.custom.range = [
              new Date(item.start),
              new Date(item.end),
            ];
            this.formInfo.custom.max = item.max;
          }
        });
      } else {
        this.formInfo = {
          fullYear: { max: "" },
          quarter: { type: "当前季度", max: "" },
          custom: { range: [], max: "" },
        };
        this.checkboxArr = ["全年"];
      }
    },
    // 表单赋值
    updateResultData() {
      this.resultData = [];
      if (this.checkboxArr.includes("全年")) {
        const startOfYear = dayjs().startOf("year").format("YYYY-MM-DD");
        const endOfYear = dayjs().endOf("year").format("YYYY-MM-DD");
        this.resultData.push({
          type: "全年",
          start: startOfYear,
          end: endOfYear,
          max: this.formInfo.fullYear.max || "",
        });
      }
      if (this.checkboxArr.includes("季度")) {
        let start, end;
        const currentDate = new Date();
        const year = currentDate.getFullYear();

        switch (this.formInfo.quarter.type) {
          case "一季度":
            start = `${year}-01-01`;
            end = `${year}-03-31`;
            break;
          case "二季度":
            start = `${year}-04-01`;
            end = `${year}-06-30`;
            break;
          case "三季度":
            start = `${year}-07-01`;
            end = `${year}-09-30`;
            break;
          case "四季度":
            start = `${year}-10-01`;
            end = `${year}-12-31`;
            break;
          default:
            start = this.getCurrentQuarter().start;
            end = this.getCurrentQuarter().end;
            break;
        }

        this.resultData.push({
          type: this.formInfo.quarter.type,
          start,
          end,
          max: this.formInfo.quarter.max,
        });
      }
      if (this.checkboxArr.includes("自定义")) {
        let [start, end] = [];
        if (
          this.formInfo.custom.range &&
          this.formInfo.custom.range.length === 2
        ) {
          [start, end] = this.formInfo.custom.range;
        }

        this.resultData.push({
          type: "自定义",
          start: start ? dayjs(start).format("YYYY-MM-DD") : "", // 使用 dayjs 格式化开始日期
          end: end ? dayjs(end).format("YYYY-MM-DD") : "", // 使用 dayjs 格式化结束日期
          max: this.formInfo.custom.max,
        });
      }
      this.$emit("updateResult", this.resultData);
    },
    // 校验已选中的必填
    validateSelection() {
      if (!this.resultData.length) {
        this.$message.error("请至少选择一个时间范围");
        return false;
      }
      for (const item of this.resultData) {
        if (item.type === "全年" && !item.max) {
          this.$message.error("请填写时间范围为全年的批次阈值");
          return false;
        } else if (item.type.includes("季度") && !item.max) {
          this.$message.error("请填写时间范围为季度的批次阈值");
          return false;
        } else if (
          item.type === "自定义" &&
          (!item.max || !item.start || !item.end)
        ) {
          this.$message.error("请填写时间范围为自定义的日期或者批次阈值");
          return false;
        }
      }
      return true;
    },
    // 复选框选择重置
    handleCheckbox(e) {
      if (e.length === 0) {
        this.$message.warning("请至少选择一个时间范围");
      }
      const checkboxArr = e;
      if (!checkboxArr.includes("全年")) {
        this.formInfo.fullYear.max = "";
      }
      if (!checkboxArr.includes("季度")) {
        this.formInfo.quarter.type = "当前季度";
        this.formInfo.quarter.max = "";
      }
      if (!checkboxArr.includes("自定义")) {
        this.formInfo.custom.range = [];
        this.formInfo.custom.max = "";
      }
    },
    // 获取当前所在季度的第一天和最后一天
    getCurrentQuarter() {
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1; // 月份从0开始，所以需要加1

      let startMonth, endMonth;
      if (month >= 1 && month <= 3) {
        startMonth = 1;
        endMonth = 3;
      } else if (month >= 4 && month <= 6) {
        startMonth = 4;
        endMonth = 6;
      } else if (month >= 7 && month <= 9) {
        startMonth = 7;
        endMonth = 9;
      } else {
        startMonth = 10;
        endMonth = 12;
      }

      const start = new Date(year, startMonth - 1, 1); // 月份从0开始，所以需要减1
      const end = new Date(year, endMonth, 0); // 月份从0开始，所以不需要减1，直接用下个月的第0天表示本月最后一天

      return {
        start: dayjs(start).format("YYYY-MM-DD"),
        end: dayjs(end).format("YYYY-MM-DD"),
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.s-annual-quarterlybatch {
  height: auto;
}
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}
::v-deep .el-date-editor.el-input__inner {
  width: 300px;
}
/* 输入框无左-右-上边框 */
::v-deep .input-no-border {
  width: 100px;
  .el-input__inner {
    border-top: none;
    border-left: none;
    border-right: none;
    border-radius: 0;
    padding: 5px;
    text-align: center;
  }
}
::v-deep span.el-range-separator {
  width: 25px !important;
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #606266;
}
</style>
