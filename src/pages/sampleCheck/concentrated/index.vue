/* 重复抽样 */
<template>
  <el-card>
    <div slot="header" class="clearfix">
      <span>筛选条件</span>
    </div>
    <!-- 搜索条件 -->
    <div class="filter-options">
      <el-form
        ref="searchForm"
        :inline="true"
        v-model="searchForm"
        size="small"
      >
        <el-form-item label="校验类型：">
          <el-select v-model="searchForm.type" clearable>
            <el-option
              label="同任务下集中抽样校验"
              value="同任务下集中抽样校验"
            ></el-option>
            <el-option
              label="不同任务下抽样总量校验"
              value="不同任务下抽样总量校验"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="抽样地点：" type="custom">
          <div class="d-flex">
            <el-form-item prop="cyhj">
              <el-select
                v-model="searchForm.cyhj"
                placeholder="请选择抽样环节"
                class="w-150"
                @change="handleLinks"
                clearable
              >
                <el-option
                  v-for="item in links"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="cydd">
              <el-select
                :disabled="!searchForm.cyhj"
                v-model="searchForm.cydd"
                placeholder="请选择抽样地点"
                class="w-150"
                clearable
              >
                <el-option
                  v-for="item in placesData"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="食品分类：" prop="spfl">
          <el-select
            v-model="searchForm.spfl"
            placeholder="请选择食品分类"
            class="w-150"
            clearable
          >
            <el-option
              v-for="item in classification"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="loadData(searchForm)"
            :loading="loading"
            >查询</el-button
          >
          <el-button @click="handleReset">重置</el-button>
          <el-dropdown @command="handleType">
            <el-button class="ml-10" type="success"
              >生成校验规则<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1"
                >同任务下集中抽样校验</el-dropdown-item
              >
              <el-dropdown-item :command="2"
                >不同任务下抽样总量校验</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格展示 -->
    <el-table ref="table" :data="tableData" class="mt-20" v-loading="loading">
      <el-table-column
        prop="type"
        label="校验类型"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="checkDescription"
        label="校验说明"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="logic"
        label="校验逻辑"
        align="center"
        width="280"
      ></el-table-column>
      <el-table-column
        prop="source"
        label="是否区分任务来源"
        align="center"
        width="110"
      >
        <template slot-scope="{ row }">
          {{ row.source ? row.source : "/" }}
        </template>
      </el-table-column>
      <el-table-column prop="cydd" label="抽样地点" align="center">
        <template slot-scope="{ row }">
          {{ row.cydd ? row.cydd : "/" }}
        </template>
      </el-table-column>
      <el-table-column prop="spfl" label="食品分类" align="center">
        <template slot-scope="{ row }">
          {{ mapClassification(row.spfl) }}
        </template>
      </el-table-column>
      <el-table-column prop="range" label="数据范围" align="center">
      </el-table-column>
      <el-table-column
        prop="thresholdValue"
        label="阈值"
        align="center"
      ></el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template slot-scope="{ row }">
          <el-tag v-show="row.status === 1" type="success">启用</el-tag>
          <el-tag v-show="row.status === 0" type="danger">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="140px" fixed="right">
        <template slot-scope="{ row }">
          <el-button
            v-show="row.status === 0"
            type="text"
            @click="handleStart(row)"
            >启用</el-button
          >
          <el-button
            v-show="row.status === 1"
            type="text"
            @click="handleStop(row)"
            >停用</el-button
          >
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" class="red" @click="handleDelete(row)"
            >删除</el-button
          >
          <el-button type="text" disabled @click="handleEdit(row)"
            >查看已抽样情况</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
      ></el-pagination>
    </div>

    <!-- 同任务编辑 -->
    <s-same-batch
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :ruleId="ruleId"
      @finsh="loadData"
    >
    </s-same-batch>

    <!-- 不同任务编辑 -->
    <s-same-enterprises
      v-if="dialogVisible2"
      :visible.sync="dialogVisible2"
      :ruleId="ruleId"
      @finsh="loadData"
    >
    </s-same-enterprises>
  </el-card>
</template>

<script>
import SameBatch from "./component/SameBatch/index.vue";
import SameEnterprises from "./component/SameEnterprises/index.vue";
import PlaceArrJson from "./component/SameEnterprises/data.json";
import {
  concentratedList,
  concentratedDelete,
  concentratedStart,
  concentratedStop,
} from "@/api/modules/sampleCheck.js";

export default {
  components: {
    "s-same-batch": SameBatch,
    "s-same-enterprises": SameEnterprises,
  },
  data() {
    return {
      tableData: [],
      searchForm: {
        cyhj: null,
        cydd: null,
      },
      dialogVisible: false, // 同批次样品校验 新增/编辑
      dialogVisible2: false, // 同企业总量校验 新增/编辑
      ruleId: "", //规则ID
      classification: [
        { id: 1, name: "食品大类" },
        { id: 2, name: "食品亚类" },
        { id: 3, name: "食品次亚类" },
        { id: 4, name: "食品细类" },
      ],
      links: [
        { id: 1, name: "生产" },
        { id: 2, name: "流通" },
        { id: 3, name: "餐饮" },
      ],
      placesData: [], // 抽样地点枚举值
      page: {
        total: 0,
        current: 1,
        size: 10,
      },
      loading: false,
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    //=====================================获取远程数据==================================//
    // 初始表格数据
    loadData() {
      this.loading = true;
      const params = {
        ...this.searchForm,
        current: this.page.current,
        size: this.page.size,
      };
      concentratedList(params)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data.records;
            this.page.total = res.data.total ? parseFloat(res.data.total) : 0;
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          console.error("获取列表失败:", err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 分页
    handleSizeChange(val) {
      this.page.size = val;
      this.loadData();
    },
    handleCurrentChange(val) {
      this.page.current = val;
      this.loadData();
    },
    // 重置
    handleReset() {
      this.searchForm = {};
      this.loadData();
    },
    //=====================================其他操作=====================================//
    // 新增弹窗 1-同批次样品校验     2-同企业总量校验
    handleType(command) {
      if (command === 1) {
        this.dialogVisible = true;
      } else {
        this.dialogVisible2 = true;
      }
      this.ruleId = "";
    },
    // 编辑规则
    handleEdit(row) {
      this.ruleId = row.id;
      if (row.type === "同任务下集中抽样校验") {
        this.dialogVisible = true;
      } else {
        this.dialogVisible2 = true;
      }
    },
    // 删除规则
    handleDelete(row) {
      this.$confirm("此操作将删除该规则，是否继续?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          concentratedDelete(row.id)
            .then(() => {
              this.loadData();
            })
            .catch((err) => {
              console.error(err);
            });
        })
        .catch(() => {});
    },
    // 启用
    handleStart(row) {
      concentratedStart(row.id)
        .then(() => {
          this.loadData();
        })
        .catch((err) => {
          console.error(err);
        });
    },
    // 停用
    handleStop(row) {
      this.$confirm("此操作将停用该规则，是否继续?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        concentratedStop(row.id)
          .then(() => {
            this.loadData();
          })
          .catch((err) => {
            console.error(err);
          });
      });
    },
    // 分类映射
    mapClassification(val) {
      switch (val) {
        case 1:
          return "食品大类";
        case 2:
          return "食品亚类";
        case 3:
          return "食品次亚类";
        case 4:
          return "食品细类";
        default:
          return "/";
      }
    },
    // 抽样环节改变重置抽样地点
    handleLinks(val) {
      if (val) {
        this.placesData = PlaceArrJson[val];
      } else {
        this.placesData = [];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-form-item {
  margin-bottom: 0 !important;
}
.ml-10 {
  margin-left: 10px;
}
.mt-20 {
  margin-top: 20px;
}
.w-150 {
  width: 150px;
}
.d-flex {
  display: flex;
  align-items: flex-start;
  transform: translate(0, -5px);
}
.el-form-item {
  margin-top: 5px;
}
::v-deep .el-radio__input.is-checked .el-radio__inner {
  border-color: #409eff;
  background: #409eff;
}
::v-deep .el-radio__input.is-checked + .el-radio__label {
  color: #409eff;
}
</style>
