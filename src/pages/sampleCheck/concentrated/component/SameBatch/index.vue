/* 同任务下集中抽样校验规则设定 */
<template>
  <el-dialog
    title="同任务下集中抽样校验规则设定"
    :visible.sync="visible"
    width="70%"
    @close="handleClose"
    top="2vh"
  >
    <el-form ref="form" v-model="formInfo" inline>
      <s-card title="基本信息">
        <el-col :span="20">
          <el-form-item label="校验类型：" prop="type">
            {{ formInfo.type }}
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="校验说明：" prop="name">
            {{ formInfo.checkDescription }}
          </el-form-item>
        </el-col>
      </s-card>
      <s-card title="请选择指定任务来源校验（任务来源即任务下发机构）">
        <div class="card-container">
          <el-radio-group v-model="formInfo.source">
            <el-radio label="否">否</el-radio>
            <el-radio label="是">是</el-radio>
            <s-card class="mt20" v-if="formInfo.source === '是'">
              <el-form-item label="任务来源：">
                <el-input
                  class="w-[60px] ml-10"
                  v-model="formInfo.sourceValue"
                  size="mini"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </s-card>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请选择时间字段">
        <div class="card-container">
          <el-radio-group v-model="formInfo.timeField">
            <el-radio label="抽样时间">抽样时间</el-radio>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请选择校验对象">
        <div class="card-container">
          <el-radio-group v-model="formInfo.target">
            <el-radio label="单家被抽样单位">单家被抽样单位</el-radio>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请选择时间范围及阈值（可多选）" class="mt10">
        <s-annual-quarterly-batch
          @updateResult="handleDataUpdate"
          ref="batchComponent"
          :dataInfo="initBatchData"
        ></s-annual-quarterly-batch>
      </s-card>
      <s-card title="请选择数据范围">
        <div class="card-container">
          <el-radio-group v-model="formInfo.range" @change="handleRange">
            <el-radio label="本机构数据">本机构数据</el-radio>
            <el-radio label="全国">全国</el-radio>
            <el-radio label="机构所在省份">机构所在省份</el-radio>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="校验逻辑">
        <div class="card-container">
          <p>{{ formInfo.logic }}</p>
        </div>
      </s-card>
      <s-card title="规则状态">
        <el-form-item label="机构状态：" prop="status">
          <el-radio-group v-model="formInfo.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </s-card>
    </el-form>
    <div slot="footer">
      <el-button
        :loading="loading"
        :disabled="loading"
        size="small"
        type="primary"
        @click="handleSubmit"
      >
        确定
      </el-button>
      <el-button size="small" type="warning" @click="handleClose">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import AnnualQuarterlyBatch from "@/pages/sampleCheck/components/AnnualQuarterlyBatch.vue";
import SCard from "@/pages/sampleCheck/components/s-card.vue";
import {
  concentratedAdd,
  concentratedUpdate,
  concentratedDetail,
} from "@/api/modules/sampleCheck.js";
export default {
  components: {
    "s-annual-quarterly-batch": AnnualQuarterlyBatch,
    "s-card": SCard,
  },
  props: {
    visible: {
      //弹窗是否显示
      type: Boolean,
      default: false,
    },
    ruleId: {
      //规则id
      type: String,
      default: "",
    },
  },
  data() {
    return {
      //=================================表单与表格参数================================//
      formInfo: {
        type: "同任务下集中抽样校验",
        checkDescription: "同任务来源，相同被抽样单位集中抽样校验",
        source: "否",
        sourceValue: "",
        timeField: "抽样时间",
        target: "单家被抽样单位",
        range: "本机构数据",
        time: "",
        logic: "",
        status: 1,
      },
      //===================================枚举参数====================================//
      //===================================业务参数====================================//
      //===================================其他参数====================================//
      loading: false, //新增按钮加载效果
      batchData: [], // 时间范围
      initBatchData: [], // 初始化时间范围
    };
  },
  watch: {
    formInfo: {
      handler: function (val, oldVal) {
        this.formInfo.logic = this.generationRules();
      },
      deep: true,
    },
    batchData: {
      handler: function (val, oldVal) {
        this.$set(this.formInfo, "logic", this.generationRules());
      },
      deep: true,
    },
  },
  mounted() {
    if (this.ruleId) {
      this.getFormInfo();
    }
  },
  methods: {
    //=====================================初始数据====================================//
    // 查询详情
    getFormInfo() {
      concentratedDetail(this.ruleId)
        .then((res) => {
          let tempObj = res.data;
          if (tempObj.source !== "否") {
            tempObj = {
              ...tempObj,
              sourceValue: tempObj.source,
              source: "是",
            };
          }
          this.formInfo = tempObj;
          this.initBatchData = JSON.parse(res.data.time);
        })
        .catch((err) => {});
    },
    //=====================================组件间交互====================================//
    // 提交
    handleSubmit() {
      /* 校验 */
      // 任务来源
      if (this.formInfo.source === "是" && !this.formInfo.sourceValue) {
        return this.$message.error(
          "当前选择了区分任务来源校验，任务来源必须填写"
        );
      }
      // 校验时间范围组件
      if (!this.$refs.batchComponent.validateSelection()) {
        return;
      }
      let params = {
        ...this.formInfo,
        status: this.formInfo.status,
        time: JSON.stringify(this.batchData),
      };
      // 处理任务来源
      if (params.source === "是") {
        params.source = params.sourceValue;
      }
      delete params.sourceValue;
      // console.log("请求参数", params);
      // return;
      this.loading = true;
      if (this.ruleId) {
        this.handleUpdate(params);
      } else {
        this.handleAdd(params);
      }
    },
    // 新增
    handleAdd(params) {
      concentratedAdd(params)
        .then((res) => {
          if (res.code === 200) {
            this.handleClose();
            this.$emit("finsh");
            this.$message.success("新增成功");
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 编辑
    handleUpdate(params) {
      concentratedUpdate(this.ruleId, params)
        .then((res) => {
          if (res.code === 200) {
            this.handleClose();
            this.$emit("finsh");
            this.$message.success("修改成功");
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 关闭
    handleClose() {
      this.$emit("update:visible", false);
    },
    //=====================================其他操作=====================================//
    // 生成校验逻辑
    generationRules() {
      let tempTextArr = [];
      this.batchData.forEach((item) => {
        if (item.type === "自定义") {
          tempTextArr.push(
            `${
              item.start && item.end
                ? item.start + "至" + item.end
                : "（请选择日期）"
            }抽样总量超过${item.max || "（）"}批次，系统给出超出提示。`
          );
        } else {
          tempTextArr.push(
            `${item.type}抽样总量超过${
              item.max || "（）"
            }批次，系统给出超出提示。`
          );
        }
      });
      return `单家被抽样单位，单个任务下
      ${
        this.formInfo.source === "是"
          ? this.formInfo.sourceValue
            ? "任务来源为：" + this.formInfo.sourceValue
            : "任务来源为:（）"
          : ""
      }
      ，${tempTextArr.join("")}`;
    },
    // 数据范围选择
    handleRange(e) {
      if (e !== "本机构数据") {
        this.$message.warning("相关数据对接中，敬请期待");
      }
      this.formInfo.range = "本机构数据";
    },
    // 监听获取范围组件的数据
    handleDataUpdate(data) {
      this.batchData = data;
    },
  },
};
</script>

<style lang="scss" scoped>
.card-container {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 15px 2px;
}
</style>
