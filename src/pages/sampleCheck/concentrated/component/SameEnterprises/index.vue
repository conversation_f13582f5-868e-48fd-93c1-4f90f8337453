/* 同企业总量校验规则设定 */
<template>
  <el-dialog
    title="不同任务下抽样总量校验规则设定"
    :visible.sync="visible"
    width="70%"
    @close="handleClose"
    top="2vh"
  >
    <el-form ref="form" v-model="formInfo" inline>
      <s-card title="基本信息">
        <el-col :span="20">
          <el-form-item label="校验类型：" prop="type">
            {{ formInfo.type }}
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="校验说明：" prop="name">
            {{ formInfo.checkDescription }}
          </el-form-item>
        </el-col>
      </s-card>
      <s-card title="请选择是否指定任务来源校验（任务来源即任务下发机构）">
        <div class="card-container">
          <el-radio-group v-model="formInfo.source">
            <el-radio label="否">否</el-radio>
            <el-radio label="是">是</el-radio>
            <s-card class="mt20" v-if="formInfo.source === '是'">
              <el-form-item label="任务来源：">
                <el-input
                  clearable
                  v-model="formInfo.sourceValue"
                  size="mini"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </s-card>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请选择时间字段">
        <div class="card-container">
          <el-radio-group v-model="formInfo.timeField">
            <el-radio label="抽样时间">抽样时间</el-radio>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请选择是否区分食品分类校验">
        <div class="card-container">
          <el-radio-group v-model="formInfo.spfl">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
            <s-card class="mt20" v-if="formInfo.spfl === 1">
              <el-form-item label="食品分类：" prop="spflValue">
                <el-radio-group v-model="formInfo.spflValue">
                  <el-radio
                    v-for="item in classification"
                    :key="item.id"
                    :label="item.id"
                    >{{ item.name }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </s-card>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请选择是否区分抽样地点校验">
        <div class="card-container">
          <el-radio-group v-model="formInfo.place">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
            <s-card class="mt20" v-if="formInfo.place === 1">
              <el-row :gutter="10" class="card-container">
                <el-col :span="12">
                  <el-form-item
                    label="抽样环节（单选）："
                    prop="cyhj"
                    label-width="150px"
                  >
                    <el-select
                      size="mini"
                      v-model="formInfo.cyhj"
                      @change="handleLinks"
                      clearable
                    >
                      <el-option
                        v-for="item in links"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="抽样地点（可多选）："
                    prop="cydd"
                    label-width="180px"
                  >
                    <el-select
                      :disabled="!formInfo.cyhj"
                      clearable
                      size="mini"
                      v-model="formInfo.cydd"
                      collapse-tags
                      multiple
                    >
                      <el-option
                        v-for="item in placesData"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </s-card>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请选择校验对象">
        <div class="card-container">
          <el-radio-group v-model="formInfo.target">
            <el-radio label="单家被抽样单位">单家被抽样单位</el-radio>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请选择时间范围及阈值（可多选）" class="mt10">
        <s-annual-quarterly-batch
          @updateResult="handleDataUpdate"
          ref="batchComponent"
          :dataInfo="initBatchData"
        ></s-annual-quarterly-batch>
      </s-card>
      <s-card title="请选择数据范围">
        <div class="card-container">
          <el-radio-group v-model="formInfo.range" @change="handleRange">
            <el-radio label="本机构数据">本机构数据</el-radio>
            <el-radio label="全国">全国</el-radio>
            <el-radio label="机构所在省份">机构所在省份</el-radio>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="校验逻辑">
        <div class="card-container">
          <p>{{ formInfo.logic }}</p>
        </div>
      </s-card>
      <s-card title="规则状态">
        <el-form-item label="机构状态：" prop="status">
          <el-radio-group v-model="formInfo.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </s-card>
    </el-form>
    <div slot="footer">
      <el-button
        :loading="loading"
        :disabled="loading"
        size="small"
        type="primary"
        @click="handleSubmit"
      >
        确定
      </el-button>
      <el-button size="small" type="warning" @click="handleClose">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import AnnualQuarterlyBatch from "@/pages/sampleCheck/components/AnnualQuarterlyBatch.vue";
import PlaceArrJson from "./data.json";
import SCard from "@/pages/sampleCheck/components/s-card.vue";
import {
  concentratedAdd,
  concentratedUpdate,
  concentratedDetail,
} from "@/api/modules/sampleCheck.js";
export default {
  components: {
    "s-annual-quarterly-batch": AnnualQuarterlyBatch,
    "s-card": SCard,
  },
  props: {
    visible: {
      //弹窗是否显示
      type: Boolean,
      default: false,
    },
    ruleId: {
      //规则id
      type: String,
      default: "",
    },
  },
  data() {
    return {
      //=================================表单与表格参数================================//
      formInfo: {
        type: "不同任务下抽样总量校验",
        checkDescription:
          "不同任务来源，单家被抽样单位全年/季度/自定义时间抽样总量校验",
        source: "否",
        sourceValue: "",
        timeField: "抽样时间",
        spfl: 0,
        place: 0,
        cydd: [],
        target: "单家被抽样单位",
        time: "",
        range: "本机构数据",
        logic: "",
        status: 1,
        spflValue: "",
      },
      labelWidth: "85px",
      //===================================枚举参数====================================//
      //===================================业务参数====================================//
      //===================================其他参数====================================//
      loading: false, //新增按钮加载效果
      batchData: [], // 时间范围
      initBatchData: [], // 初始化时间范围
      classification: [
        { id: 1, name: "食品大类" },
        { id: 2, name: "食品亚类" },
        { id: 3, name: "食品次亚类" },
        { id: 4, name: "食品细类" },
      ],
      links: [
        { id: 1, name: "生产" },
        { id: 2, name: "流通" },
        { id: 3, name: "餐饮" },
      ],
      placesData: [], // 抽样地点枚举值
    };
  },
  watch: {
    formInfo: {
      handler: function (val, oldVal) {
        this.formInfo.logic = this.generationRules();
      },
      deep: true,
    },
    batchData: {
      handler: function (val, oldVal) {
        this.$set(this.formInfo, "logic", this.generationRules());
      },
      deep: true,
    },
  },
  created() {
    if (this.ruleId) {
      this.getFormInfo();
    }
  },
  methods: {
    //=====================================初始数据====================================//
    // 查询详情
    getFormInfo() {
      concentratedDetail(this.ruleId)
        .then((res) => {
          let tempObj = res.data;
          if (tempObj.source !== "否") {
            tempObj.sourceValue = tempObj.source;
            tempObj.source = "是";
          }
          if (tempObj.spfl !== 0) {
            tempObj.spflValue = tempObj.spfl;
            tempObj.spfl = 1;
          }
          // 抽样地点
          if (tempObj.cyhj !== 0 && tempObj.cyhj) {
            tempObj.place = 1;
            tempObj.cydd = tempObj.cydd.split(",");
            // 抽样地点枚举值
            this.placesData = PlaceArrJson[tempObj.cyhj];
          } else if (tempObj.cyhj === 0) {
            tempObj.place = 0;
            tempObj.cydd = [];
            tempObj.cyhj = null;
          }
          this.formInfo = tempObj;
          this.initBatchData = JSON.parse(res.data.time);
        })
        .catch((err) => {});
    },
    //=====================================组件间交互====================================//
    handleSubmit() {
      /* 校验 */
      // 任务来源
      if (this.formInfo.source === "是" && !this.formInfo.sourceValue) {
        return this.$message.error(
          "当前选择了区分任务来源校验，任务来源必须填写"
        );
      }
      // 食品分类
      if (this.formInfo.spfl === 1 && !this.formInfo.spflValue) {
        return this.$message.error(
          "当前选择了区分食品分类校验，食品分类必须选择"
        );
      }
      // 抽样地点
      if (this.formInfo.place === 1) {
        if (!this.formInfo.cyhj) {
          return this.$message.error(
            "当前选择了区分抽样地点校验，抽样环节必须选择"
          );
        } else if (!this.formInfo.cydd.length) {
          return this.$message.error(
            "当前选择了区分抽样地点校验，抽样地点必须选择"
          );
        }
      }
      // 校验时间范围组件
      if (!this.$refs.batchComponent.validateSelection()) {
        return;
      }
      let params = {
        ...this.formInfo,
        status: this.formInfo.status,
        time: JSON.stringify(this.batchData),
      };
      // 处理任务来源
      if (params.source === "是") {
        params.source = params.sourceValue;
      }
      // 处理食品分类
      if (params.spfl === 1) {
        params.spfl = params.spflValue;
      }
      // 处理抽样地点   cyhj抽样环节：0否1生产2流通3餐饮  cydd：抽样地点字符串集，逗号','分割
      if (params.place === 0) {
        params.cyhj = params.place;
        params.cydd = "";
      } else {
        params.cydd = params.cydd.join(",");
      }

      delete params.place;
      delete params.spflValue;
      delete params.sourceValue;
      // console.log("请求参数", params);
      // return;
      this.loading = true;
      if (this.ruleId) {
        this.handleUpdate(params);
      } else {
        this.handleAdd(params);
      }
    },
    // 新增
    handleAdd(params) {
      concentratedAdd(params)
        .then((res) => {
          if (res.code === 200) {
            this.handleClose();
            this.$emit("finsh")
            this.$message.success("新增成功");
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 编辑
    handleUpdate(params) {
      concentratedUpdate(this.ruleId, params)
        .then((res) => {
          if (res.code === 200) {
            this.handleClose();
            this.$emit("finsh")
            this.$message.success("修改成功");
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 关闭
    handleClose() {
      this.$emit("update:visible", false);
    },
    //=====================================其他操作=====================================//
    // 生成校验逻辑
    generationRules() {
      let tempTextArr = [];
      console.log(this.formInfo.cydd);
      this.batchData.forEach((item) => {
        if (item.type === "自定义") {
          tempTextArr.push(
            `${
              item.start && item.end
                ? item.start + "至" + item.end
                : "（请选择日期）"
            }抽样总量超过${item.max || "（）"}批次，系统给出超出提示。`
          );
        } else {
          tempTextArr.push(
            `${item.type}抽样总量超过${
              item.max || "（）"
            }批次，系统给出超出提示。`
          );
        }
      });
      return `单家被抽样单位，${
        this.formInfo.source === "是"
          ? this.formInfo.sourceValue
            ? "任务来源为：" + this.formInfo.sourceValue
            : "任务来源为:（）"
          : "所有任务来源"
      }，
      ${
        this.formInfo.spfl === 0
          ? ""
          : "相同的" +
            (this.formInfo.spflValue
              ? this.mapClassification(this.formInfo.spflValue)
              : "（）") +
            "，"
      }
      ${
        this.formInfo.place === 0
          ? "所有抽样地点，"
          : "抽样地点为" +
            (this.formInfo.cydd.length
              ? this.formInfo.cydd.join("、")
              : "（）") +
            "，"
      }
      ${tempTextArr.join("")}`;
    },
    // 数据范围选择
    handleRange(e) {
      if (e !== "本机构数据") {
        this.$message.warning("相关数据对接中，敬请期待");
      }
      this.formInfo.range = "本机构数据";
    },
    // 监听获取范围组件的数据
    handleDataUpdate(data) {
      this.batchData = data;
    },
    // 抽样环节改变重置抽样地点
    handleLinks(val) {
      this.$set(this.formInfo, "cydd", []);
      this.$set(this.formInfo, "cyhj", val);
      this.placesData = PlaceArrJson[val];
    },
    // 分类映射
    mapClassification(val) {
      switch (val) {
        case 0:
          return "否";
        case 1:
          return "食品大类";
        case 2:
          return "食品亚类";
        case 3:
          return "食品次亚类";
        case 4:
          return "食品细类";
        default:
          return "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.card-container {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 15px 2px;
  min-width: 830px;
}
</style>
