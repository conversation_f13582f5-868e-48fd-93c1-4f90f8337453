/* 重复抽样 */
<template>
  <el-card>
    <div slot="header" class="clearfix">
      <span>筛选条件</span>
    </div>
    <!-- 搜索条件 -->
    <div class="filter-options">
      <el-form ref="searchForm" :inline="true" :model="searchForm" size="small">
        <el-form-item label="校验类型：">
          <el-select v-model="searchForm.type" clearable>
            <el-option
              label="同批次样品校验"
              value="同批次样品校验"
            ></el-option>
            <el-option
              label="同企业总量校验"
              value="同企业总量校验"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="loadData(searchForm)"
            :loading="loading"
            >查询</el-button
          >
          <el-button @click="handleReset">重置</el-button>
          <el-dropdown @command="handleType">
            <el-button class="ml-10" type="success"
              >生成校验规则<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1">同批次样品校验</el-dropdown-item>
              <el-dropdown-item :command="2">同企业总量校验</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格展示 -->
    <el-table ref="table" :data="tableData" class="mt-20" v-loading="loading">
      <el-table-column
        prop="type"
        label="校验类型"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="describeValue"
        label="校验说明"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="logic"
        label="校验逻辑"
        align="center"
        width="280"
      ></el-table-column>
      <el-table-column
        prop="source"
        width="110"
        label="是否区任务来源"
        align="center"
      >
      </el-table-column>
      <el-table-column prop="relationship" label="关联字段" align="center">
        <template slot-scope="{ row }">
          {{ row.relationship ? row.relationship : "/" }}
        </template>
      </el-table-column>
      <el-table-column prop="time" label="时间范围" align="center">
      </el-table-column>
      <el-table-column prop="rangeValue" label="数据范围" align="center">
      </el-table-column>
      <el-table-column prop="max" label="阈值" align="center"></el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template slot-scope="{ row }">
          <el-tag v-show="row.status === 1" type="success">启用</el-tag>
          <el-tag v-show="row.status === 0" type="danger">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="140px" fixed="right">
        <template slot-scope="{ row }">
          <el-button
            v-show="row.status === 0"
            type="text"
            @click="handleStart(row)"
            >启用</el-button
          >
          <el-button
            v-show="row.status === 1"
            type="text"
            @click="handleStop(row)"
            >停用</el-button
          >
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" class="red" @click="handleDelete(row)"
            >删除</el-button
          >
          <el-button type="text" disabled @click="handleEdit(row)"
            >查看已抽样情况</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
      ></el-pagination>
    </div>
    <!-- 同批次编辑 -->
    <s-same-batch
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :ruleId="ruleId"
      @finsh="loadData"
    >
    </s-same-batch>

    <!-- 同企业编辑 -->
    <s-same-enterprises
      v-if="dialogVisible2"
      :visible.sync="dialogVisible2"
      :ruleId="ruleId"
      @finsh="loadData"
    >
    </s-same-enterprises>
  </el-card>
</template>

<script>
import SameBatch from "./component/SameBatch/index.vue";
import SameEnterprises from "./component/SameEnterprises/index.vue";
import {
  repeatedList,
  repeatedDelete,
  repeatedStart,
  repeatedStop,
} from "@/api/modules/sampleCheck.js";

export default {
  components: {
    "s-same-batch": SameBatch,
    "s-same-enterprises": SameEnterprises,
  },
  data() {
    return {
      tableData: [],
      searchForm: {},
      dialogVisible: false, // 同批次样品校验 新增/编辑
      dialogVisible2: false, // 同企业总量校验 新增/编辑
      ruleId: "", //规则ID
      page: {
        total: 0,
        current: 1,
        size: 10,
      },
      loading: false,
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    //=====================================获取远程数据==================================//
    // 初始表格数据
    loadData() {
      this.loading = true;
      const params = {
        ...this.searchForm,
        current: this.page.current,
        size: this.page.size,
      };
      repeatedList(params)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data.records;
            this.page.total = res.data.total ? parseFloat(res.data.total) : 0;
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          console.error("获取列表失败:", err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 分页
    handleSizeChange(val) {
      this.page.size = val;
      this.loadData();
    },
    handleCurrentChange(val) {
      this.page.current = val;
      this.loadData();
    },
    // 重置
    handleReset() {
      this.searchForm = {};
      this.loadData();
    },
    //=====================================其他操作=====================================//
    // 新增弹窗 1-同批次样品校验     2-同企业总量校验
    handleType(command) {
      if (command === 1) {
        this.dialogVisible = true;
      } else {
        this.dialogVisible2 = true;
      }
      this.ruleId = "";
    },
    // 编辑规则
    handleEdit(row) {
      this.ruleId = row.id;
      if (row.type === "同批次样品校验") {
        this.dialogVisible = true;
      } else {
        this.dialogVisible2 = true;
      }
    },
    // 删除规则
    handleDelete(row) {
      this.$confirm("此操作将删除该规则，是否继续?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const params = {
            id: row.id,
          };
          repeatedDelete(params)
            .then(() => {
              this.loadData();
            })
            .catch((err) => {
              console.error(err);
            });
        })
        .catch(() => {});
    },
    // 启用
    handleStart(row) {
      const params = {
        id: row.id,
      };
      repeatedStart(params)
        .then(() => {
          this.loadData();
        })
        .catch((err) => {
          console.error(err);
        });
    },
    // 停用
    handleStop(row) {
      this.$confirm("此操作将停用该规则，是否继续?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const params = {
          id: row.id,
        };
        repeatedStop(params)
          .then(() => {
            this.loadData();
          })
          .catch((err) => {
            console.error(err);
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-form-item {
  margin-bottom: 0 !important;
}
.ml-10 {
  margin-left: 10px;
}
.mt-20 {
  margin-top: 20px;
}
::v-deep .el-radio__input.is-checked .el-radio__inner {
  border-color: #409eff;
  background: #409eff;
}
::v-deep .el-radio__input.is-checked + .el-radio__label {
  color: #409eff;
}
</style>
