/* 同批次样品校验规则设定 */
<template>
  <el-dialog
    title="同批次样品校验规则设定"
    :visible.sync="visible"
    width="70%"
    @close="handleClose"
    top="2vh"
  >
    <el-form ref="form" v-model="formInfo" inline>
      <s-card title="基本信息">
        <el-col :span="20">
          <el-form-item label="校验类型：" prop="type">
            {{ formInfo.type }}
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="校验说明：" prop="name">
            {{ formInfo.describeValue }}
          </el-form-item>
        </el-col>
      </s-card>
      <s-card title="请选择是否区分任务来源校验">
        <div class="card-container">
          <el-radio-group v-model="formInfo.source">
            <el-radio label="否">否</el-radio>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请选择关联字段以确认同批次样品">
        <div class="card-container">
          <el-checkbox-group v-model="formInfo.relationship">
            <el-checkbox label="生产许可证号" disabled></el-checkbox>
            <el-checkbox label="生产企业名称" disabled></el-checkbox>
            <el-checkbox label="生产日期" disabled></el-checkbox>
            <el-checkbox label="样品名称" disabled></el-checkbox>
            <el-checkbox label="规格型号"></el-checkbox>
            <!-- <el-checkbox label="其他">
              其他
              <el-select
                v-show="formInfo.relationship.includes('其他')"
                v-model="formInfo.other"
                size="mini"
                class="other-input"
                clearable
              >
                <el-option
                  v-for="item in selectEnums"
                  :key="item.name"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-checkbox> -->
          </el-checkbox-group>
        </div>
      </s-card>
      <s-card title="请选择校验对象">
        <div class="card-container">
          <el-radio-group v-model="formInfo.target">
            <el-radio label="全部生产企业">全部生产企业</el-radio>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请选择时间范围">
        <div class="card-container">
          <el-radio-group v-model="formInfo.time">
            <el-radio label="全年">
              {{ `全年（${new Date().getFullYear()}年）` }}
            </el-radio>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请选择数据范围">
        <div class="card-container">
          <el-radio-group v-model="formInfo.rangeValue" @change="handleRange">
            <el-radio label="本机构数据">本机构数据</el-radio>
            <el-radio label="全国">全国</el-radio>
            <el-radio label="机构所在省份">机构所在省份</el-radio>
          </el-radio-group>
        </div>
      </s-card>
      <s-card title="请填写数据阈值（同批次样品检验，建议填写1）">
        <div class="card-container">
          <span>超过</span>
          <el-input
            ref="batchNumRef"
            size="small"
            v-model="formInfo.max"
            class="input-no-border"
          ></el-input>
          <span>批次，系统给出提示；</span>
        </div>
      </s-card>
      <s-card title="校验逻辑">
        <div class="card-container">
          <p>{{ formInfo.logic }}</p>
        </div>
      </s-card>
      <s-card title="规则状态">
        <el-form-item label="机构状态：" prop="status">
          <el-radio-group v-model="formInfo.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </s-card>
    </el-form>
    <div slot="footer">
      <el-button
        :loading="loading"
        :disabled="loading"
        size="small"
        type="primary"
        @click="handleSubmit"
      >
        确定
      </el-button>
      <el-button size="small" type="warning" @click="handleClose">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import SCard from "@/pages/sampleCheck/components/s-card.vue";
import { repeatedAdd, repeatedDetail } from "@/api/modules/sampleCheck.js";
export default {
  components: {
    "s-card": SCard,
  },
  props: {
    visible: {
      //弹窗是否显示
      type: Boolean,
      default: false,
    },
    ruleId: {
      //规则id
      type: String,
      default: "",
    },
  },
  data() {
    return {
      //=================================表单与表格参数================================//
      formInfo: {
        type: "同批次样品校验",
        describeValue: "相同样品重复抽样校验",
        source: "否",
        relationship: ["生产许可证号", "生产企业名称", "生产日期", "样品名称"],
        target: "全部生产企业",
        time: "全年",
        rangeValue: "本机构数据",
        max: "",
        logic: "",
        status: 1,
      },
      labelWidth: "85px",
      selectEnums: [], // 其他字段枚举
      //===================================枚举参数====================================//
      //===================================业务参数====================================//
      //===================================其他参数====================================//
      loading: false, //新增按钮加载效果
    };
  },
  watch: {
    formInfo: {
      handler: function (val, oldVal) {
        this.formInfo.logic = this.generationRules(val);
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    // this.getOtherList();
    if (this.ruleId) {
      this.getFormInfo();
    }
  },
  methods: {
    //=====================================初始数据====================================//
    // 查询详情
    getFormInfo() {
      let params = {
        id: this.ruleId,
      };
      repeatedDetail(params)
        .then((res) => {
          this.formInfo = {
            ...res.data,
            relationship: res.data.relationship.split(","),
          };
        })
        .catch((err) => {})
        .finally(() => {});
    },
    // 查询其他 字段枚举值
    getOtherList() {
      this.axios
        .get("/base/selectEbsOrg")
        .then((res) => {
          let list = res.data;
          this.selectEnums = list.map((item) => {
            return { name: item };
          });
        })
        .catch((err) => {})
        .finally(() => {});
    },
    //=====================================组件间交互====================================//
    handleSubmit() {
      if (!this.formInfo.max) {
        this.$refs.batchNumRef.focus();
        return this.$message.error("请填写数据阈值");
      }
      this.loading = true;
      const params = {
        ...this.formInfo,
        status: this.formInfo.status,
        relationship: this.formInfo.relationship.join(","),
      };
      repeatedAdd(params)
        .then((res) => {
          if (res.code === 200) {
            this.handleClose();
            this.$emit("finsh")
            this.$message.success(this.ruleId ? "修改成功" : "新增成功");
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 关闭
    handleClose() {
      this.$emit("update:visible", false);
    },
    //=====================================其他操作=====================================//
    // 生成校验逻辑
    generationRules(val) {
      let relationshipArr = "";
      if (val.relationship.includes("其他")) {
        let tempArr = val.relationship.filter((item) => item !== "其他");
        // 其他有值
        if (val.other) {
          tempArr.push(val.other);
        }
        relationshipArr = tempArr.join("、");
      } else {
        relationshipArr = val.relationship.join("、");
      }
      return `${
        val.source === "是" ? "区分" : "不区分"
      }任务来源，${relationshipArr}完全一致时，本机构抽样数量>${
        val.max || "（）"
      }批次时，给出预警。`;
    },
    // 数据范围选择
    handleRange(e) {
      if (e !== "本机构数据") {
        this.$message.warning("相关数据对接中，敬请期待");
      }
      this.formInfo.rangeValue = "本机构数据";
    },
  },
};
</script>

<style lang="scss" scoped>
.card-container {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 15px 2px;
}
.other-input {
  width: 150px;
  margin-left: 5px;
}
/* 设置checkbox禁用选中样式 */
::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
}
::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  border-color: #fff !important;
}
::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #409eff !important;
}
/* 输入框无左-右-上边框 */
::v-deep .input-no-border {
  width: 100px;
  .el-input__inner {
    border-top: none;
    border-left: none;
    border-right: none;
    border-radius: 0;
    padding: 5px;
    text-align: center;
  }
}
</style>
