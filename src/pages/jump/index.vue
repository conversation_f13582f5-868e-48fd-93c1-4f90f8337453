<template>
  <div></div>
</template>

<script>
import { setToken } from "@/utils/auth";
import { JSEncrypt } from "jsencrypt";
import axios from "axios";
import Cookies from "js-cookie";
import { getUserType } from "@/api/modules/sysUser";

export default {
  name: "Jump",
  computed: {
    message() {},
  },
  created() {
    const that = this;
    //level=1 弱密码校验、修改密码
    let level = "1";
    //设置密码公钥
    let publicKey =
      "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCNu5SO299bS8NvGXM+dypDLP0BjzcuEJtwvTwjw3qkIFL/NjLT7l2a04QAZbdu88DtC34+KEEfk+gfhed5nDAPIzXyURjbZXoviBisJ0TToMZA2C3Y41reu6dqD3Xy1RzLZMYGtyTAqTj1Lc9ar2O5b/7pd7UXA7BdJznkZ9NA7wIDAQAB";
    console.log(publicKey);
    let jse = new JSEncrypt();
    //设置公钥
    jse.setPublicKey(publicKey);
    let pwd = "Bjxryjc!@#202409+-*/";
    //密码加密  通过rsa非对称加密方式对密码进行加密并拼接固定随机数，有随机数的认为密码是进行过加密的
    pwd = jse.encrypt(pwd) + "PUBKb7028230a84a11edafd700ffcdce1852";
    let userInfo = { name: "admin_lims_jlzj", pwd: pwd, level: level };
    // 使用配置中的登录URL
    let loginUrl = this.$config.loginUrl;

    axios
      .post(loginUrl, JSON.stringify(userInfo), {
        emulateJSON: true,
        headers: { "Content-Type": "application/json;charset=UTF-8" },
      })
      .then(async function (res) {
        if (res.status) {
          console.log(res);
          if (res.data.SSOTGTCookie == null || res.data.SSOTGTCookie == "") {
            that.$message({
              message: "认证失败",
              type: "error",
            });
            return;
          }
          setToken(res.data.SSOTGTCookie);
          Cookies.set("ebs_username", res.data.ebs_username);
          Cookies.set("ebs_realName", res.data.user.realName);

          let data = JSON.parse(JSON.stringify(res.data.organization));
          delete data.permissions;
          Cookies.set("org", JSON.stringify(data));

          that.getUserType();
        } else {
          that.$message({
            message: res.data.msg,
            type: "error",
          });
        }
      })
      .catch((res) => {
        that.$message.error(res.msg || "登录失败!");
      })
      .finally(() => {
        that.loading = false;
      });
  },
  methods: {
    getUserType() {
      getUserType().then((res) => {
        Cookies.set("userType", res.data.type);
        Cookies.set("lat", res.data.latitude);
        Cookies.set("lng", res.data.longitude);

        this.$router.push({ path: "/simple-sample" });
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
