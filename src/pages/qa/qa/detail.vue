<template>
  <el-card shadow='hover'>
    <div class='page-wrapper'>
      <div class='main'>
        <p class='title'>{{ data.title }}</p>
        <p class='info'>发表于{{ data.pushTime }}<span class='ml10'>{{ data.replyCount }}个评论</span><span class='ml10'>{{ data.collectCount }}个收藏</span></p>
        <div class='author'>
          <el-avatar class='mr10' :size='20' :src='data.avatar'></el-avatar>
          {{ data.nickName }}
        </div>
        <div class="content">{{ data.content }}</div>
        <div class="SongList" v-if="imgList">
          <div class="covers">
            <div class="cover" v-for="img in imgList" :key='img'>
              <el-image style="width:80px;height:80px;" :src="img" :preview-src-list="[img]">
              </el-image>
            </div>
          </div>
        </div>

        <p class='sub'>全部评论（{{ data.replyCount }}）</p>
        <div class="line"></div>

        <div class='comment' v-for='(item ,index) in listCom' :key="index">
          <div style="display:flex;flex-direction: row;">
            <el-avatar class='mr10' :size='40' :src='item.avatar'></el-avatar>
            <div class="right">
              <div class="rightTop">
                <label class='name'>{{ item.nickName }}{{ item.isAuthor ? '（作者）' : '' }}</label>
                <label class='time'>{{ item.replyTime }}</label>
              </div>
              <div class="rightBottom">
                <p class='content'>{{ item.reply }}</p>
                <div class='btns' style="widht:100%">
                  <el-button class='bt textRed' type='text' size='mini' @click='handleDel(item)'>删除</el-button>
                  <el-button class='bt btnBlue' type='text' size='mini' @click='handleReply(2,item)'>回复</el-button>
                </div>
              </div>
            </div>
          </div>
          <div class='reply' v-if='item.commentList' v-for='(child,idx) in getCommentArr(item.commentList)' :key="idx">
            <div class='top'>
              <el-avatar class='mr10' :size='20' :src='child.avatar'></el-avatar>
              <label class='name'>{{ child.nickName }}</label>
              <label>回复</label>
              <el-avatar style="margin-left:10px" class='mr10' :size='20' :src='child.avatar'></el-avatar>
              <label class='name'>{{ child.toUserNickName }}</label>
              <label class='time'>{{ child.replyTime }}</label>
            </div>
            <div class='bottom'>
              <p class='content'>{{ child.reply }}</p>
              <div class='btns'>
                <el-button class='bt textRed' type='text' size='mini' @click='handleDel(child)'>删除</el-button>
                <el-button class='bt btnBlue' type='text' size='mini' @click='handleReply(2,child)'>回复</el-button>
              </div>
            </div>
          </div>
          <div class="more" v-if="+item.commentList.length > 2" @click="checkChildMordClick(item)">
            查看全部{{+item.commentList.length}}条内容 >
          </div>
          <div class="line" style="margin-top:3px;"></div>
        </div>
        <div class="more" v-if="listCom.length>0 && !isHidenMore" style="margin:0 auto; margin-top:10px; " @click="checkMoreClick">
          查看更多评论 >
        </div>
      </div>
      <el-button class='mt20 btnBlue' type='primary' plain round @click="handleReply(1)">回复</el-button>
    </div>

    <el-dialog :visible.sync='showReply' v-loading="loading" width='40%' title='回复'>
      <el-form ref='form' class='form' label-width='120px' label-position='left' size='mini'>
        <el-form-item label='回复内容' required>
          <el-input type='textarea' v-model='replyContent' :rows='8' />
        </el-form-item>
      </el-form>
      <div slot='footer' class='footer'>
        <el-button size='small' @click='handleClose'>取 消</el-button>
        <el-button class='ml20' type='primary' size='small' @click='handleSubmit'>提 交</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync='childMore' width='60%' title='全部评论'>
      <div style="padding:0 10px;">
        <div class='comment' v-for='(child,idx) in childMoreList' :key="idx">
          <div style="display:flex;flex-direction: row;">
            <el-avatar class='mr10' :size='40' :src='child.avatar'></el-avatar>
            <div class="right">
              <div class="rightTop">
                <label class='name'>{{ child.nickName }}</label>
                <label class='time'>{{ child.replyTime }}</label>
              </div>
              <div class="rightBottom">
                <p class='content'><span style="color:#BFC3CB;">回复：{{child.toUserNickName}}</span> {{ child.reply }}</p>
                <div class='btns' style="widht:100%">
                  <el-button class='bt textRed' type='text' size='mini' @click='handleDel(child)'>删除</el-button>
                  <el-button class='bt btnBlue' type='text' size='mini' @click='handleReply(2,child)'>回复</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import { QAComment, QADeleteReply, QADetail, getComment } from '@/api/modules/verify.js'

export default {
  data() {
    return {
      imgList: [],
      isHidenMore: false,
      childMore: false,
      childMoreList: [],
      loading: false,
      id: '',
      replyType: 1,
      showReply: false,
      curr: null,
      replyContent: '',
      data: {
      },
      listCom: [
      ],
      page: {
        current: 0,
        size: 10
      },
    }
  },
  mounted() {
    this.id = this.$route.query.id
    this.loadData()
  },
  methods: {
    loadData() {
      this.imgList = []
      QADetail({ id: this.id }).then(res => {
        this.data = res.data
        // this.listCom = res.data.commentList
        this.imgList = res.data.contentPic ? res.data.contentPic.split(',') : []
        this.checkMoreClick()
      })
    },
    checkMoreClick() {
      this.page.current++
      let param = this.page
      param.id = this.id
      getComment(param).then(res => {
        this.listCom = this.listCom.concat(res.data.records)
        this.isHidenMore = this.listCom.length >= +res.data.total ? true : false
      })
    },
    handleReply(type, row) {

      this.replyType = type
      this.curr = row
      this.showReply = true
    },
    handleDel(row) {
      this.$confirm('确定删除当前评论吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        QADeleteReply({ id: row.id }).then(res => {
          if (+res.code === 200) {
            this.childMore = false
            this.$message.success(res.msg)
            this.listCom = []
            this.loadData()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {

      })
    },
    getCommentArr(arr = []) {
      if (arr.length <= 2) {
        return arr
      }
      return arr.slice(0, 2)
    },
    handleClose() {
      this.showReply = false
      this.curr = null
      this.replyContent = null
      this.replyType = null
    },
    handleSubmit() {
      let data = {
        id: this.curr ? this.curr.id : null,
        qaId: this.id,
        commentType: this.replyType,
        reply: this.replyContent
      }
      QAComment(data).then(res => {
        if (res.code == 200) {
          this.$message.success('操作成功')
          this.childMore = false
          this.handleClose()
          this.listCom = []
          this.loadData()
        }
      }).finally(() => {
        this.loading = false
      })
    },
    checkChildMordClick(val) {
      this.childMore = true
      this.childMoreList = val.commentList
    },
  }
}
</script>

<style lang='scss' scoped>
.page-wrapper {
  display: flex;
  align-items: flex-start;

  .main {
    width: 56%;
    margin-left: 24%;
    margin-top: 30px;
    margin-right: auto;
    font-size: 14px;

    .title {
      font-size: 24px;
      line-height: 36px;
    }

    .info {
      height: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #c0c4cc;
      line-height: 20px;
      margin-top: 8px;
    }

    .author {
      display: flex;
      flex-direction: row;
      margin-top: 8px;
      height: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #409eff;
      line-height: 20px;
    }

    .tags {
      width: auto;
      padding: 5px;
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
    }
    .content {
      margin: 20px 0;
      font-size: 14px;
      font-weight: 400;
      color: #131414;
      line-height: 20px;
    }
    .sub {
      margin: 20px 0;
      font-size: 16px;
      font-weight: 500;
      color: #131414;
      line-height: 24px;
    }
  }
}

.comment {
  margin-top: 10px;
  .right {
    width: 100%;
  }
  .rightTop {
    display: flex;
    justify-content: space-between;
    .name {
      font-size: 14px;
      font-weight: 400;
      color: #606266;
      line-height: 20px;
    }
    .time {
      font-size: 14px;
      font-weight: 400;
      color: #c0c4cc;
      line-height: 20px;
    }
  }
  .rightBottom {
    .content {
      font-size: 16px;
      font-weight: 400;
      color: #131414;
      line-height: 22px;
      margin: 4px 0;
    }
    .btns {
      display: flex;
      flex-direction: row-reverse;
      .bt {
        margin: 0 5px;
      }
    }
  }
}

.reply {
  padding-left: 50px;
  .top {
    display: flex;
    align-items: center;
    height: 40px;

    .name {
      margin-right: 10px;
      font-size: 14px;
      font-weight: 400;
      color: #606266;
      line-height: 20px;
    }

    .time {
      margin-left: auto;
      color: #c0c4cc;
      text-align: right;
    }
  }

  .bottom {
    padding-left: 50px;
    margin-top: 10px;
    margin-bottom: 10px;

    .content {
      font-size: 16px;
      font-weight: 400;
      color: #131414;
      line-height: 22px;
      margin: 4px 0;
    }
    .btns {
      display: flex;
      flex-direction: row-reverse;
      .bt {
        margin: 0 5px;
      }
    }
  }
}

.form {
  margin: 20px 50px;
}
.textRed {
  color: #f56c6c;
}
.btnBlue {
  color: #409eff;
}
.more {
  padding: 3px;
  cursor: pointer;
  margin-left: 50px;
  text-align: center;
  width: 138px;
  height: 28px;
  background: #f5f5f5;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}
.line {
  border-bottom: 1px solid #e6ebf5;
  height: 1px;
  width: 100%;
  margin-bottom: 10px;
}

.SongList {
  width: 30%;
}
.covers {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.cover {
  display: flex;
  justify-content: center;
  width: 33%;
  padding: 5px;
}
</style>
