<template>
  <div class="task-detail-page">
    <el-card class="task-card">
      <div slot="header" class="task-header">
        <div class="header-left">
          <span class="page-title">任务详情</span>
        </div>
      </div>

      <!-- 任务信息 -->
      <div class="task-info-section" v-loading="loading">
        <!-- 基本信息 -->
        <div class="basic-info-card">
          <div class="task-info-item">
            <span class="info-label">任务来源：</span>
            <span class="info-value">{{ taskInfo.taskSource }}</span>
          </div>
          <div class="section-title">
            <span class="title-text">报送分类A：{{ taskInfo.classA }}</span>
            <span class="task-status-wrapper">
              <span class="task-status" v-if="taskInfo.status"
                >待完成批次数：{{
                  taskInfo.totalCount - taskInfo.pendingCount
                }}/{{ taskInfo.totalCount }}</span
              >
              <el-button
                type="primary"
                size="small"
                @click="completeTask"
                class="complete-btn"
                >完成任务</el-button
              >
            </span>
          </div>
          <div class="task-desc">报送分类B：{{ taskInfo.taskName }}</div>
          <div class="task-meta">
            <div class="meta-item">
              <span class="label">抽样人员：</span>
              <span class="value">{{ taskInfo.samplingPerson }}</span>
            </div>
            <div class="meta-item">
              <span class="label">抽样环节：</span>
              <span class="value">{{ taskInfo.sampleSection }}</span>
            </div>
            <div class="meta-item">
              <span class="label">任务执行区域：</span>
              <span class="value">{{ taskInfo.executionArea }}</span>
            </div>
          </div>
        </div>

        <!-- 任务详情表格 -->
        <div class="detail-section">
          <div class="table-container">
            <el-table
              border
              :data="detailData"
              style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
              :span-method="objectSpanMethod"
            >
              <el-table-column
                label="食品大类"
                align="center"
                prop="foodCategory"
              ></el-table-column>
              <el-table-column
                label="食品亚类"
                align="center"
                prop="foodSubcategory"
              ></el-table-column>
              <el-table-column
                label="食品品种"
                align="center"
                prop="foodItem"
              ></el-table-column>
              <el-table-column
                label="食品细类"
                align="center"
                prop="foodDetailType"
              ></el-table-column>
              <el-table-column
                label="最少抽样数量"
                align="center"
                prop="minSampleCount"
              ></el-table-column>
              <el-table-column
                label="最少抽样重量"
                align="center"
                prop="minSampleWeight"
              ></el-table-column>
              <el-table-column
                label="最少备样重量"
                align="center"
                prop="minBackupWeight"
              ></el-table-column>
              <el-table-column
                label="备注说明"
                align="center"
                prop="remark"
              ></el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 智能任务解析 -->
        <div class="detail-section">
          <div class="section-title">智能任务解析</div>

          <div class="analysis-section">
            <div class="analysis-content">
              <div class="analysis-header">
                <div class="analysis-title">食品大类所含不合格产品排名</div>
                <div class="analysis-filters">
                  <!-- <el-button type="primary" size="small" plain
                    >河南省</el-button
                  > -->

                  <div class="date-selectors">
                    <el-select
                      v-model="selectedYear"
                      size="small"
                      placeholder="年份"
                    >
                      <el-option label="2025年" value="2025"></el-option>
                      <el-option label="2024年" value="2024"></el-option>
                      <el-option label="2023年" value="2023"></el-option>
                      <el-option label="2022年" value="2022"></el-option>
                    </el-select>
                    <el-select
                      v-model="selectedQuarter"
                      size="small"
                      placeholder="季度"
                      class="quarter-select"
                    >
                      <el-option label="第一季度" value="Q1"></el-option>
                      <el-option label="第二季度" value="Q2"></el-option>
                      <el-option label="第三季度" value="Q3"></el-option>
                      <el-option label="第四季度" value="Q4"></el-option>
                    </el-select>
                  </div>
                </div>
              </div>

              <div class="filters">
                <span class="filter-label">食品细类：</span>
                <el-select
                  v-model="selectedFoodType"
                  placeholder="请选择"
                  size="small"
                  clearable
                >
                  <el-option label="全部" value=""></el-option>
                  <el-option
                    v-for="item in detailData"
                    :key="item.foodDetailType"
                    :label="item.foodDetailType"
                    :value="item.foodDetailType"
                  ></el-option>
                </el-select>
              </div>

              <!-- 不合格产品排名表格 -->
              <el-table
                v-loading="analysisLoading"
                :data="analysisItems"
                style="width: 100%"
                :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
                class="ranking-table"
              >
                <el-table-column label="排名" align="center" width="80">
                  <template slot-scope="scope">
                    <div
                      :class="['item-num', scope.row.topRank ? 'with-bg' : '']"
                    >
                      {{ scope.$index + 1 }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="样品名称"
                  prop="name"
                  align="left"
                ></el-table-column>
                <el-table-column
                  label="不合格批次/总抽检批次"
                  prop="rate"
                  align="center"
                  width="180"
                ></el-table-column>
                <el-table-column
                  label="不合格率"
                  prop="percent"
                  align="center"
                  width="100"
                  class-name="percent-column"
                ></el-table-column>
                <el-table-column width="50" align="center">
                  <template slot-scope="scope">
                    <el-popover
                      placement="right"
                      width="700"
                      trigger="click"
                      popper-class="company-popover"
                      @show="fetchCompanyData(scope.row.name)"
                    >
                      <div class="popover-content">
                        <h4>不合格企业信息</h4>
                        <div v-loading="companyLoading">
                          <el-table
                            :data="failedCompanyData"
                            style="width: 100%"
                            size="mini"
                            :header-cell-style="{
                              background: '#f5f7fa',
                              color: '#333',
                            }"
                            empty-text="暂无数据"
                          >
                            <el-table-column
                              prop="productionName"
                              label="企业名称"
                              min-width="220"
                              show-overflow-tooltip
                              style="padding-left: 10px"
                            ></el-table-column>
                            <el-table-column
                              prop="productionAddress"
                              label="企业地址"
                              min-width="300"
                              show-overflow-tooltip
                            ></el-table-column>
                            <el-table-column
                              label="不合格率"
                              align="center"
                              width="100"
                            >
                              <template slot-scope="scope">
                                <span style="color: #f56c6c">
                                  {{
                                    (
                                      (scope.row.noPass / scope.row.total) *
                                      100
                                    ).toFixed(2)
                                  }}%
                                </span>
                              </template>
                            </el-table-column>
                          </el-table>

                          <div class="pagination-container">
                            <el-pagination
                              @size-change="handleCompanySizeChange"
                              @current-change="handleCompanyPageChange"
                              :current-page.sync="companyPagination.current"
                              :page-sizes="[10, 20, 30, 50]"
                              :page-size.sync="companyPagination.size"
                              layout="prev, pager, next"
                              :total="companyPagination.total"
                              background
                              small
                            />
                          </div>
                        </div>
                      </div>
                      <el-button slot="reference" type="text" size="mini">
                        <i class="el-icon-arrow-right" />
                      </el-button>
                    </el-popover>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 无数据提示 -->
              <div
                v-if="analysisItems.length === 0 && !analysisLoading"
                class="no-data-tip"
              >
                暂无数据
              </div>
            </div>
          </div>
        </div>

        <!-- 机构抽检情况 -->
        <div class="detail-section">
          <div class="section-title">
            <span>机构抽检情况</span>
            <div class="date-filter">
              <el-radio-group v-model="currentPeriod" size="small">
                <el-radio-button label="quarter">本季度</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="week">本周</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div class="tabs-container">
            <el-tabs
              v-model="activeTab"
              type="card"
              class="borderless-tabs"
              @tab-click="handleTabChange"
            >
              <el-tab-pane
                v-for="tab in sampleTabs"
                :key="tab.name"
                :label="`${tab.name}（${tab.count}）`"
                :name="tab.name"
              >
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="table-container" v-loading="sampleLoading">
            <div
              v-if="sampleTabs.length === 0 && !sampleLoading"
              class="no-data-tip"
            >
              暂无抽检数据
            </div>
            <el-table
              v-else
              :data="currentSampleData"
              style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
              empty-text="暂无数据"
            >
              <el-table-column
                label="样品名称"
                prop="sampleName"
                align="center"
              ></el-table-column>
              <el-table-column
                label="被抽样单位名称"
                prop="unitName"
                align="center"
              ></el-table-column>
              <el-table-column
                label="营业执照号/社会信用代码"
                prop="spBusinessLicenseNo"
                align="center"
              ></el-table-column>
              <el-table-column
                label="生产许可证号"
                prop="productionLicenseCode"
                align="center"
              ></el-table-column>
              <el-table-column
                label="生产日期"
                prop="manufactureDat"
                align="center"
              ></el-table-column>
              <el-table-column
                label="抽样人员"
                prop="samplingPersonnel"
                align="center"
              ></el-table-column>
            </el-table>

            <!-- 分页控件 -->
            <div class="pagination-container" v-if="samplePagination.total > 0">
              <el-pagination
                @size-change="handleSampleSizeChange"
                @current-change="handleSamplePageChange"
                :current-page.sync="samplePagination.current"
                :page-sizes="[10, 20, 30, 50]"
                :page-size.sync="samplePagination.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="samplePagination.total"
                background
                small
              />
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 完成任务弹窗 -->
    <complete-task-modal
      :visible.sync="completeModalVisible"
      :plan-task-id="taskId"
      @success="handleCompleteSuccess"
    ></complete-task-modal>
  </div>
</template>

<script>
import {
  getTaskDetail,
  getTopFiveFailedProducts,
  getProductionFailedList,
  getSampleTestCount,
  getSampleTestDetail,
} from "@/api/modules/task";
import CompleteTaskModal from "./components/CompleteTaskModal.vue";

/**
 * @description 任务详情页面
 */
export default {
  name: "TaskDetail",
  components: {
    CompleteTaskModal,
  },
  data() {
    return {
      loading: false,
      taskId: null,
      taskInfo: {
        type: "",
        taskName: "",
        samplingPerson: "",
        sampleSection: "",
        executionArea: "",
        status: "pending",
        pendingCount: 0, // 已完成批次数
        totalCount: 0, // 总批次数
        classA: "",
        classB: "",
        taskSource: "", // 添加任务来源属性
      },
      detailData: [],
      sampleTabs: [],
      activeTab: "",
      selectedSampleType: "", // 当前选中的抽检样品类型
      sampleListData: [], // 初始化为数组
      failedCompanyData: [], // 企业不合格数据
      companyPagination: {
        current: 1,
        size: 10,
        total: 0,
      },
      companyLoading: false, // 企业数据加载状态
      currentFoodName: "", // 当前选中的食品名称
      analysisItems: [],
      selectedFoodType: "",
      selectedYear: "2022",
      selectedQuarter: "Q3",
      analysisLoading: false,
      currentPeriod: "month", // 当前选择的时间段：quarter, month, all
      currentProductName: "", // 添加当前产品名称
      sampleLoading: false, // 抽检数据加载状态
      samplePagination: {
        current: 1,
        size: 10,
        total: 0,
      },
      completeModalVisible: false, // 添加完成任务弹窗显示状态
    };
  },
  computed: {
    /**
     * 当前选中标签页的样品数据
     */
    currentSampleData() {
      // 确保无论sampleListData是数组还是对象都能正确处理
      if (Array.isArray(this.sampleListData)) {
        return this.sampleListData;
      } else if (
        this.sampleListData &&
        typeof this.sampleListData === "object"
      ) {
        // 如果是对象，尝试将其转换为数组
        return Object.values(this.sampleListData);
      }
      return [];
    },
  },
  watch: {
    // 监听年份变化
    selectedYear() {
      this.fetchAnalysisData();
    },
    // 监听季度变化
    selectedQuarter() {
      this.fetchAnalysisData();
    },
    // 监听食品细类变化
    selectedFoodType() {
      this.fetchAnalysisData();
    },
    // 监听标签页变化
    activeTab(newVal) {
      if (newVal) {
        this.handleTabChange(newVal);
      }
    },
    // 监听时间周期变化
    currentPeriod(newVal, oldVal) {
      if (newVal !== oldVal && this.selectedSampleType) {
        this.fetchSampleTestDetail();
      }
    },
  },
  created() {
    // 获取路由参数中的任务ID
    this.taskId = this.$route.params.id || this.$route.query.id;

    // 获取批次数量信息 - completedBatch 表示已完成批次数
    const completedBatch = Number(
      this.$route.params.batchCompleted || this.$route.query.batchCompleted || 0
    );
    const batchTotal = Number(
      this.$route.params.batchTotal || this.$route.query.batchTotal || 0
    );

    this.taskInfo.pendingCount = completedBatch;
    this.taskInfo.totalCount = batchTotal;

    if (this.taskId) {
      this.fetchTaskDetail();
    } else {
      this.$message.error("任务ID不存在，无法获取详情");
    }
  },
  methods: {
    /**
     * 获取任务详情数据
     */
    fetchTaskDetail() {
      this.loading = true;
      getTaskDetail(this.taskId)
        .then((res) => {
          if (res.code === 200) {
            const data = res.data;
            // 设置基本信息
            this.taskInfo = {
              ...this.taskInfo, // 保留批次数量信息
              classA: data.classA || "",
              classB: data.classB || "",
              taskName: data.classB || "", // 将 classB 作为任务名称
              samplingPerson: data.samplePerson || "",
              sampleSection: data.sampleLink || "",
              status: "pending", // 默认为待处理状态
              executionArea: data.executionArea || "",
              taskSource: data.taskSource || "", // 添加任务来源属性
            };

            // 设置详情数据
            if (data.planTaskDetails && data.planTaskDetails.length > 0) {
              this.detailData = data.planTaskDetails.map((item) => ({
                taskSource: item.taskSource || data.taskSource || "-",
                foodCategory: item.cate1 || "未分类",
                foodSubcategory: item.cate2 || "未分类",
                foodItem: item.cate3 || "未分类",
                foodDetailType: item.cate4 || "未分类",
                remark: item.remark || "",
                minSampleCount: item.minSampleQuantity || "",
                minSampleWeight: item.minSampleWeight || "",
                minBackupWeight: item.minBackupWeight || "",
              }));
            }

            // 设置样品标签页
            this.sampleTabs = data.sampleTabs || [];
            if (this.sampleTabs.length > 0) {
              this.activeTab = this.sampleTabs[0].name;
            }

            // 设置样品列表数据
            this.sampleListData = data.sampleList || {};

            // 设置分析项目
            this.analysisItems = data.analysisItems || [];

            // 在获取任务详情后立即获取分析数据
            this.fetchAnalysisData();

            // 在获取任务详情后，获取抽检统计数据
            this.fetchSampleTestCount();
          } else {
            this.$message.error(res.msg || "获取任务详情失败");
          }
        })
        .catch((err) => {
          console.error("获取任务详情失败:", err);
          this.$message.error("获取任务详情失败，请检查网络连接");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /**
     * 切换时间段
     */
    changePeriod(period) {
      this.currentPeriod = period;
      // 这里可以根据不同的时间段筛选数据
      this.$message.info(
        `已切换到${
          period === "quarter" ? "本季度" : period === "month" ? "本月" : "本周"
        }数据`
      );
    },
    /**
     * 完成任务
     */
    completeTask() {
      this.completeModalVisible = true;
    },
    /**
     * 任务完成成功回调
     */
    handleCompleteSuccess() {
      // 刷新任务详情数据
      this.fetchTaskDetail();
      this.$message.success("任务已完成");
    },
    /**
     * 查看详情
     */
    viewDetail(item) {
      console.log("查看详情:", item);
      this.$message.info(`查看 ${item.name} 的详细信息`);
    },
    /**
     * 单元格合并方法
     */
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 只处理第一列（食品大类）
      if (columnIndex === 0) {
        // 为每个食品大类创建跨度
        const foodCategories = this.detailData.map((item) => item.foodCategory);
        const spanArr = [];
        const pos = {};

        // 计算合并信息
        foodCategories.forEach((food, index) => {
          if (index === 0) {
            spanArr.push(1);
            pos[food] = 0;
          } else {
            if (food === foodCategories[index - 1]) {
              spanArr[pos[food]] += 1;
              spanArr.push(0);
            } else {
              spanArr.push(1);
              pos[food] = index;
            }
          }
        });

        // 返回合并信息
        if (spanArr[rowIndex] === 0) {
          return { rowspan: 0, colspan: 0 };
        } else {
          return { rowspan: spanArr[rowIndex], colspan: 1 };
        }
      }
    },
    /**
     * 获取不合格产品排名数据
     */
    fetchAnalysisData() {
      this.analysisLoading = true;

      // 从详情数据中获取食品大类（cate1）
      let firstCategory = "";
      if (this.detailData && this.detailData.length > 0) {
        firstCategory = this.detailData[0].foodCategory; // foodCategory就是从cate1映射来的
      }

      // 构建查询参数
      const params = {
        year: this.selectedYear,
        firstCategory: firstCategory || "食用农产品", // 使用从详情数据中获取的食品大类
        quarter: this.selectedQuarter.replace("Q", "") || "3", // 去掉可能的Q前缀，确保只传递数字
        fourthlyCategory: this.selectedFoodType || undefined, // 如果有选择食品细类则传递
      };

      getTopFiveFailedProducts(params)
        .then((res) => {
          if (res.code === 200) {
            // 转换数据格式以适应当前表格
            this.analysisItems = (res.data || []).map((item, index) => ({
              name: item.foodName || "-", // 食品名称
              rate: `${item.noPass}/${item.total}`, // 不合格批次/总抽检批次
              percent: item.percentage || "0%", // 不合格率
              topRank: index < 3, // 前三名设为顶级排名
            }));
          } else {
            this.$message.error(res.msg || "获取不合格产品排名数据失败");
          }
        })
        .catch((err) => {
          console.error("获取不合格产品排名数据失败:", err);
          this.$message.error("获取不合格产品排名数据失败，请检查网络连接");
        })
        .finally(() => {
          this.analysisLoading = false;
        });
    },
    /**
     * 获取企业不合格数据
     */
    async fetchCompanyData(productName) {
      if (!productName) return;

      this.companyLoading = true;
      this.currentProductName = productName; // 保存当前产品名称
      try {
        const params = {
          year: this.selectedYear,
          firstCategory: this.detailData?.[0]?.foodCategory || "食用农产品",
          // quarter: this.selectedQuarter.replace("Q", ""),
          current: this.companyPagination.current,
          size: this.companyPagination.size,
          productName,
          sampleName: productName, // 添加样品名称，与产品名称相同
        };

        const res = await getProductionFailedList(params);

        if (res.code === 200 && res.data) {
          this.failedCompanyData = res.data.records || [];
          this.companyPagination.total = res.data.total || 0;
        } else {
          this.$message.error(res.msg || "获取企业不合格数据失败");
        }
      } catch (error) {
        console.error("获取企业不合格数据失败:", error);
        this.$message.error("获取企业不合格数据失败");
      } finally {
        this.companyLoading = false;
      }
    },
    /**
     * 处理企业数据分页变化
     */
    handleCompanyPageChange(page) {
      this.companyPagination.current = page;
      this.fetchCompanyData(this.currentProductName);
    },
    /**
     * 处理每页条数变化
     */
    handleCompanySizeChange(size) {
      this.companyPagination.size = size;
      this.companyPagination.current = 1;
      this.fetchCompanyData(this.currentProductName);
    },
    /**
     * 获取抽检统计数据
     */
    fetchSampleTestCount() {
      const params = {
        planTaskId: this.taskId,
      };

      getSampleTestCount(params)
        .then((res) => {
          if (res.code === 200 && Array.isArray(res.data)) {
            // 更新抽检标签页数据
            this.sampleTabs = res.data.map((item) => ({
              name: item.fccGradeFourName,
              count: item.num,
              label: item.fccGradeFourName,
            }));

            // 如果有数据，默认选中第一条
            if (this.sampleTabs.length > 0) {
              this.activeTab = this.sampleTabs[0].name;
              this.selectedSampleType = this.sampleTabs[0].name;
              // 获取对应的抽检详情数据
              // this.fetchSampleTestDetail(this.selectedSampleType);
            }
          } else {
            this.$message.error(res.msg || "获取抽检统计数据失败");
          }
        })
        .catch((err) => {
          console.error("获取抽检统计数据失败:", err);
          this.$message.error("获取抽检统计数据失败，请检查网络连接");

          // 添加模拟数据用于测试
          this.sampleTabs = [
            { name: "鲜湿河粉", count: 15, label: "鲜湿河粉" },
            { name: "干河粉", count: 8, label: "干河粉" },
            { name: "特级生抽", count: 12, label: "特级生抽" },
            { name: "普通老抽", count: 10, label: "普通老抽" },
          ];

          // 默认选中第一条
          if (this.sampleTabs.length > 0) {
            this.activeTab = this.sampleTabs[0].name;
            this.selectedSampleType = this.sampleTabs[0].name;
            // 模拟获取详情数据
            this.fetchSampleTestDetail();
          }
        });
    },
    /**
     * 切换抽检标签页
     */
    handleTabChange(tab) {
      if (typeof tab === "string") {
        this.selectedSampleType = tab;
      } else if (tab && tab.name) {
        this.selectedSampleType = tab.name;
      }

      // 使用模拟数据进行测试，根据选中的标签页生成对应的数据
      this.generateMockSampleData();
    },
    /**
     * 获取抽检详情数据
     */
    fetchSampleTestDetail() {
      // 如果没有选中的样品类型，则返回
      if (!this.selectedSampleType) return;

      this.sampleLoading = true;

      const params = {
        planTaskId: this.taskId,
        cate: this.selectedSampleType,
        period: this.currentPeriod,
        current: this.samplePagination.current,
        size: this.samplePagination.size,
      };

      getSampleTestDetail(params)
        .then((res) => {
          if (res.code === 200) {
            // 处理返回的数据
            this.sampleListData = res.data?.records || [];

            // 设置分页信息
            this.samplePagination.total = parseInt(res.data?.total || 0);
            this.samplePagination.current = parseInt(res.data?.current || 1);
            this.samplePagination.size = parseInt(res.data?.size || 10);
          } else {
            this.$message.error(res.msg || "获取抽检详情数据失败");
            this.sampleListData = [];
          }
        })
        .catch((err) => {
          console.error("获取抽检详情数据失败:", err);
          this.$message.error("获取抽检详情数据失败，使用模拟数据");

          // 生成模拟数据
          const mockData = [];

          // 根据不同的标签生成不同的样品数据
          for (let i = 1; i <= 15; i++) {
            mockData.push({
              sampleName: `${this.selectedSampleType}样品${i}`,
              unitName: `测试企业${i}有限公司`,
              spBusinessLicenseNo: `91330${i}00MA2GMYRX3${i}`,
              productionLicenseCode: `*********${i}2112${i}`,
              manufactureDat: `2023-0${(i % 10) + 1}-${i + 10}`,
              samplingPersonnel: `张三${
                i % 3 === 0 ? "、李四" : i % 3 === 1 ? "、王五" : ""
              }`,
            });
          }

          // 设置模拟数据和分页信息
          this.sampleListData = mockData.slice(
            (this.samplePagination.current - 1) * this.samplePagination.size,
            this.samplePagination.current * this.samplePagination.size
          );
          this.samplePagination.total = mockData.length;
        })
        .finally(() => {
          this.sampleLoading = false;
        });
    },
    /**
     * 处理抽样数据分页变化
     */
    handleSamplePageChange(page) {
      this.samplePagination.current = page;
      // 使用模拟数据进行测试
      this.generateMockSampleData();
    },

    /**
     * 处理抽样数据每页条数变化
     */
    handleSampleSizeChange(size) {
      this.samplePagination.size = size;
      this.samplePagination.current = 1;
      // 使用模拟数据进行测试
      this.generateMockSampleData();
    },
  },
};
</script>

<style lang="scss" scoped>
.task-detail-page {
  padding: 10px 0;
  background-color: #f0f2f5;
  min-height: calc(100vh - 120px);

  .task-card {
    margin-bottom: 20px;

    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title {
        font-size: 18px;
        font-weight: bold;
      }
    }

    .task-info-section {
      .basic-info-card {
        background-color: #f5f7fa;
        padding: 20px;
        border-radius: 4px;
        margin-bottom: 20px;

        .task-info-item {
          .info-label {
            color: #606266;
            margin-right: 10px;
          }

          .info-value {
            color: #303133;
          }
        }

        .section-title {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .title-text {
            font-size: 16px;
            color: #303133;
          }

          .task-status-wrapper {
            display: flex;
            flex-direction: column;
            align-items: flex-end;

            .task-status {
              color: #e6a23c;
              font-size: 14px;
              margin-bottom: 8px;
            }

            .complete-btn {
              font-size: 12px;
              padding: 6px 10px;
            }
          }
        }

        .task-desc {
          font-size: 15px;
          color: #303133;
          margin-bottom: 15px;
        }

        .task-meta {
          font-size: 15px;
          display: flex;
          gap: 20px;

          .meta-item {
            .label {
              color: #606266;
            }

            .value {
              color: #303133;
            }
          }
        }
      }

      .detail-section {
        margin-bottom: 30px;

        .section-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 15px;
          border-left: 4px solid #409eff;
          padding-left: 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .date-filter {
            display: flex;
          }
        }

        .table-container {
          width: 100%;
          overflow-x: auto;
        }

        .tabs-container {
          margin-bottom: 15px;
        }
      }

      .analysis-section {
        background-color: #f8f8f8;
        padding: 15px;
        border-radius: 4px;
        position: relative;

        .no-data-tip {
          text-align: center;
          padding: 20px 0;
          color: #909399;
          font-size: 14px;
        }

        .analysis-content {
          width: 60%;
        }

        .analysis-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
        }

        .analysis-filters {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .date-selectors {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-left: 10px;
        }

        .quarter-select {
          margin-left: 5px;
        }

        .analysis-title {
          font-weight: bold;
          margin-bottom: 15px;
        }

        .filters {
          margin-bottom: 15px;
          display: flex;
          align-items: center;

          .filter-label {
            margin-right: 10px;
          }
        }

        .ranking-table {
          margin-top: 15px;

          .item-num {
            width: 26px;
            height: 26px;
            color: #606266;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin: 0 auto;

            &.with-bg {
              background-color: #409eff;
              color: white;
              border-radius: 50%;
            }
          }
        }

        .percent-column {
          color: #e6a23c;
          font-weight: bold;
        }
      }
    }
  }
}

.popover-content {
  h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #409eff;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
  }

  .pagination-container {
    margin-top: 15px;
    text-align: right;
  }
}

/deep/ .detail-popover {
  max-width: 80vw;
}

/deep/ .borderless-tabs {
  .el-tabs__header {
    border-bottom: none;
  }

  .el-tabs__nav {
    border: none;
  }

  .el-tabs__item {
    border: none !important;
    background-color: transparent !important;
    margin-right: 15px;
    position: relative;

    &.is-active {
      color: #409eff;
      font-weight: bold;

      &:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background-color: #409eff;
      }
    }
  }
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}

.no-data-tip {
  text-align: center;
  padding: 20px 0;
  color: #909399;
  font-size: 14px;
}
</style>
