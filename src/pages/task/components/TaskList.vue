<template>
  <div class="task-list-page">
    <el-card class="task-card">
      <div slot="header" class="task-header">
        <span class="task-title">{{ pageTitle }}</span>
        <div class="time-filter" v-if="showTimeFilter">
          <el-radio-group
            v-model="timeRange"
            size="small"
            @change="handleTimeRangeChange"
          >
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="year">本年</el-radio-button>
            <el-radio-button label="month">本月</el-radio-button>
            <el-radio-button label="week">本周</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form
          :inline="true"
          :model="form"
          class="demo-form-inline"
          size="small"
        >
          <el-form-item label="任务来源：" style="margin-right: 20px">
            <el-select
              v-model="form.taskSource"
              placeholder="请选择任务来源"
              clearable
              @change="handleTaskSourceChange"
            >
              <el-option
                v-for="item in taskSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报送分类A：" style="margin-right: 20px">
            <el-select
              v-model="form.classA"
              placeholder="请选择"
              clearable
              @change="handleClassAChange"
              :disabled="!form.taskSource"
            >
              <el-option
                v-for="item in reportTypeAOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报送分类B：" style="margin-right: 20px">
            <el-select
              v-model="form.classB"
              placeholder="请选择"
              clearable
              :disabled="!form.classA"
            >
              <el-option
                v-for="item in reportTypeBOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="食品大类："
            v-if="type === 'pending'"
            style="margin-right: 20px"
          >
            <el-select v-model="form.cate1" placeholder="请选择" clearable>
              <el-option
                v-for="item in foodCategoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item style="margin-right: 0">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleSearch"
              size="small"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="handleReset" size="small"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <!-- 任务列表 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          style="width: 100%"
          v-loading="loading"
          :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="taskSource"
            label="任务来源"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="classA"
            label="报送分类A"
            align="center"
            min-width="150"
          ></el-table-column>
          <el-table-column
            prop="classB"
            label="报送分类B"
            align="center"
            min-width="150"
          ></el-table-column>
          <el-table-column
            prop="cate1"
            label="食品大类"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="cate2"
            label="食品亚类"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="cate3"
            label="食品次亚类"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="cate4"
            label="食品细类"
            align="center"
            min-width="120"
          ></el-table-column>

          <template v-if="type === 'pending'">
            <el-table-column
              prop="batch"
              label="批次数量"
              align="center"
              min-width="120"
            ></el-table-column>
            <el-table-column
              prop="useBatch"
              label="完成批次"
              align="center"
              min-width="120"
            ></el-table-column>
            <el-table-column
              prop="executionArea"
              label="任务执行区域"
              align="center"
              min-width="120"
            ></el-table-column>
            <el-table-column
              label="操作"
              width="200"
              align="center"
              fixed="right"
              class-name="operation-column"
            >
              <template slot-scope="scope">
                <div class="action-container">
                  <el-link
                    type="primary"
                    :underline="false"
                    icon="el-icon-tickets"
                    @click="handleComplete(scope.row)"
                    >完成任务</el-link
                  >
                  <el-link
                    type="primary"
                    :underline="false"
                    icon="el-icon-view"
                    class="action-link"
                    @click="handleDetail(scope.row)"
                    >任务详情</el-link
                  >
                </div>
              </template>
            </el-table-column>
          </template>

          <template v-else-if="type === 'completed'">
            <el-table-column
              prop="sampleNo"
              label="抽样编号"
              align="center"
              min-width="200"
            ></el-table-column>
            <el-table-column
              prop="finishDate"
              label="完成时间"
              align="center"
              min-width="120"
            ></el-table-column>
          </template>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.current"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 完成任务弹窗 -->
    <complete-task-modal
      :visible.sync="completeModalVisible"
      :plan-task-id="selectedTaskId"
      @success="handleCompleteSuccess"
    ></complete-task-modal>
  </div>
</template>

<script>
import {
  getTaskList,
  getClassTree,
  getFoodCategoryTree,
  getPendingTaskList,
  getCompletedTaskList,
  getFinishedTaskList,
} from "@/api/modules/task";
import CompleteTaskModal from "./CompleteTaskModal.vue";

export default {
  name: "TaskList",
  components: {
    CompleteTaskModal,
  },
  props: {
    type: {
      type: String,
      default: "pending", // 'pending' 或 'completed'
      validator: (value) => ["pending", "completed"].includes(value),
    },
  },
  data() {
    return {
      form: {
        classA: "",
        classB: "",
        cate1: "",
        taskSource: "",
      },
      taskSourceOptions: [], // 将从接口获取的数据转换为下拉选项
      // 下拉选项
      reportTypeAOptions: [],
      reportTypeBOptions: [],
      foodCategoryOptions: [],
      // 分类数据缓存
      classTreeData: [],
      // 表格数据
      tableData: [],
      // 分页信息
      pagination: {
        current: 1,
        size: 10,
        total: 0,
      },
      loading: false,
      timeRange: "", // 默认为空，表示全部
      completeModalVisible: false, // 完成任务弹窗显示状态
      selectedTaskId: "", // 选中的任务ID
    };
  },
  computed: {
    pageTitle() {
      return this.type === "pending" ? "待执行任务" : "已完成任务";
    },
    showTimeFilter() {
      return this.type === "completed";
    },
  },
  mounted() {
    this.fetchTaskList();
    this.fetchClassTree(); // 获取分类树数据
    this.fetchFoodCategoryTree(); // 获取食品大类数据
  },
  methods: {
    // 获取分类树数据
    fetchClassTree() {
      getClassTree({ isDeploy: 1 })
        .then((res) => {
          if (res.code === 200 && res.data) {
            this.classTreeData = res.data;

            // 处理任务来源选项
            this.taskSourceOptions = this.classTreeData.map((item) => ({
              value: item.name,
              label: item.name,
              children: item.children || [],
            }));
          } else {
            this.$message.error(res.msg || "获取分类数据失败");
          }
        })
        .catch((err) => {
          console.error("获取分类数据失败:", err);
          this.$message.error("获取分类数据失败，请检查网络连接");
        });
    },

    // 处理任务来源变化
    handleTaskSourceChange() {
      // 重置报送分类A和B
      this.form.classA = "";
      this.form.classB = "";
      this.reportTypeAOptions = [];
      this.reportTypeBOptions = [];

      // 根据选择的任务来源更新报送分类A选项
      if (this.form.taskSource) {
        const selectedTaskSource = this.taskSourceOptions.find(
          (item) => item.value === this.form.taskSource
        );
        if (selectedTaskSource && selectedTaskSource.children) {
          this.reportTypeAOptions = selectedTaskSource.children.map(
            (child) => ({
              value: child.name,
              label: child.name,
              children: child.children || [],
            })
          );
        } else {
          this.reportTypeAOptions = [];
        }
      } else {
        this.reportTypeAOptions = [];
      }
    },

    // 处理分类A变化
    handleClassAChange() {
      // 重置分类B
      this.form.classB = "";
      this.reportTypeBOptions = [];

      // 根据选择的分类A更新分类B选项
      if (this.form.classA && this.form.taskSource) {
        const selectedTaskSource = this.taskSourceOptions.find(
          (item) => item.value === this.form.taskSource
        );

        if (selectedTaskSource && selectedTaskSource.children) {
          const selectedClassA = selectedTaskSource.children.find(
            (item) => item.name === this.form.classA
          );

          if (selectedClassA && selectedClassA.children) {
            this.reportTypeBOptions = selectedClassA.children.map((child) => ({
              value: child.name,
              label: child.name,
            }));
          } else {
            this.reportTypeBOptions = [];
          }
        } else {
          this.reportTypeBOptions = [];
        }
      } else {
        this.reportTypeBOptions = [];
      }
    },

    // 获取食品大类树数据
    fetchFoodCategoryTree() {
      getFoodCategoryTree()
        .then((res) => {
          if (res.code === 200 && res.data) {
            this.foodCategoryOptions = res.data.map((item) => ({
              value: item.cateName,
              label: item.cateName,
            }));
          } else {
            this.$message.error(res.msg || "获取食品大类数据失败");
          }
        })
        .catch((err) => {
          console.error("获取食品大类数据失败:", err);
          this.$message.error("获取食品大类数据失败，请检查网络连接");
        });
    },

    // 获取任务列表
    fetchTaskList() {
      this.loading = true;
      const params = {
        ...this.form,
        pageNum: this.pagination.current,
        pageSize: this.pagination.size,
      };

      // 根据类型区分待执行和已完成任务
      if (this.type === "pending") {
        // 待执行任务，使用原有API
        params.isFinish = 0;

        getPendingTaskList(params)
          .then((res) => {
            if (res.code === 200) {
              // 处理后端返回的数据，按照表格显示需求映射字段
              this.tableData = res.data.records || [];
              this.pagination.total = Number(res.data.total || 0);
            } else {
              this.$message.error(res.msg || "获取任务列表失败");
            }
          })
          .catch((err) => {
            console.error("获取任务列表失败:", err);
            this.$message.error("获取任务列表失败，请检查网络连接");
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        // 已完成任务，使用新API
        // 如果是已完成任务，添加时间筛选参数
        if (this.timeRange) {
          let timeFilterValue;
          switch (this.timeRange) {
            case "year":
              timeFilterValue = 0; // 本年
              break;
            case "month":
              timeFilterValue = 1; // 本月
              break;
            case "week":
              timeFilterValue = 2; // 本周
              break;
            default:
              timeFilterValue = null;
          }

          if (timeFilterValue !== null) {
            params.timeFilter = timeFilterValue;
          }
        }

        // 使用新的API获取已完成任务
        getFinishedTaskList(params)
          .then((res) => {
            if (res.code === 200) {
              // 处理后端返回的数据，按照表格显示需求映射字段
              this.tableData = res.data.records || [];
              this.pagination.total = Number(res.data.total || 0);
              this.pagination.size = Number(res.data.size || 10);
              this.pagination.current = Number(res.data.current || 1);
            } else {
              this.$message.error(res.msg || "获取任务列表失败");
            }
          })
          .catch((err) => {
            console.error("获取已完成任务列表失败:", err);
            this.$message.error("获取任务列表失败，请检查网络连接");
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    // 搜索任务
    handleSearch() {
      this.pagination.current = 1;
      this.fetchTaskList();
    },
    // 重置筛选条件
    handleReset() {
      this.form = {
        taskSource: "",
        classA: "",
        classB: "",
        cate1: "",
      };
      this.reportTypeAOptions = [];
      this.reportTypeBOptions = [];
      this.pagination.current = 1;
      this.fetchTaskList();
    },
    // 改变每页条数
    handleSizeChange(val) {
      this.pagination.size = val;
      this.fetchTaskList();
    },
    // 切换页码
    handleCurrentChange(val) {
      this.pagination.current = val;
      this.fetchTaskList();
    },
    // 处理时间范围变化
    /**
     * 处理时间范围选择变化
     * timeFilter参数值含义:
     * 0 - 本年
     * 1 - 本月
     * 2 - 本周
     * 不传 - 全部
     */
    handleTimeRangeChange() {
      this.pagination.current = 1;
      this.fetchTaskList();
    },
    // 完成任务
    handleComplete(row) {
      this.selectedTaskId = row.id;
      this.completeModalVisible = true;
    },
    // 任务完成成功回调
    handleCompleteSuccess() {
      this.fetchTaskList();
    },
    // 查看详情
    handleDetail(row) {
      // 使用编程式导航跳转到任务详情页面，并传递任务ID和批次信息
      this.$router.push({
        name: "task-detail",
        params: {
          id: row.id,
          batchTotal: row.batch,
          batchCompleted: row.useBatch,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.task-list-page {
  padding: 10px 0;
  background-color: #f0f2f5;
  min-height: calc(100vh - 120px);

  .task-card {
    margin-bottom: 20px;

    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .task-title {
        font-size: 18px;
        font-weight: bold;
      }

      .time-filter {
        .el-radio-button {
          margin-left: 0;
        }

        /deep/ .el-radio-group {
          display: flex;
        }

        /deep/ .el-radio-button__inner {
          border-radius: 0;
          padding: 8px 15px;
          font-size: 13px;
        }

        /deep/ .el-radio-button:first-child .el-radio-button__inner {
          border-left: 1px solid #dcdfe6;
          border-radius: 4px 0 0 4px;
        }

        /deep/ .el-radio-button:last-child .el-radio-button__inner {
          border-radius: 0 4px 4px 0;
        }

        /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
          border-color: #2c5072;
          box-shadow: -1px 0 0 0 #2c5072;
        }
      }
    }

    .filter-section {
      margin-bottom: 20px;
    }

    .table-container {
      width: 100%;
      overflow-x: auto;
    }

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

.action-container {
  white-space: nowrap;
  display: flex;
  justify-content: center;
}

.action-link {
  margin-left: 20px;
}

/deep/ .el-link {
  font-weight: normal;
}

/deep/ .operation-column {
  border-bottom: 1px solid #ebeef5;
}

/deep/ .el-table__fixed-right {
  height: 100% !important;
  .el-table__fixed-body-wrapper {
    height: 100% !important;
  }
  .operation-column {
    border-bottom: 1px solid #ebeef5;
  }
}

/deep/ .el-table__body-wrapper {
  .operation-column {
    border-bottom: 1px solid #ebeef5;
  }
}

/deep/ .el-table__header {
  th {
    &:last-child {
      transform: translateY(-1px) !important;
    }
  }
}
</style>
