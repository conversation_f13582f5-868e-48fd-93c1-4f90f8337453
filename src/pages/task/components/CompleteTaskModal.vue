<template>
  <el-dialog
    title="完成任务"
    :visible.sync="dialogVisible"
    width="700px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="complete-task-content">
      <div class="info-text">
        <el-alert
          title="请选择需要标记为已完成的抽样单，可多选"
          type="info"
          :closable="false"
          show-icon
        >
        </el-alert>
      </div>

      <el-table
        ref="sampleCodeTable"
        :data="filteredSampleCodeList"
        style="width: 100%"
        v-loading="loading"
        :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
      >
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="sampleNo"
          label="抽样单编号"
          align="center"
          min-width="180"
        >
          <template slot-scope="scope">
            <template
              v-if="scope.row.sampleNo && scope.row.sampleNo.trim().length > 0"
            >
              {{ scope.row.sampleNo }}
            </template>
            <span v-else class="no-sample-no">未填写</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="team"
          label="抽样人员"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleCompleteSingle(scope.row)"
            >
              完成
            </el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="empty-text">
            <el-empty
              description="暂无待完成的抽样单"
              :image-size="80"
            ></el-empty>
          </div>
        </template>
      </el-table>

      <div class="pagination-container">
        <div class="page-info">共 {{ pagination.total }} 条</div>
        <div class="page-size-select">
          <el-select
            v-model="pagination.size"
            size="mini"
            @change="handleSizeChange"
            style="width: 100px"
          >
            <el-option
              v-for="item in [10, 20, 30, 50]"
              :key="item"
              :label="`${item}条/页`"
              :value="item"
            ></el-option>
          </el-select>
        </div>
        <div class="page-buttons">
          <el-button
            size="mini"
            icon="el-icon-arrow-left"
            @click="handlePrevPage"
            :disabled="pagination.current <= 1"
          ></el-button>
          <el-button
            size="mini"
            icon="el-icon-arrow-right"
            @click="handleNextPage"
            :disabled="pagination.current >= pagination.pages"
          ></el-button>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
    </div>

    <!-- 抽样单号输入弹窗 -->
    <el-dialog
      title="填写抽样单编号"
      :visible.sync="sampleNoDialogVisible"
      width="450px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form :model="sampleNoForm" ref="sampleNoForm" :rules="sampleNoRules">
        <el-form-item label="抽样单编号" prop="sampleNo" label-width="100px">
          <el-input
            v-model="sampleNoForm.sampleNo"
            placeholder="请输入抽样单编号"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="sampleNoDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitSampleNo">确定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
/**
 * 完成任务模态框组件
 * @module CompleteTaskModal
 */
import {
  getSampleCodeList,
  updateSampleFinishStatus,
} from "@/api/modules/task";
import { http } from "@/api/request";

export default {
  name: "CompleteTaskModal",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    planTaskId: {
      type: [String, Number],
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      sampleCodeList: [],
      pagination: {
        current: 1,
        size: 10,
        total: 0,
        pages: 1,
      },
      sampleNoDialogVisible: false,
      sampleNoForm: {
        sampleNo: "",
      },
      sampleNoRules: {
        sampleNo: [
          { required: true, message: "请输入抽样单编号", trigger: "blur" },
        ],
      },
      currentRow: null,
    };
  },
  computed: {
    /**
     * 控制对话框显示
     */
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    /**
     * 过滤后的抽样单列表
     */
    filteredSampleCodeList() {
      return this.sampleCodeList.filter((item) => item.isFinish === "0");
    },
  },
  watch: {
    /**
     * 监听对话框显示状态
     */
    visible(val) {
      if (val && this.planTaskId) {
        this.fetchSampleCodeList();
      } else {
        // 关闭时重置状态
        this.resetState();
      }
    },
  },
  methods: {
    /**
     * 获取抽样单列表
     */
    fetchSampleCodeList() {
      this.loading = true;
      const params = {
        planTaskId: this.planTaskId,
        pageNum: this.pagination.current,
        pageSize: this.pagination.size,
      };

      getSampleCodeList(params)
        .then((res) => {
          if (res.code === 200) {
            // 处理数据，确保每一行数据的sampleNo属性存在且非空字符串
            this.sampleCodeList = (res.data.records || []).map((item) => {
              // 确保sampleNo存在，如果为null或undefined，则设为空字符串
              if (item.sampleNo === null || item.sampleNo === undefined) {
                item.sampleNo = "";
              }
              return item;
            });
            console.log("抽样单列表数据:", this.sampleCodeList);
            this.pagination.total = Number(res.data.total || 0);
            this.pagination.pages = Number(res.data.pages || 1);
          } else {
            this.$message.error(res.msg || "获取抽样单列表失败");
          }
        })
        .catch((err) => {
          console.error("获取抽样单列表失败:", err);
          this.$message.error("获取抽样单列表失败，请检查网络连接");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /**
     * 处理完成单个抽样单
     */
    handleCompleteSingle(row) {
      // 如果没有抽样单号，则打开抽样单号输入弹窗
      if (!row.sampleNo) {
        this.currentRow = row;
        this.sampleNoForm.sampleNo = "";
        this.openSampleNoDialog();
      } else {
        // 如果已有抽样单号，直接完成该抽样单，并传递抽样单号
        this.loading = true;
        const sampleNo = row.sampleNo.trim();

        updateSampleFinishStatus([row.id], sampleNo)
          .then((res) => {
            if (res.code === 200) {
              this.$message.success("抽样单完成状态更新成功");
              this.fetchSampleCodeList(); // 刷新列表
              this.$emit("success");
            } else {
              this.$message.error(res.msg || "更新抽样单完成状态失败");
            }
          })
          .catch((err) => {
            console.error("更新抽样单完成状态失败:", err);
            this.$message.error("更新抽样单完成状态失败，请检查网络连接");
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    /**
     * 打开抽样单号输入弹窗
     */
    openSampleNoDialog() {
      this.sampleNoDialogVisible = true;
    },
    /**
     * 提交抽样单编号
     */
    submitSampleNo() {
      this.$refs.sampleNoForm.validate((valid) => {
        if (valid) {
          const sampleNo = this.sampleNoForm.sampleNo;

          // 更新当前行的抽样单号
          if (this.currentRow) {
            this.loading = true;

            // 使用更新后的API函数，传入sampleNo参数
            updateSampleFinishStatus([this.currentRow.id], sampleNo)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success("抽样单完成状态更新成功");
                  this.fetchSampleCodeList(); // 刷新列表
                  this.$emit("success");
                } else {
                  this.$message.error(res.msg || "更新抽样单完成状态失败");
                }
              })
              .catch((err) => {
                console.error("操作失败:", err);
                this.$message.error("操作失败，请检查网络连接");
              })
              .finally(() => {
                this.loading = false;
                this.sampleNoDialogVisible = false;
              });
          }
        } else {
          // 表单验证失败
          return false;
        }
      });
    },
    /**
     * 处理关闭对话框
     */
    handleClose() {
      this.resetState();
      this.dialogVisible = false;
    },
    /**
     * 重置状态
     */
    resetState() {
      this.sampleCodeList = [];
      this.pagination.current = 1;
    },
    /**
     * 改变每页条数
     */
    handleSizeChange(val) {
      this.pagination.size = val;
      this.pagination.current = 1;
      this.fetchSampleCodeList();
    },
    /**
     * 切换页码
     */
    handleCurrentChange(val) {
      this.pagination.current = val;
      this.fetchSampleCodeList();
    },
    /**
     * 上一页
     */
    handlePrevPage() {
      if (this.pagination.current > 1) {
        this.pagination.current -= 1;
        this.fetchSampleCodeList();
      }
    },
    /**
     * 下一页
     */
    handleNextPage() {
      if (this.pagination.current < this.pagination.pages) {
        this.pagination.current += 1;
        this.fetchSampleCodeList();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.complete-task-content {
  margin-bottom: 20px;

  .info-text {
    margin-bottom: 15px;
  }

  .pagination-container {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .page-info {
      margin-right: 10px;
      font-size: 13px;
      color: #606266;
    }

    .page-size-select {
      margin-right: 10px;
    }

    .page-buttons {
      display: flex;
      align-items: center;

      .el-button {
        margin-left: 5px;
        padding: 5px 8px;
      }
    }
  }
}

.el-tag {
  margin-right: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.no-sample-no {
  color: #f56c6c;
  font-size: 12px;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 500;
  height: 40px;
  padding: 6px 0;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-select-dropdown__item {
  padding: 0 12px;
  line-height: 30px;
  font-size: 13px;
}

/deep/ .el-button--mini {
  padding: 7px 10px;
}
</style>
