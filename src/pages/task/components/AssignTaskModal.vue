<template>
  <el-dialog
    title="任务分配"
    :visible.sync="visible"
    width="500px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="claimForm"
      label-width="120px"
      size="small"
    >
      <el-form-item label="抽样队：" prop="teamId">
        <el-select
          v-model="form.teamId"
          placeholder="请选择抽样队"
          style="width: 100%"
          @change="handleTeamChange"
          size="small"
        >
          <el-option
            v-for="item in teamOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="抽样人员：" prop="memberIds">
        <el-select
          v-model="form.memberIds"
          placeholder="请选择至少两名抽样人员"
          style="width: 100%"
          size="small"
          multiple
          collapse-tags
          :disabled="!form.teamId"
        >
          <el-option
            v-for="item in memberOptions"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分配批次数：" prop="batchNumber">
        <el-input
          v-model.number="form.batchNumber"
          placeholder="请输入分配批次数"
          type="number"
          min="1"
          :max="maxAvailableBatch"
          size="small"
          @change="handleBatchNumberChange"
        ></el-input>
        <div class="form-tip">
          批次必须大于0且不超过可用总数({{ maxAvailableBatch }}批)
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="small">取消</el-button>
      <el-button
        type="primary"
        @click="submitForm"
        :loading="loading"
        size="small"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  getAllUsers,
  collectingTasks,
  getSampleTeamList,
} from "@/api/modules/task";

export default {
  name: "AssignTaskModal",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    taskId: {
      type: String,
      default: "",
    },
    taskData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      form: {
        teamId: "",
        teamCode: "",
        memberIds: [],
        batchNumber: 1,
        taskId: "",
      },
      rules: {
        teamId: [
          { required: true, message: "请选择抽样队", trigger: "change" },
        ],
        memberIds: [
          {
            required: true,
            message: "请选择抽样人员",
            trigger: "change",
            type: "array",
          },
          {
            validator: (rule, value, callback) => {
              if (!value || value.length < 2) {
                callback(new Error("请至少选择两名抽样人员"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        batchNumber: [
          { required: true, message: "请输入分配批次", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value === "" || value === null || value === undefined) {
                callback(new Error("请输入分配批次"));
              } else if (parseInt(value) <= 0) {
                callback(new Error("批次必须大于0"));
              } else if (
                this.maxAvailableBatch > 0 &&
                parseInt(value) > this.maxAvailableBatch
              ) {
                callback(
                  new Error(`批次不能超过可用总数(${this.maxAvailableBatch})`)
                );
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      teamOptions: [],
      memberOptions: [],
      loading: false,
      maxAvailableBatch: 0,
    };
  },
  watch: {
    visible(newVal) {
      console.log("Modal visibility changed:", newVal);
      if (newVal) {
        this.resetForm();
        this.setDefaultAssignType();
        // 获取抽样队列表
        this.fetchTeams();
        // 立即更新最大可用批次
        this.updateMaxAvailableBatch();
        // 使用 $nextTick 确保 DOM 更新后再次更新最大可用批次
        this.$nextTick(() => {
          this.updateMaxAvailableBatch();
        });
      }
    },
    "form.teamId"(val) {
      if (val) {
        this.form.memberIds = [];
      }
      this.updateMaxAvailableBatch();
    },
    // 监听taskData的变化，更新最大可用批次数
    taskData: {
      handler(newVal) {
        if (newVal && this.visible) {
          console.log("任务数据更新:", newVal);
          this.updateMaxAvailableBatch();
        }
      },
      deep: true,
    },
  },
  computed: {
    selectedTeam() {
      return (
        this.teamOptions.find((team) => team.id === this.form.teamId) || null
      );
    },
    canSubmit() {
      return (
        this.form.teamId &&
        this.form.memberIds &&
        this.form.memberIds.length > 0
      );
    },
  },
  methods: {
    // 更新最大可用批次数
    updateMaxAvailableBatch() {
      if (!this.taskData || !this.taskData.id) {
        this.maxAvailableBatch = 0;
        return;
      }

      // 使用任务数据中的可用总数
      if (this.taskData.cateAvailableTotal !== undefined) {
        this.maxAvailableBatch =
          parseInt(this.taskData.cateAvailableTotal) || 0;
        console.log("从任务数据直接获取最大可用批次:", this.maxAvailableBatch);
      } else {
        // 获取已分配给该团队的批次数量
        const assignedBatchesCount = this.getAssignedBatchesCount(
          this.form.teamId
        );

        // 计算最大可用批次 = 总批次 - 已分配批次
        this.maxAvailableBatch = Math.max(
          0,
          (this.taskData.total_batches || 0) - assignedBatchesCount
        );
      }

      console.log(
        "最大可用批次:",
        this.maxAvailableBatch,
        "任务数据:",
        this.taskData
      );

      // 如果当前设置的批次数大于最大可用批次，则调整为最大可用批次
      if (
        this.maxAvailableBatch > 0 &&
        this.form.batchNumber > this.maxAvailableBatch
      ) {
        this.form.batchNumber = this.maxAvailableBatch;
      } else if (this.maxAvailableBatch <= 0) {
        this.form.batchNumber = 1; // 设置默认值
      }
    },

    /**
     * 获取已分配给指定团队的批次数量
     * @param {string} teamId 团队ID
     * @returns {number} 已分配批次数
     */
    getAssignedBatchesCount(teamId) {
      if (!this.taskData || !this.taskData.assigned_teams) {
        return 0;
      }

      // 找到当前选择的团队在已分配列表中的记录
      const assignedTeam = this.taskData.assigned_teams.find(
        (team) => team.id === teamId
      );
      return assignedTeam ? assignedTeam.batches : 0;
    },

    // 处理批次数变化
    handleBatchNumberChange(value) {
      // 转换为数字
      let numValue = parseInt(value) || 0;

      // 确保不小于1
      if (numValue < 1) {
        numValue = 1;
        this.form.batchNumber = 1;
      }

      // 确保不超过最大可用批次数
      if (numValue > this.maxAvailableBatch) {
        this.$message.warning(
          `批次数已自动调整为最大可用总数: ${this.maxAvailableBatch}`
        );
        this.form.batchNumber = this.maxAvailableBatch;
      }
    },

    // 获取抽样队列表
    fetchTeams() {
      this.loading = true;
      getSampleTeamList({ current: 1, size: 9999 })
        .then((res) => {
          if (res.code === 200) {
            this.teamOptions = res.data.records || res.data || [];
            this.maxAvailableBatch = res.data.maxAvailableBatch || 0;
          } else {
            this.$message.error(res.msg || "获取抽样队列表失败");
          }
        })
        .catch((err) => {
          console.error("获取抽样队列表失败:", err);
          this.$message.error("获取抽样队列表失败，请检查网络连接");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 选择抽样队
    handleTeamChange(teamId) {
      this.form.memberIds = [];
      this.memberOptions = [];
      this.form.teamCode = "";

      if (!teamId) {
        return;
      }

      const selectedTeam = this.teamOptions.find((team) => team.id === teamId);
      if (selectedTeam) {
        console.log("选中的队伍:", selectedTeam);
        this.form.teamCode =
          selectedTeam.code || selectedTeam.teamCode || teamId;

        const memberIdsStr = selectedTeam.teamMemberIds || "";
        const memberNamesStr = selectedTeam.teamMember || "";

        if (memberIdsStr && memberNamesStr) {
          const ids = memberIdsStr.split(",");
          const names = memberNamesStr.split(",");

          console.log("解析出的IDs:", ids);
          console.log("解析出的Names:", names);

          if (ids.length === names.length) {
            this.memberOptions = ids.map((id, index) => ({
              id: id.trim(),
              label: names[index].trim() || `队员ID:${id.trim()}`,
            }));
            console.log("生成的人员选项:", this.memberOptions);
          } else {
            console.warn(
              "抽样队数据错误：队员ID和队员名称数量不匹配",
              selectedTeam
            );
            this.$message.warning("该抽样队队员数据异常，请检查");
            this.memberOptions = [];
          }
        } else {
          console.warn("抽样队数据错误：缺少队员ID或队员名称", selectedTeam);
          this.$message.warning("该抽样队未配置队员信息");
          this.memberOptions = [];
        }
      } else {
        console.warn("未找到选中的抽样队，ID:", teamId);
      }
    },
    // 提交表单
    submitForm() {
      this.$refs.claimForm.validate((valid) => {
        if (valid) {
          // 表单验证
          if (!this.canSubmit) {
            this.$message.warning("请选择团队和团队成员");
            return;
          }

          if (
            this.form.batchNumber <= 0 ||
            this.form.batchNumber > this.maxAvailableBatch
          ) {
            this.$message.warning(
              `批次数必须大于0且不大于${this.maxAvailableBatch}`
            );
            return;
          }

          this.loading = true;

          // 获取选中的人员姓名列表
          const selectedMemberNames = this.form.memberIds
            .map((id) => {
              const member = this.memberOptions.find((m) => m.id === id);
              return member ? member.label : "";
            })
            .filter((name) => name);

          // 构建提交数据
          const submitData = {
            // 任务标识
            hnyId: this.taskData.id || this.taskId,
            hnyCateId: this.taskData.id || "",
            taskId: this.taskData.taskId || "",
            taskSource: this.taskData.taskSource || "",

            // 团队信息
            teamId: this.form.teamId,
            teamCode: this.form.teamCode,
            teamUserId: this.form.memberIds.join(","),
            teamUser: selectedMemberNames.join(","),

            // 批次信息
            batch: parseInt(this.form.batchNumber),

            // 分类信息
            classA: this.taskData.classA || "",
            classB: this.taskData.classB || "",
            cate1: this.taskData.cate1 || "",
            cate2: this.taskData.cate2 || "",
            cate3: this.taskData.cate3 || "",
            cate4: this.taskData.cate4 || "",

            // 其他信息
            sampleLink: this.taskData.stepTypeName || "",
            executionArea: this.taskData.executionArea || "",
          };

          console.log("提交数据:", submitData);

          // 调用API提交数据
          collectingTasks(submitData)
            .then((response) => {
              if (response.code === 200) {
                this.$message.success("任务分配成功");
                this.$emit("success");
                this.handleClose();
              } else {
                this.$message.error(response.msg || "分配任务失败");
              }
            })
            .catch((error) => {
              console.error("分配任务失败:", error);
              this.$message.error(
                "分配任务失败: " + (error.message || "未知错误")
              );
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    // 重置表单
    resetForm() {
      if (this.$refs.claimForm) {
        this.$refs.claimForm.resetFields();
      }
      this.form = {
        teamId: undefined,
        teamCode: "",
        memberIds: [],
        batchNumber: 1,
      };
      this.maxAvailableBatch = 0;
      this.loading = false;
      this.memberOptions = [];
    },
    /**
     * 处理取消操作
     */
    handleCancel() {
      this.resetForm();
      this.$emit("cancel");
    },
    /**
     * 关闭弹窗
     */
    handleClose() {
      this.resetForm();
      this.$emit("update:visible", false);
    },
    /**
     * 根据任务状态设置默认分配方式
     */
    setDefaultAssignType() {
      // 根据业务逻辑设置默认值
      this.form.batchNumber = 1;
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.2;
}
</style>
