<template>
  <div class="task-page">
    <el-card class="task-card">
      <div slot="header" class="task-header">
        <span class="task-title">任务大厅</span>
      </div>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form
          :inline="true"
          :model="form"
          class="demo-form-inline"
          size="small"
        >
          <el-form-item label="任务来源：" style="margin-right: 20px">
            <el-select
              v-model="form.taskSource"
              placeholder="请选择任务来源"
              clearable
              @change="handleTaskSourceChange"
            >
              <el-option
                v-for="item in taskSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报送分类A：" style="margin-right: 20px">
            <el-select
              v-model="form.classA"
              placeholder="请选择"
              clearable
              @change="handleClassAChange"
              :disabled="!form.taskSource"
            >
              <el-option
                v-for="item in reportTypeAOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报送分类B：" style="margin-right: 20px">
            <el-select
              v-model="form.classB"
              placeholder="请选择"
              clearable
              @change="handleClassBChange"
              :disabled="!form.classA"
            >
              <el-option
                v-for="item in reportTypeBOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="任务名称：" style="margin-right: 20px">
            <el-select
              v-model="form.taskName"
              placeholder="请选择任务名称"
              clearable
              style="width: 220px"
              :disabled="!form.classB"
            >
              <el-option
                v-for="item in taskNameOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="食品大类：" style="margin-right: 20px">
            <el-select v-model="form.cate1" placeholder="请选择" clearable>
              <el-option
                v-for="item in foodCategoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="任务执行区域：" style="margin-right: 20px">
            <el-input
              v-model="form.executionArea"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item style="margin-right: 0">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleSearch"
              size="small"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="handleReset" size="small"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <!-- 任务详情 -->
      <div class="task-detail-section">
        <div class="task-title">任务详情</div>

        <!-- 任务统计信息 -->
        <div class="task-statistics">
          <!-- 第一块：完成批次数/部署批次数 -->
          <div class="stats-item">
            <div class="stats-value primary-color">
              {{ taskStats.finished }}/{{ taskStats.total }}
            </div>
            <div class="stats-label">完成批次数/部署批次数</div>
            <div class="stats-progress">
              <el-progress
                :percentage="taskStats.percentage"
                :color="customColorMethod"
                :show-text="false"
                :stroke-width="5"
              ></el-progress>
              <span class="stats-percentage">{{ taskStats.percentage }}%</span>
            </div>
          </div>

          <!-- 第二块：今日完成批次 -->
          <div class="stats-item">
            <div class="stats-value success-color">
              {{ taskStats.todayFinished }}
            </div>
            <div class="stats-label">今日完成批次</div>
            <div class="stats-compare">
              <span :class="taskStats.trend < 0 ? 'down-trend' : 'up-trend'">
                <i
                  :class="
                    taskStats.trend < 0
                      ? 'el-icon-caret-bottom'
                      : 'el-icon-caret-top'
                  "
                ></i>
                环比昨日 {{ Math.abs(taskStats.trend) }}%
              </span>
            </div>
            <div class="stats-icon">
              <i class="el-icon-tickets"></i>
            </div>
          </div>

          <!-- 第三块：预计完成日期 -->
          <div class="stats-item">
            <div class="stats-value warning-color">
              {{ taskStats.expectedEndDate }}
            </div>
            <div class="stats-label">预计完成日期</div>
            <div class="stats-sub-label">
              计划完成日期：{{ taskStats.planEndDate }}
            </div>
            <div class="stats-days-left">
              剩余完成时间：<span>{{ taskStats.daysLeft }}</span
              >天
            </div>
            <div class="stats-icon">
              <i class="el-icon-date"></i>
            </div>
          </div>
        </div>

        <!-- 任务列表 -->
        <div class="table-container">
          <el-table
            class="task-hall__table"
            :data="tableData"
            style="width: 100%"
            v-loading="loading"
            :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
          >
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="taskSource"
              label="任务来源"
              align="center"
              min-width="120"
            >
              <template slot-scope="scope">
                {{ scope.row.taskSource || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="classA"
              label="报送分类A"
              align="center"
              min-width="120"
            >
              <template slot-scope="scope">
                {{ scope.row.classA || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="classB"
              label="报送分类B"
              align="center"
              min-width="120"
            >
              <template slot-scope="scope">
                {{ scope.row.classB || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="taskName"
              label="任务名称"
              align="center"
              min-width="120"
            >
              <template slot-scope="scope">
                {{ scope.row.taskName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="cate1"
              label="食品大类"
              align="center"
              min-width="100"
            >
              <template slot-scope="scope">
                {{ scope.row.cate1 || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="cate2"
              label="食品亚类"
              align="center"
              min-width="100"
            >
            </el-table-column>
            <el-table-column
              prop="cate3"
              label="食品品种"
              align="center"
              min-width="100"
            >
            </el-table-column>
            <el-table-column
              prop="cate4"
              label="食品细类"
              align="center"
              min-width="100"
            >
            </el-table-column>
            <el-table-column
              prop="stepTypeName"
              label="抽样环节"
              align="center"
              min-width="100"
            >
              <template slot-scope="scope">
                {{ scope.row.stepTypeName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="executionArea"
              label="任务执行区域"
              align="center"
              min-width="120"
            ></el-table-column>
            <el-table-column
              prop="availableTimes"
              label="完成数/可用总数/计划总数"
              align="center"
              min-width="160"
            ></el-table-column>
            <el-table-column
              label="操作"
              width="140"
              align="center"
              fixed="right"
            >
              <template slot-scope="scope">
                <div class="action-container">
                  <el-link
                    v-if="shouldShowAssignButton(scope.row.availableTimes)"
                    type="primary"
                    :underline="false"
                    icon="el-icon-s-unfold"
                    @click="handleClaim(scope.row)"
                    >分配</el-link
                  >
                  <!-- <el-link
                    type="danger"
                    :underline="false"
                    icon="el-icon-delete"
                    class="action-link"
                    @click="handleDelete(scope.row)"
                    >删除</el-link
                  > -->
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.current"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          ></el-pagination>
        </div>
      </div>
    </el-card>

    <!-- 任务分配弹窗 -->
    <assign-task-modal
      :visible.sync="claimModalVisible"
      :task-id="selectedTaskId"
      :task-data="selectedTaskData"
      @success="handleClaimSuccess"
    ></assign-task-modal>
  </div>
</template>

<script>
import {
  getTaskList,
  deleteTask,
  getTaskStatistics,
  getClassTree,
  getFoodCategoryTree,
} from "@/api/modules/task";
import AssignTaskModal from "./components/AssignTaskModal.vue";

export default {
  name: "TaskHall",
  components: {
    AssignTaskModal,
  },
  data() {
    return {
      form: {
        taskSource: "",
        classA: "",
        classB: "",
        taskName: "",
        cate1: "",
        executionArea: "",
      },
      // 下拉选项
      taskSourceOptions: [], // 将从接口获取的数据转换为下拉选项
      reportTypeAOptions: [],
      reportTypeBOptions: [],
      taskNameOptions: [], // 任务名称选项
      // 分类数据缓存
      classTreeData: [],
      foodCategoryOptions: [],
      // 任务统计数据
      taskStats: {
        total: 0,
        finished: 0,
        todayFinished: 0,
        percentage: 0,
        trend: 0,
        expectedEndDate: "--",
        planEndDate: "--",
        daysLeft: 0,
      },
      // 表格数据
      tableData: [],
      // 分页信息
      pagination: {
        current: 1,
        size: 10,
        total: 0,
      },
      loading: false,
      claimModalVisible: false,
      selectedTaskId: "",
      selectedTaskData: {},
    };
  },
  mounted() {
    this.fetchTaskList(); // 初始加载任务列表
    this.fetchTaskStatistics(); // 获取统计数据
    this.fetchClassTree(); // 获取分类树数据
    this.fetchFoodCategoryTree(); // 获取食品大类数据
  },
  methods: {
    // 获取分类树数据
    fetchClassTree() {
      getClassTree({ isDeploy: 1 })
        .then((res) => {
          if (res.code === 200 && res.data) {
            this.classTreeData = res.data;

            // 处理任务来源选项
            this.taskSourceOptions = this.classTreeData.map((item) => ({
              value: item.name,
              label: item.name,
              children: item.children || [],
            }));
          } else {
            this.$message.error(res.msg || "获取分类数据失败");
          }
        })
        .catch((err) => {
          console.error("获取分类数据失败:", err);
          this.$message.error("获取分类数据失败，请检查网络连接");
        });
    },

    // 处理任务来源变化
    handleTaskSourceChange() {
      // 重置报送分类A、B和任务名称
      this.form.classA = "";
      this.form.classB = "";
      this.form.taskName = "";
      this.reportTypeAOptions = [];
      this.reportTypeBOptions = [];
      this.taskNameOptions = [];

      // 根据选择的任务来源更新报送分类A选项
      if (this.form.taskSource) {
        const selectedTaskSource = this.taskSourceOptions.find(
          (item) => item.value === this.form.taskSource
        );
        if (selectedTaskSource && selectedTaskSource.children) {
          this.reportTypeAOptions = selectedTaskSource.children.map(
            (child) => ({
              value: child.name,
              label: child.name,
              children: child.children || [],
            })
          );
        } else {
          this.reportTypeAOptions = [];
        }
      } else {
        this.reportTypeAOptions = [];
      }
    },

    // 处理分类A变化
    handleClassAChange() {
      // 重置分类B和任务名称
      this.form.classB = "";
      this.form.taskName = "";
      this.reportTypeBOptions = [];
      this.taskNameOptions = [];

      // 根据选择的分类A更新分类B选项
      if (this.form.classA && this.form.taskSource) {
        const selectedTaskSource = this.taskSourceOptions.find(
          (item) => item.value === this.form.taskSource
        );

        if (selectedTaskSource && selectedTaskSource.children) {
          const selectedClassA = selectedTaskSource.children.find(
            (item) => item.name === this.form.classA
          );

          if (selectedClassA && selectedClassA.children) {
            this.reportTypeBOptions = selectedClassA.children.map((child) => ({
              value: child.name,
              label: child.name,
              children: child.children || [],
            }));
          } else {
            this.reportTypeBOptions = [];
          }
        } else {
          this.reportTypeBOptions = [];
        }
      } else {
        this.reportTypeBOptions = [];
      }
    },
    // 处理分类B变化
    handleClassBChange() {
      // 重置任务名称
      this.form.taskName = "";
      this.taskNameOptions = [];

      // 从级联数据中获取任务名称列表
      if (this.form.taskSource && this.form.classA && this.form.classB) {
        // 先找到当前选中的任务来源
        const selectedTaskSource = this.taskSourceOptions.find(
          (item) => item.value === this.form.taskSource
        );

        if (selectedTaskSource && selectedTaskSource.children) {
          // 找到当前选中的classA
          const selectedClassA = selectedTaskSource.children.find(
            (item) => item.name === this.form.classA
          );

          if (selectedClassA && selectedClassA.children) {
            // 找到当前选中的classB
            const selectedClassB = selectedClassA.children.find(
              (item) => item.name === this.form.classB
            );

            if (selectedClassB && selectedClassB.children) {
              // 转换任务名称列表为下拉选项格式
              this.taskNameOptions = selectedClassB.children.map((item) => ({
                value: item.name,
                label: item.name,
              }));
            } else {
              this.taskNameOptions = [];
            }
          } else {
            this.taskNameOptions = [];
          }
        } else {
          this.taskNameOptions = [];
        }
      } else {
        this.taskNameOptions = [];
      }
    },
    // 获取食品大类树数据
    fetchFoodCategoryTree() {
      getFoodCategoryTree()
        .then((res) => {
          if (res.code === 200 && res.data) {
            // 只获取第一层数据作为食品大类选项
            this.foodCategoryOptions = res.data.map((item) => ({
              value: item.cateName,
              label: item.cateName,
            }));
          } else {
            this.$message.error(res.msg || "获取食品大类数据失败");
          }
        })
        .catch((err) => {
          console.error("获取食品大类数据失败:", err);
          this.$message.error("获取食品大类数据失败，请检查网络连接");
        });
    },
    // 获取任务列表
    fetchTaskList() {
      this.loading = true;

      const params = {};

      if (this.form.taskSource) params.taskSource = this.form.taskSource;
      if (this.form.classA) params.classA = this.form.classA;
      if (this.form.classB) params.classB = this.form.classB;
      if (this.form.taskName) params.taskName = this.form.taskName;
      if (this.form.cate1) params.cate1 = this.form.cate1;
      if (this.form.executionArea)
        params.executionArea = this.form.executionArea;

      // 分页
      params.current = this.pagination.current;
      params.size = this.pagination.size;

      getTaskList(params)
        .then((res) => {
          if (res.code === 200) {
            // 处理后端返回的数据，按照表格显示需求映射字段
            this.tableData = (res.data.records || []).map((item) => {
              return {
                ...item,
                availableTimes: `${item.finishNum || 0}/${
                  item.cateAvailableTotal || 0
                }/${item.cateTotal || 0}`,
              };
            });
            this.pagination.total = Number(res.data.total || 0);
          } else {
            this.$message.error(res.msg || "获取任务列表失败");
          }
        })
        .catch((err) => {
          console.error("获取任务列表失败:", err);
          this.$message.error("获取任务列表失败，请检查网络连接");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取任务统计数据
    fetchTaskStatistics() {
      // 构建参数对象，仅包含已选择的筛选条件
      const params = {};
      if (this.form.taskSource) params.taskSource = this.form.taskSource;
      if (this.form.classA) params.classA = this.form.classA;
      if (this.form.classB) params.classB = this.form.classB;
      if (this.form.taskName) params.taskName = this.form.taskName;
      if (this.form.cate1) params.cate1 = this.form.cate1;
      if (this.form.executionArea)
        params.executionArea = this.form.executionArea;

      // 打印参数对象，用于调试
      console.log("统计数据请求参数:", params);

      getTaskStatistics(params)
        .then((res) => {
          if (res.code === 200) {
            // 先处理百分比数据
            let percentageValue = 0;
            if (res.data.percentage) {
              // 去掉百分号，转为数字
              percentageValue = parseFloat(
                res.data.percentage.replace("%", "")
              );
            }

            // 更新统计数据
            this.taskStats = {
              total: res.data.total_num || 0,
              finished: res.data.finish_num || 0,
              todayFinished: res.data.today_finish_num || 0,
              percentage: percentageValue,
              trend: this.calculateTrend(
                res.data.today_finish_num,
                res.data.yesterdayFinish
              ),
              expectedEndDate: "--", // 暂无此数据
              planEndDate: "--", // 暂无此数据
              daysLeft: 0, // 暂无此数据
            };
          }
        })
        .catch((err) => {
          console.error("获取统计数据失败:", err);
        });
    },
    // 计算环比趋势
    calculateTrend(today, yesterday) {
      if (!today || !yesterday) return 0;

      // 计算环比增长率：(今日完成数 - 昨日完成数) / 昨日完成数 * 100
      const diff = today - yesterday;
      const rate = yesterday === 0 ? 0 : (diff / yesterday) * 100;
      return parseFloat(rate.toFixed(1));
    },
    // 搜索任务
    handleSearch() {
      this.pagination.current = 1;
      // 打印筛选条件，用于调试
      console.log("搜索筛选条件:", {
        taskSource: this.form.taskSource,
        classA: this.form.classA,
        classB: this.form.classB,
        taskName: this.form.taskName,
        cate1: this.form.cate1,
        executionArea: this.form.executionArea,
      });
      this.fetchTaskList();
      this.fetchTaskStatistics(); // 刷新统计数据
    },
    // 重置筛选条件
    handleReset() {
      this.form = {
        taskSource: "",
        classA: "",
        classB: "",
        taskName: "",
        cate1: "",
        executionArea: "",
      };
      this.reportTypeAOptions = [];
      this.reportTypeBOptions = [];
      this.taskNameOptions = [];
      this.pagination.current = 1;
      this.fetchTaskList();
      this.fetchTaskStatistics(); // 刷新统计数据
    },
    // 改变每页条数
    handleSizeChange(val) {
      this.pagination.size = val;
      this.fetchTaskList();
    },
    // 切换页码
    handleCurrentChange(val) {
      this.pagination.current = val;
      this.fetchTaskList();
    },
    // 任务分配
    handleClaim(row) {
      if (row.cateAvailableTotal <= 0) {
        return this.$message.error("该任务暂无可用数量,不可分配");
      }
      console.log("分配任务，行数据:", row);

      // 直接使用表格行数据
      this.selectedTaskData = {
        ...row,
        id: row.id,
        // 确保cateAvailableTotal是数字类型
        cateAvailableTotal: parseInt(row.cateAvailableTotal) || 0,
      };

      // 如果availableTimes存在且是字符串，进一步解析以获取准确的数值
      if (row.availableTimes && typeof row.availableTimes === "string") {
        const [finishNum, availableTotal, planTotal] =
          row.availableTimes.split("/");
        // 当字符串解析结果可用时，优先使用解析后的值
        this.selectedTaskData.cateAvailableTotal =
          parseInt(availableTotal) || 0;
      }

      console.log("传递给分配弹窗的数据:", this.selectedTaskData);

      this.selectedTaskId = row.id;
      this.claimModalVisible = true;
    },
    // 删除任务
    handleDelete(row) {
      this.$confirm("确定要删除该任务吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = true;
          deleteTask(row.id)
            .then((res) => {
              if (res.code === 200) {
                this.$message.success("任务删除成功");
                this.fetchTaskList();
                this.fetchTaskStatistics(); // 刷新统计数据
              } else {
                this.$message.error(res.msg || "删除任务失败");
              }
            })
            .catch((err) => {
              console.error("删除任务失败:", err);
              this.$message.error("删除任务失败，请检查网络连接");
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {
          // 取消操作
        });
    },
    // 领取任务成功回调
    handleClaimSuccess() {
      this.fetchTaskList();
      this.fetchTaskStatistics(); // 刷新统计数据
    },
    // 进度条颜色
    customColorMethod(percentage) {
      if (percentage < 30) {
        return "#67c23a";
      } else if (percentage < 70) {
        return "#e6a23c";
      } else {
        return "#f56c6c";
      }
    },
    // 获取可用数
    getAvailableNum(availableTimes) {
      const [, cateAvailableTotal] = availableTimes.split("/");
      return parseInt(cateAvailableTotal) || 0;
    },
    // 判断是否全部完成
    isAllCompleted(availableTimes) {
      const [finishNum, , planTotal] = availableTimes.split("/");
      return parseInt(finishNum) >= parseInt(planTotal);
    },
    // 判断是否应该显示分配按钮
    shouldShowAssignButton(availableTimes) {
      const [finishNum, cateAvailableTotal, planTotal] =
        availableTimes.split("/");
      // 当可用总数为0或完成数等于计划总数时不显示分配按钮
      return (
        parseInt(cateAvailableTotal) > 0 &&
        parseInt(finishNum) < parseInt(planTotal)
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.task-page {
  padding: 10px 0;
  background-color: #f0f2f5;
  min-height: calc(100vh - 120px);

  .task-card {
    margin-bottom: 20px;

    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .task-title {
        font-size: 18px;
        font-weight: bold;
      }
    }

    .filter-section {
      margin-bottom: 20px;
    }

    .task-detail-section {
      .task-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
        border-left: 4px solid #409eff;
        padding-left: 10px;
      }

      .task-statistics {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
        background-color: #fff;
        padding: 20px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

        .stats-item {
          width: 32%;
          padding: 20px;
          border-radius: 4px;
          background-color: #f9f9f9;
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .stats-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;

            &.primary-color {
              color: #409eff;
            }

            &.success-color {
              color: #67c23a;
            }

            &.warning-color {
              color: #e6a23c;
            }
          }

          .stats-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 12px;
          }

          .stats-sub-label {
            font-size: 13px;
            color: #909399;
            margin-bottom: 5px;
          }

          .stats-progress {
            display: flex;
            align-items: center;
            width: 100%;
            margin-top: 5px;

            .el-progress {
              width: 70%;
              margin-right: 10px;
            }

            .stats-percentage {
              font-size: 14px;
              color: #67c23a;
            }
          }

          .stats-compare {
            font-size: 13px;
            margin-top: 5px;

            .up-trend {
              color: #f56c6c;
            }

            .down-trend {
              color: #67c23a;
            }
          }

          .stats-icon {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 26px;
            color: rgba(0, 0, 0, 0.1);
          }

          .stats-days-left {
            font-size: 13px;
            color: #909399;
            margin-top: 5px;

            span {
              color: #e6a23c;
              font-weight: bold;
            }
          }
        }
      }

      .table-container {
        width: 100%;
        overflow-x: auto;
      }

      .pagination-container {
        margin-top: 20px;
        text-align: right;
      }
    }
  }
}

.action-container {
  white-space: nowrap;
  display: flex;
  justify-content: center;
}

.action-link {
  margin-left: 15px;
}

/deep/ .el-table__header {
  th {
    &:last-child {
      transform: translateY(-1px) !important;
    }
  }
}

// 食品类别单元格样式
.food-category-cell {
  padding: 5px 0;
  border-bottom: 1px dashed #ebeef5;

  &:last-child {
    border-bottom: none;
  }
}

/deep/ .el-table__row {
  .food-category-cell:not(:last-child) {
    margin-bottom: 4px;
  }
}
</style>
