<template>
  <div class="playback-list">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :inline="true" :model="queryParams" size="small">
        <el-form-item label="回放名称:">
          <el-input
            v-model="queryParams.courseName"
            placeholder="请输入回放名称"
            clearable
            @keyup.enter.native="handleQuery"
          ></el-input>
        </el-form-item>
        <el-form-item label="课程类型:">
          <el-select
            v-model="queryParams.courseType"
            placeholder="请选择课程类型"
            clearable
          >
            <el-option
              v-for="item in courseTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="难度等级:">
          <el-select
            v-model="queryParams.courseDifficulty"
            placeholder="请选择难度等级"
            clearable
          >
            <el-option
              v-for="item in difficultyOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 回放列表 -->
    <div class="course-list" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="8" v-for="(course, index) in courseList" :key="index">
          <div class="course-card">
            <div class="course-cover" @click="handlePreview(course)">
              <img
                :src="
                  course.coverPath ||
                  'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png'
                "
                alt="回放封面"
              />
              <div class="cover-mask">
                <i class="el-icon-video-play play-icon"></i>
                <div class="play-text">观看回放</div>
              </div>
            </div>
            <div class="course-info">
              <h3 class="course-title">{{ course.courseName }}</h3>
              <p class="course-desc">{{ course.courseIntroduce }}</p>
              <div class="course-meta">
                <div class="meta-left">
                  <el-tag
                    :type="getDifficultyTagType(course.courseDifficulty)"
                    >{{ course.courseDifficulty }}</el-tag
                  >
                  <el-tag>{{ course.courseType }}</el-tag>
                  <span class="create-time">{{
                    formatTime(course.createTime)
                  }}</span>
                </div>
                <span class="price-tag" :class="{ free: !course.price }">
                  {{ getPriceText(course.price) }}
                </span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 分页组件 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page.sync="queryParams.pageNum"
          :page-sizes="[6, 12, 18, 24]"
          :page-size.sync="queryParams.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        ></el-pagination>
      </div>

      <!-- 无数据提示 -->
      <div class="empty-container" v-if="courseList.length === 0 && !loading">
        <el-empty description="暂无回放数据">
          <el-button type="primary" size="small" @click="resetQuery"
            >刷新数据</el-button
          >
        </el-empty>
      </div>
    </div>

    <!-- 视频预览组件 -->
    <video-preview
      :visible.sync="previewVisible"
      :video-url="previewData.videoUrl"
      :title="previewData.title"
      :difficulty="previewData.difficulty"
      :description="previewData.description"
    />

    <!-- 微信支付弹窗 -->
    <wechat-payment
      :visible.sync="paymentVisible"
      :course="paymentCourse"
      @payment-success="handlePaymentSuccess"
    />
  </div>
</template>

<script>
import { getCourseList, checkCoursePaidStatus } from "@/api/modules/course";
import VideoPreview from "./VideoPreview";
import WechatPayment from "@/components/WechatPayment";

/**
 * 知识库回放列表组件
 * <AUTHOR> Assistant
 * @date 2024
 */
export default {
  name: "PlaybackList",
  components: {
    VideoPreview,
    WechatPayment,
  },
  data() {
    return {
      // 加载状态
      loading: false,
      // 课程列表
      courseList: [],
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 6,
        courseName: "",
        courseType: "",
        courseDifficulty: "",
        courseCategory: "2", // 固定为回放类型
      },
      // 课程类型选项
      courseTypeOptions: [
        { label: "抽样课程", value: "抽样课程" },
        { label: "检验课程", value: "检验课程" },
      ],
      // 难度选项
      difficultyOptions: [
        { label: "初级", value: "初级" },
        { label: "中级", value: "中级" },
        { label: "高级", value: "高级" },
      ],
      // 视频预览
      previewVisible: false,
      previewData: {
        videoUrl: "",
        title: "",
        difficulty: "",
        description: "",
      },
      // 支付相关
      paymentVisible: false,
      paymentCourse: null,
      paymentLoading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /**
     * 获取回放课程列表
     */
    getList() {
      this.loading = true;

      // 构造请求参数
      const params = {
        current: this.queryParams.pageNum,
        size: this.queryParams.pageSize,
        courseCategory: "2", // 固定查询回放类型
        useUser: "Y", // 加入该参数才能让后端的接口返回该用户是否购买了课程
      };

      // 只有当有值时才添加筛选参数
      if (
        this.queryParams.courseName &&
        this.queryParams.courseName.trim() !== ""
      ) {
        params.courseName = this.queryParams.courseName.trim();
      }

      if (
        this.queryParams.courseType &&
        this.queryParams.courseType.trim() !== ""
      ) {
        params.courseType = this.queryParams.courseType;
      }

      if (
        this.queryParams.courseDifficulty &&
        this.queryParams.courseDifficulty.trim() !== ""
      ) {
        params.courseDifficulty = this.queryParams.courseDifficulty;
      }

      // 调用API
      getCourseList(params)
        .then((res) => {
          if (res.code === 200 && res.success) {
            this.courseList = res.data.records || [];
            this.total = parseInt(res.data.total) || 0;
          } else {
            this.$message.error(res.msg || "获取回放列表失败");
            this.courseList = [];
            this.total = 0;
          }
        })
        .catch((err) => {
          console.error("获取回放列表失败:", err);
          this.$message.error("获取回放列表失败，请检查网络连接");
          this.courseList = [];
          this.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /**
     * 搜索按钮操作
     */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /**
     * 重置按钮操作
     */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 6,
        courseName: "",
        courseType: "",
        courseDifficulty: "",
        courseCategory: "2", // 保持回放类型不变
      };
      this.getList();
    },

    /**
     * 页码改变事件
     */
    handleCurrentChange() {
      this.getList();
    },

    /**
     * 每页条数改变事件
     */
    handleSizeChange() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /**
     * 预览按钮操作
     * @param {Object} course - 课程数据
     */
    async handlePreview(course) {
      if (!course.videoPath) {
        this.$message.warning("该回放暂无可预览的视频");
        return;
      }

      // 如果课程有价格，需要检查付费状态
      if (course.price && course.price > 0) {
        try {
          // 调用API检查当前用户是否已付费
          const response = await checkCoursePaidStatus(course.id);

          if (
            response.code === 200 &&
            response.success &&
            response.data === true
          ) {
            // 已付费，可以直接播放
            this.playVideo(course);
          } else {
            // 未付费，显示支付弹窗
            this.showPaymentModal(course);
          }
        } catch (error) {
          console.error("检查付费状态失败:", error);
          // 出错时默认显示支付弹窗
          this.showPaymentModal(course);
        }
      } else {
        // 免费课程直接播放
        this.playVideo(course);
      }
    },

    /**
     * 播放视频
     * @param {Object} course - 课程数据
     */
    playVideo(course) {
      this.previewData = {
        videoUrl: course.videoPath,
        title: course.courseName,
        difficulty: course.courseDifficulty,
        description: course.courseIntroduce,
      };
      this.previewVisible = true;
    },

    /**
     * 显示支付弹窗
     * @param {Object} course - 课程数据
     */
    showPaymentModal(course) {
      this.paymentCourse = course;
      this.paymentVisible = true;
    },

    /**
     * 支付成功处理
     * @param {Object} course - 课程数据
     */
    handlePaymentSuccess(course) {
      // 关闭支付弹窗
      this.paymentVisible = false;

      // 直接播放视频
      setTimeout(() => {
        this.playVideo(course);
      }, 500);
    },

    /**
     * 获取难度标签类型
     * @param {string} difficulty - 难度级别
     * @returns {string} 标签类型
     */
    getDifficultyTagType(difficulty) {
      switch (difficulty) {
        case "初级":
          return "";
        case "中级":
          return "warning";
        case "高级":
          return "danger";
        default:
          return "";
      }
    },

    /**
     * 格式化时间
     * @param {string} time - 时间字符串
     * @returns {string} 格式化后的时间
     */
    formatTime(time) {
      if (!time) return "";
      return time.substring(0, 10);
    },

    /**
     * 获取价格显示文本
     * @param {number|string} price - 价格
     * @returns {string} 格式化后的价格文本
     */
    getPriceText(price) {
      if (!price || price === null || price === undefined || price === 0) {
        return "免费";
      }
      // 价格单位从分转换为元
      return `¥ ${(price / 100).toFixed(2)}`;
    },
  },
};
</script>

<style lang="scss" scoped>
.playback-list {
  padding: 20px;

  .search-container {
    background: #fff;
    padding: 20px 15px 0 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }

  .course-list {
    min-height: 400px;
    margin-bottom: 20px;
  }

  .course-card {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;
    transition: all 0.3s;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

      .cover-mask {
        opacity: 1;
      }

      .course-cover img {
        transform: scale(1.05);
      }
    }

    .course-cover {
      height: 200px;
      overflow: hidden;
      position: relative;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
      }

      .cover-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s;

        .play-icon {
          font-size: 60px;
          color: #fff;
          margin-bottom: 10px;
        }

        .play-text {
          color: #fff;
          font-size: 14px;
          font-weight: bold;
        }
      }
    }

    .course-info {
      padding: 15px;

      .course-title {
        margin: 0 0 10px;
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .course-desc {
        margin: 0 0 10px;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        height: 42px;
      }

      .course-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .meta-left {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 8px;

          .el-tag {
            font-size: 12px;
          }

          .create-time {
            color: #909399;
            font-size: 12px;
          }
        }

        .price-tag {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 14px;
          font-weight: bold;
          background: linear-gradient(135deg, #ff6b6b, #ee5a52);
          color: #fff;

          &.free {
            background: linear-gradient(135deg, #51cf66, #40c057);
          }
        }
      }
    }
  }

  .pagination-container {
    text-align: center;
    margin-top: 30px;
  }

  .empty-container {
    padding: 40px 0;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .playback-list {
    .course-card {
      .course-cover {
        height: 180px;
      }
    }
  }
}

@media (max-width: 768px) {
  .playback-list {
    padding: 10px;

    .search-container {
      padding: 15px 10px 0 10px;
    }

    .course-card {
      .course-cover {
        height: 160px;
      }

      .course-info {
        padding: 12px;

        .course-title {
          font-size: 14px;
        }

        .course-desc {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
