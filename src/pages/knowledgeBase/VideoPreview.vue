<template>
  <el-dialog
    :visible.sync="visible"
    :title="title || '视频预览'"
    width="800px"
    :close-on-click-modal="false"
    append-to-body
    center
    @close="handleClose"
    @closed="handleClosed"
  >
    <div class="video-container" v-loading="loading">
      <!-- iframe播放器 -->
      <iframe
        v-if="useIframe && processedVideoUrl"
        :src="processedVideoUrl"
        class="video-iframe"
        frameborder="0"
        allowfullscreen
      ></iframe>

      <!-- 原生视频播放器 -->
      <video
        v-if="!useIframe && processedVideoUrl"
        ref="videoPlayer"
        class="video-player"
        controls
        :src="processedVideoUrl"
        crossorigin="anonymous"
        @canplay="handleCanPlay"
        @loadeddata="handleLoaded"
        @loadstart="handleLoadStart"
        @waiting="handleWaiting"
        @error="handleError"
      ></video>

      <div v-if="loadingMessage && loading" class="loading-message">
        <span>{{ loadingMessage }}</span>
      </div>
      <div v-if="error" class="error-message">
        <i class="el-icon-warning-outline"></i>
        <span>{{ error }}</span>
        <div class="error-actions">
          <el-button type="text" @click="openInNewTab"
            >在新窗口中打开</el-button
          >
          <el-button type="text" @click="togglePlayer">切换播放模式</el-button>
        </div>
      </div>
    </div>
    <div class="course-info" v-if="showInfo">
      <h3>{{ title }}</h3>
      <p class="difficulty">
        <el-tag :type="getDifficultyTagType(difficulty)">{{
          difficulty
        }}</el-tag>
      </p>
      <p class="desc">{{ description }}</p>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "VideoPreview",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    videoUrl: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
    difficulty: {
      type: String,
      default: "",
    },
    description: {
      type: String,
      default: "",
    },
    showInfo: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      loading: true,
      error: null,
      processedVideoUrl: "",
      loadingMessage: "正在加载视频...",
      loadStartTime: null,
      useIframe: false,
    };
  },
  computed: {
    // 获取当前协议
    currentProtocol() {
      return typeof window !== "undefined" ? window.location.protocol : "http:";
    },
  },
  watch: {
    videoUrl: {
      immediate: true,
      handler(newUrl) {
        if (newUrl) {
          console.log("视频URL已更新:", newUrl);
          this.loading = true;
          this.error = null;
          this.processVideoUrl(newUrl);

          // 添加一个小延迟以确保DOM已更新
          setTimeout(() => {
            if (this.$refs.videoPlayer) {
              this.$refs.videoPlayer.load();
            }
          }, 200);
        }
      },
    },
    visible(val) {
      if (val) {
        this.loading = true;
        this.error = null;
        this.loadingMessage = "正在加载视频...";

        // 如果是阿里云OSS视频，尝试使用iframe播放
        if (this.videoUrl && this.videoUrl.includes("aliyuncs.com")) {
          console.log("检测到阿里云OSS视频，自动切换到iframe模式");
          // 使用iframe模式
          this.useIframe = true;
          this.loading = false;
          return;
        }

        // 防止视频加载太快，导致loading闪烁
        setTimeout(() => {
          if (!this.useIframe && this.$refs.videoPlayer) {
            // 手动触发播放以帮助某些浏览器启动加载过程
            const playPromise = this.$refs.videoPlayer.play();
            if (playPromise !== undefined) {
              playPromise.catch((err) => {
                console.log("播放器无法自动播放:", err);
                // 如果无法自动播放，添加提示
                this.loadingMessage = "请点击视频进行播放";
              });
            }
          }
        }, 300);
      } else {
        // Modal关闭时停止视频播放
        this.stopVideo();
      }
    },
  },
  beforeDestroy() {
    // 组件销毁前停止视频播放
    this.stopVideo();
  },
  methods: {
    processVideoUrl(url) {
      if (!url) {
        this.processedVideoUrl = "";
        return;
      }

      try {
        // 解析URL
        const urlObj = new URL(url);

        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();
        urlObj.searchParams.set("t", timestamp);

        // 如果使用iframe，保持原始协议，避免CORS错误
        if (this.videoUrl && this.videoUrl.includes("aliyuncs.com")) {
          // 检测到阿里云OSS视频，标记使用iframe
          this.useIframe = true;
        }
        // 如果不使用iframe且页面是HTTP而视频是HTTPS，尝试调整协议
        else if (
          !this.useIframe &&
          this.currentProtocol === "http:" &&
          urlObj.protocol === "https:"
        ) {
          console.log("调整视频URL协议从HTTPS到HTTP");
          urlObj.protocol = "http:";
        }

        this.processedVideoUrl = urlObj.toString();
        console.log(
          "处理后的视频URL:",
          this.processedVideoUrl,
          "使用iframe:",
          this.useIframe
        );
      } catch (err) {
        console.error("处理视频URL时出错:", err);
        // 回退到原始URL
        this.processedVideoUrl = url;
      }
    },

    handleCanPlay() {
      console.log("视频可以播放了 (canplay 事件)");
      this.loading = false;
      this.loadingMessage = null;
    },

    handleLoaded() {
      console.log("视频数据已加载 (loadeddata 事件)");
      this.loading = false;
      this.loadingMessage = null;
    },

    handleLoadStart() {
      console.log("视频开始加载 (loadstart 事件)");
      this.loadStartTime = Date.now();

      // 添加超时检测
      setTimeout(() => {
        if (this.loading && !this.error) {
          console.log("视频加载超时 (20秒)");
          this.loadingMessage = "视频加载时间过长，可能存在网络问题";
        }
      }, 20000);
    },

    handleWaiting() {
      console.log("视频正在缓冲 (waiting 事件)");
      this.loadingMessage = "视频正在缓冲...";
    },

    handleError(e) {
      console.error("视频加载错误:", e);
      this.loading = false;
      this.error = "视频加载失败，可能是由于协议限制或网络问题";
      console.error("视频加载失败，URL:", this.processedVideoUrl);
    },

    openInNewTab() {
      if (this.videoUrl) {
        window.open(this.videoUrl, "_blank");
      }
    },

    tryAlternativePlayer() {
      this.error = null;
      this.loading = false;
      this.useIframe = true;
    },

    togglePlayer() {
      this.useIframe = !this.useIframe;
      if (!this.useIframe) {
        // 当切换回视频模式时，可能需要重新加载视频
        setTimeout(() => {
          if (this.$refs.videoPlayer) {
            this.$refs.videoPlayer.load();
          }
        }, 200);
      }
    },

    handleClose() {
      this.stopVideo();
      this.$emit("update:visible", false);
      this.$emit("close");
    },

    handleClosed() {
      // Dialog完全关闭后的回调，确保视频完全停止
      this.stopVideo();
      this.$emit("closed");
    },

    /**
     * 停止视频播放（用于关闭Modal时）
     */
    stopVideo() {
      // 停止原生video元素
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.pause();
        this.$refs.videoPlayer.currentTime = 0; // 重置播放进度
        this.$refs.videoPlayer.src = ""; // 清空src停止加载
      }

      // 对于iframe播放器，清空src来停止播放
      if (this.useIframe) {
        this.processedVideoUrl = "";
      }
    },

    /**
     * 暂停视频播放（保持当前进度）
     */
    pauseVideo() {
      // 暂停原生video元素
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.pause();
      }

      // iframe无法直接暂停，只能停止
      if (this.useIframe) {
        this.stopVideo();
      }
    },
    getDifficultyTagType(difficulty) {
      switch (difficulty) {
        case "初级":
          return "";
        case "中级":
          return "warning";
        case "高级":
          return "danger";
        default:
          return "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.video-player,
.video-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.error-message,
.loading-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 80%;
}

.error-message {
  color: #f56c6c;

  i {
    font-size: 32px;
    margin-bottom: 10px;
    display: block;
  }

  .error-actions {
    margin-top: 15px;
    display: flex;
    justify-content: center;

    .el-button {
      margin: 0 10px;
    }
  }
}

.loading-message {
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 4px;
}

.course-info {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;

  h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
  }

  .difficulty {
    margin: 10px 0;
  }

  .desc {
    margin: 10px 0 0 0;
    color: #606266;
    line-height: 1.6;
  }
}
</style>
