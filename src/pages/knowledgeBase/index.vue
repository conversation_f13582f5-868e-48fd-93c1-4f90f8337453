<template>
  <div class="wrapper">
    <simple-list-page v-loading="loading">
      <div slot="top-options">
        <div class="filter-container">
          <el-form :inline="true" :model="queryParams" size="small">
            <el-form-item label="课程名称：">
              <el-input
                v-model="queryParams.courseName"
                placeholder="请输入课程名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="课程类型：">
              <el-select
                v-model="queryParams.courseType"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in courseTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="课程分类：">
              <el-select
                v-model="queryParams.courseCategory"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in courseCategoryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery">搜索</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div slot="bottom-options" class="data-title">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button
          type="warning"
          size="small"
          icon="el-icon-money"
          :disabled="selectedCourses.length === 0"
          @click="handleOfflinePurchase"
          >线下购买</el-button
        >
      </div>

      <el-table
        ref="table"
        :data="courseList"
        border
        size="mini"
        style="width: 100%; margin-top: 5px; margin-bottom: 5px"
        :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
        ></el-table-column>
        <el-table-column
          label="序号"
          type="index"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="courseName"
          label="课程名称"
          align="center"
          min-width="150"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="courseIntroduce"
          label="课程简介"
          align="center"
          min-width="200"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="courseDifficulty"
          label="难度"
          align="center"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag :type="getDifficultyTagType(scope.row.courseDifficulty)">{{
              scope.row.courseDifficulty
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="courseType"
          label="课程类型"
          align="center"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="courseCategory"
          label="课程分类"
          align="center"
          width="100"
        >
          <template slot-scope="scope">
            {{ getCategoryLabel(scope.row.courseCategory) }}
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" align="center" width="120">
          <template slot-scope="scope">
            <span
              v-if="!scope.row.price || scope.row.price === 0"
              style="color: #67c23a"
              >免费</span
            >
            <span v-else style="color: #e6a23c"
              >￥{{ (scope.row.price / 100).toFixed(2) }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          align="center"
          width="150"
        ></el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handlePreview(scope.row)"
              >预览</el-button
            >
            <el-button type="text" size="small" @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
              style="color: #f56c6c"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-show="total > 0" style="height: 45px">
        <pagination
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </simple-list-page>

    <!-- 新增/编辑课程对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
      custom-class="course-dialog"
    >
      <div class="dialog-content-wrapper">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="课程名称：" prop="courseName">
            <el-input v-model="form.courseName" placeholder="请输入课程名称" />
          </el-form-item>
          <el-form-item label="课程难度：" prop="difficulty">
            <el-select
              v-model="form.difficulty"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="dict in difficultyOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="课程类型：" prop="courseType">
            <el-select
              v-model="form.courseType"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="dict in courseTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="课程分类：" prop="courseCategory">
            <el-select
              v-model="form.courseCategory"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="dict in courseCategoryOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="课程价格：" prop="priceType">
            <el-radio-group
              v-model="form.priceType"
              @change="handlePriceTypeChange"
            >
              <el-radio label="free">免费</el-radio>
              <el-radio label="paid">付费</el-radio>
            </el-radio-group>
            <el-input-number
              v-model="form.price"
              :disabled="form.priceType === 'free'"
              :min="0"
              :precision="2"
              controls-position="right"
              placeholder="请输入价格"
              style="width: 200px; margin-left: 10px"
            >
              <template slot="append">元</template>
            </el-input-number>
          </el-form-item>
          <el-form-item label="课程简介：" prop="courseDesc">
            <el-input
              v-model="form.courseDesc"
              type="textarea"
              placeholder="请输入课程简介"
              :rows="4"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="上传封面：" prop="coverImage">
            <el-upload
              class="cover-uploader"
              action="#"
              :http-request="uploadCoverImage"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              @change="() => $refs.form.validateField('coverImage')"
            >
              <img
                v-if="form.coverImage"
                :src="form.coverImage"
                class="cover-image"
                style="
                  max-width: 178px;
                  max-height: 178px;
                  width: 178px;
                  height: 178px;
                  object-fit: cover;
                "
              />
              <div v-else class="cover-upload-placeholder">
                <i class="el-icon-plus"></i>
              </div>
            </el-upload>
            <el-progress
              v-if="coverUploadLoading"
              :percentage="coverUploadPercent"
              style="margin-top: 10px"
            ></el-progress>
          </el-form-item>
          <el-form-item label="上传视频：" prop="videoUrl">
            <el-upload
              class="video-uploader"
              action="#"
              :http-request="uploadVideo"
              :show-file-list="false"
              :before-upload="beforeVideoUpload"
              @change="() => $refs.form.validateField('videoUrl')"
            >
              <el-button
                size="small"
                type="primary"
                :loading="videoUploadLoading"
                >点击上传</el-button
              >
              <div slot="tip" class="el-upload__tip" v-if="form.videoUrl">
                已上传: {{ form.videoFileName || "视频文件" }}
              </div>
              <div slot="tip" class="el-upload__tip" v-else>
                仅支持mp4格式，单个文件不超过3GB
              </div>
            </el-upload>
            <el-progress
              v-if="videoUploadLoading"
              :percentage="videoUploadPercent"
              style="margin-top: 10px"
            ></el-progress>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading"
          >保 存</el-button
        >
      </div>
    </el-dialog>

    <!-- 视频预览组件 -->
    <video-preview
      :visible.sync="previewVisible"
      :video-url="previewData.videoUrl"
      :title="previewData.title"
      :difficulty="previewData.difficulty"
      :description="previewData.description"
    />

    <!-- 线下购买对话框 -->
    <el-dialog
      title="线下购买"
      :visible.sync="offlinePurchaseVisible"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="purchaseForm"
        :model="purchaseForm"
        :rules="purchaseRules"
        label-width="140px"
      >
        <el-form-item label="付款人所属机构：" prop="organization">
          <el-select
            v-model="purchaseForm.organization"
            placeholder="请选择机构"
            style="width: 100%"
            filterable
            @change="handleOrganizationChange"
          >
            <el-option
              v-for="org in organizationOptions"
              :key="org.value"
              :label="org.label"
              :value="org.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="付款用户：" prop="user">
          <el-select
            v-model="purchaseForm.user"
            placeholder="请选择用户"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="user in userOptions"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="付款金额：" prop="amount">
          <el-input-number
            v-model="purchaseForm.amount"
            :min="0"
            :precision="2"
            controls-position="right"
            placeholder="请输入付款金额"
            style="width: 100%"
          />
          <!-- <div style="margin-top: 5px; font-size: 12px; color: #999">
            选中课程总价：¥{{ selectedCoursesTotalPrice }}
          </div> -->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelOfflinePurchase">取 消</el-button>
        <el-button
          type="primary"
          @click="submitOfflinePurchase"
          :loading="purchaseSubmitLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import VideoPreview from "./VideoPreview";
import {
  getCourseList,
  addCourse,
  updateCourse,
  deleteCourse,
  offlinePurchaseCourse,
  getAllOrganizations,
  getAllUsers,
} from "@/api/modules/course";
import { getToken } from "@/utils/auth";
import axios from "axios";

export default {
  name: "KnowledgeBase",
  components: {
    Pagination,
    VideoPreview,
  },
  data() {
    const validateCoverImage = (rule, value, callback) => {
      if (!this.form.coverImage) {
        callback();
      } else {
        callback();
      }
    };

    const validateVideoUrl = (rule, value, callback) => {
      if (!this.form.videoUrl) {
        callback(new Error("请上传课程视频"));
      } else {
        callback();
      }
    };

    return {
      // 遮罩层
      loading: false,
      // 上传地址
      uploadUrl: process.env.VUE_APP_BASE_API + "/oss/endpoint/putPublicFile",
      // 上传头信息
      headers: {
        Authorization: `Bearer ${getToken()}`,
      },
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        courseName: "",
        courseType: "",
        courseCategory: "",
      },
      // 课程列表
      courseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {
        id: null,
        courseName: "",
        difficulty: "",
        courseType: "",
        courseCategory: "",
        courseDesc: "",
        coverImage: "",
        videoUrl: "",
        videoFileName: "",
        priceType: "free", // 价格类型：free-免费，paid-付费
        price: 0, // 价格
      },
      // 表单校验
      rules: {
        courseName: [
          { required: true, message: "课程名称不能为空", trigger: "blur" },
        ],
        difficulty: [
          { required: true, message: "课程难度不能为空", trigger: "change" },
        ],
        courseType: [
          { required: true, message: "课程类型不能为空", trigger: "change" },
        ],
        courseCategory: [
          { required: true, message: "课程分类不能为空", trigger: "change" },
        ],
        courseDesc: [
          { required: false, message: "请输入课程简介", trigger: "blur" },
        ],
        coverImage: [
          { required: false, message: "请上传课程封面图片", trigger: "change" },
          { validator: validateCoverImage, trigger: "change" },
        ],
        videoUrl: [
          { required: true, message: "请上传课程视频", trigger: "change" },
          { validator: validateVideoUrl, trigger: "change" },
        ],
        priceType: [
          { required: true, message: "请选择价格类型", trigger: "change" },
        ],
        price: [
          {
            validator: (rule, value, callback) => {
              if (this.form.priceType === "paid" && (!value || value <= 0)) {
                callback(new Error("付费课程请输入有效价格"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      // 难度选项
      difficultyOptions: [
        { label: "初级", value: "初级" },
        { label: "中级", value: "中级" },
        { label: "高级", value: "高级" },
      ],
      // 课程类型选项
      courseTypeOptions: [
        { label: "抽样课程", value: "抽样课程" },
        { label: "检验课程", value: "检验课程" },
      ],
      // 课程分类选项
      courseCategoryOptions: [
        { label: "课程", value: "1" },
        { label: "回放", value: "2" },
      ],
      // 封面上传加载状态
      coverUploadLoading: false,
      // 封面上传进度
      coverUploadPercent: 0,
      // 视频上传加载状态
      videoUploadLoading: false,
      // 视频上传进度
      videoUploadPercent: 0,
      // 提交加载状态
      submitLoading: false,
      // 视频预览
      previewVisible: false,
      previewData: {
        videoUrl: "",
        title: "",
        difficulty: "",
        description: "",
      },
      // 选中的课程
      selectedCourses: [],
      // 线下购买对话框
      offlinePurchaseVisible: false,
      // 线下购买表单
      purchaseForm: {
        organization: "",
        user: "",
        amount: 0,
      },
      // 线下购买表单校验
      purchaseRules: {
        organization: [
          {
            required: true,
            message: "请选择付款人所属机构",
            trigger: "change",
          },
        ],
        user: [
          { required: true, message: "请选择付款用户", trigger: "change" },
        ],
        amount: [
          { required: true, message: "请输入付款金额", trigger: "blur" },
          {
            type: "number",
            min: 0.01,
            message: "付款金额必须大于0",
            trigger: "blur",
          },
        ],
      },
      // 机构选项
      organizationOptions: [],
      // 用户选项（全量数据）
      allUserOptions: [],
      // 用户选项（过滤后显示的数据）
      userOptions: [],
      // 线下购买提交加载状态
      purchaseSubmitLoading: false,
    };
  },
  computed: {
    // 计算选中课程的总价
    selectedCoursesTotalPrice() {
      return this.selectedCourses.reduce((total, course) => {
        const price = course.price ? course.price / 100 : 0; // 分转元
        return total + price;
      }, 0);
    },
  },
  created() {
    this.getList();
    this.loadOrganizations();
    this.loadUsers();
  },
  methods: {
    // 获取课程列表
    getList() {
      this.loading = true;

      // 构造请求参数
      const params = {
        current: this.queryParams.pageNum,
        size: this.queryParams.pageSize,
        useUser: "Y", // 加入该参数才能让后端的接口返回该用户是否购买了课程
      };

      // 只有当有值时才添加筛选参数
      if (this.queryParams.courseName) {
        params.courseName = this.queryParams.courseName;
      }

      if (this.queryParams.courseType) {
        params.courseType = this.queryParams.courseType;
      }

      if (this.queryParams.courseCategory) {
        params.courseCategory = this.queryParams.courseCategory;
      }

      // 调用实际API
      getCourseList(params)
        .then((res) => {
          if (res.code === 200 && res.success) {
            this.courseList = res.data.records || [];
            // 确保total是数字类型
            this.total = parseInt(res.data.total) || 0;
          } else {
            this.$message.error(res.msg || "获取课程列表失败");
            this.courseList = [];
            this.total = 0;
          }
        })
        .catch((err) => {
          console.error("获取课程列表失败:", err);
          this.$message.error("获取课程列表失败，请检查网络连接");
          this.courseList = [];
          this.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮操作
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        courseName: "",
        courseType: "",
        courseCategory: "",
      };
      this.handleQuery();
    },
    // 新增按钮操作
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增课程";
    },
    // 编辑按钮操作
    handleEdit(row) {
      this.reset();
      // 深拷贝并映射字段
      const rowCopy = JSON.parse(JSON.stringify(row));
      this.form = {
        id: rowCopy.id,
        courseName: rowCopy.courseName,
        difficulty: rowCopy.courseDifficulty, // 映射难度字段
        courseType: rowCopy.courseType,
        courseCategory: rowCopy.courseCategory, // 映射课程分类字段
        courseDesc: rowCopy.courseIntroduce, // 映射简介字段
        coverImage: rowCopy.coverPath, // 映射封面图片
        videoUrl: rowCopy.videoPath, // 映射视频路径
        videoFileName: rowCopy.videoPath
          ? rowCopy.videoPath.split("/").pop()
          : "",
        priceType: rowCopy.price && rowCopy.price > 0 ? "paid" : "free", // 根据price字段判断是否付费
        price: rowCopy.price ? rowCopy.price / 100 : 0, // 课程价格，分转元
      };
      this.open = true;
      this.title = "编辑课程";
    },
    // 预览按钮操作
    handlePreview(row) {
      if (!row.videoPath) {
        this.$message.warning("该课程暂无可预览的视频");
        return;
      }

      // 使用原始URL，让VideoPreview组件内部处理
      this.previewData = {
        videoUrl: row.videoPath,
        title: row.courseName,
        difficulty: row.courseDifficulty,
        description: row.courseIntroduce,
      };
      this.previewVisible = true;
    },
    // 删除按钮操作
    handleDelete(row) {
      console.log("要删除的课程信息:", row);
      this.$confirm("是否确认删除该课程?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = true;
          // 调用删除API
          if (!row.id) {
            this.$message.error("未找到课程ID，无法删除");
            this.loading = false;
            return;
          }
          deleteCourse(row.id)
            .then((res) => {
              console.log("删除API响应:", res);
              if (res.code === 200 && res.success) {
                this.$message.success("删除成功");
                this.getList();
              } else {
                this.$message.error(res.msg || "删除失败");
              }
            })
            .catch((err) => {
              console.error("删除课程失败:", err);
              this.$message.error("删除课程失败，请检查网络连接");
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        courseName: "",
        difficulty: "",
        courseType: "",
        courseCategory: "",
        courseDesc: "",
        coverImage: "",
        videoUrl: "",
        videoFileName: "",
        priceType: "free",
        price: 0,
      };
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 提交按钮
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.submitLoading = true;

          // 准备提交数据
          let submitData = {};

          if (this.form.id !== null) {
            // 编辑模式，同样需要调整字段
            submitData = {
              id: this.form.id,
              courseName: this.form.courseName,
              courseDifficulty: this.form.difficulty,
              courseType: this.form.courseType,
              courseCategory: this.form.courseCategory,
              courseIntroduce: this.form.courseDesc,
              coverPath: this.form.coverImage,
              videoPath: this.form.videoUrl,
              price:
                this.form.priceType === "paid"
                  ? Math.round(this.form.price * 100)
                  : 0, // 课程价格，元转分
            };
          } else {
            // 新增模式
            submitData = {
              courseName: this.form.courseName,
              courseDifficulty: this.form.difficulty,
              courseType: this.form.courseType,
              courseCategory: this.form.courseCategory,
              courseIntroduce: this.form.courseDesc,
              coverPath: this.form.coverImage,
              videoPath: this.form.videoUrl,
              price:
                this.form.priceType === "paid"
                  ? Math.round(this.form.price * 100)
                  : 0, // 课程价格，元转分
            };
          }

          // 根据是否有ID判断是新增还是编辑
          const request =
            this.form.id !== null
              ? updateCourse(submitData)
              : addCourse(submitData);

          request
            .then((res) => {
              if (res.code === 200 && res.success) {
                this.$message.success(
                  this.form.id !== null ? "修改成功" : "新增成功"
                );
                this.open = false;
                this.getList();
              } else {
                this.$message.error(
                  res.msg || (this.form.id !== null ? "修改失败" : "新增失败")
                );
              }
            })
            .catch((err) => {
              console.error(
                this.form.id !== null ? "编辑课程失败:" : "新增课程失败:",
                err
              );
              this.$message.error(
                this.form.id !== null
                  ? "编辑课程失败，请检查网络连接"
                  : "新增课程失败，请检查网络连接"
              );
            })
            .finally(() => {
              this.loading = false;
              this.submitLoading = false;
            });
        }
      });
    },
    // 上传封面前校验
    beforeImageUpload(file) {
      const isImage = file.type.indexOf("image/") !== -1;
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error("上传封面图片只能是图片格式!");
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传封面图片大小不能超过 2MB!");
        return false;
      }
      return true;
    },
    // 上传视频前校验
    beforeVideoUpload(file) {
      const isMP4 = file.type === "video/mp4";
      const isLt3G = file.size / 1024 / 1024 < 3072;

      if (!isMP4) {
        this.$message.error("上传视频只能是 MP4 格式!");
        return false;
      }
      if (!isLt3G) {
        this.$message.error("上传视频大小不能超过 3GB!");
        return false;
      }
      return true;
    },
    // 上传封面
    uploadCoverImage(params) {
      this.coverUploadLoading = true;
      this.coverUploadPercent = 0;
      const formData = new FormData();
      formData.append("file", params.file);

      // 使用axios直接上传以支持进度监控
      const config = {
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: `Bearer ${getToken()}`,
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          this.coverUploadPercent = percentCompleted;
        },
      };

      axios
        .post(this.uploadUrl, formData, config)
        .then((response) => {
          const res = response.data;
          if (res.code === 200 && res.success) {
            this.form.coverImage = res.data.link; // 使用link字段获取文件地址
            this.$message.success("封面上传成功");
            this.$refs.form && this.$refs.form.validateField("coverImage");
          } else {
            this.$message.error(res.msg || "封面上传失败");
          }
        })
        .catch((err) => {
          console.error("封面上传失败:", err);
          this.$message.error("封面上传失败，请检查网络连接");
        })
        .finally(() => {
          this.coverUploadLoading = false;
        });
    },
    // 上传视频
    uploadVideo(params) {
      this.videoUploadLoading = true;
      this.videoUploadPercent = 0;
      const formData = new FormData();
      formData.append("file", params.file);

      // 使用axios直接上传以支持进度监控
      const config = {
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: `Bearer ${getToken()}`,
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          this.videoUploadPercent = percentCompleted;
        },
      };

      axios
        .post(this.uploadUrl, formData, config)
        .then((response) => {
          const res = response.data;
          if (res.code === 200 && res.success) {
            this.form.videoUrl = res.data.link; // 使用link字段获取文件地址
            this.form.videoFileName = params.file.name;
            this.$message.success("视频上传成功");
            this.$refs.form && this.$refs.form.validateField("videoUrl");
          } else {
            this.$message.error(res.msg || "视频上传失败");
          }
        })
        .catch((err) => {
          console.error("视频上传失败:", err);
          this.$message.error("视频上传失败，请检查网络连接");
        })
        .finally(() => {
          this.videoUploadLoading = false;
        });
    },
    // 获取难度标签类型
    getDifficultyTagType(difficulty) {
      switch (difficulty) {
        case "初级":
          return "";
        case "中级":
          return "warning";
        case "高级":
          return "danger";
        default:
          return "";
      }
    },
    /**
     * 获取课程分类标签文本
     * @param {string} categoryValue - 课程分类值
     * @returns {string} 课程分类标签文本
     */
    getCategoryLabel(categoryValue) {
      const category = this.courseCategoryOptions.find(
        (item) => item.value === categoryValue
      );
      return category ? category.label : "";
    },
    // 价格类型变化处理
    handlePriceTypeChange(value) {
      if (value === "free") {
        this.form.price = 0;
      }
    },
    // 表格选择变化处理
    handleSelectionChange(selection) {
      this.selectedCourses = selection;
      // 计算总价并自动填充到付款金额
      this.purchaseForm.amount = this.selectedCoursesTotalPrice;
    },
    // 加载机构数据
    loadOrganizations() {
      getAllOrganizations()
        .then((res) => {
          if (res.code === 200 && res.success) {
            console.log("机构数据:", res.data); // 添加日志查看数据结构
            this.organizationOptions = (res.data || []).map((org) => ({
              label: org.orgName || org.name || `机构${org.id}`,
              value: org.id,
              orgName: org.orgName || org.name || `机构${org.id}`,
            }));
          } else {
            this.$message.error(res.msg || "获取机构列表失败");
          }
        })
        .catch((err) => {
          console.error("获取机构列表失败:", err);
          this.$message.error("获取机构列表失败，请检查网络连接");
        });
    },
    // 加载用户数据
    loadUsers() {
      getAllUsers()
        .then((res) => {
          if (res.code === 200 && res.success) {
            console.log("用户数据:", res.data); // 添加日志查看数据结构
            // 保存全量用户数据
            this.allUserOptions = (res.data || []).map((user) => ({
              label:
                user.userName ||
                user.realName ||
                user.name ||
                `用户${user.userId || user.id}`,
              value: user.userId || user.id,
              userName:
                user.userName ||
                user.realName ||
                user.name ||
                `用户${user.userId || user.id}`,
              orgId: user.orgId || user.organizationId || user.deptId, // 用户所属机构ID
              // 保存原始用户数据，以防需要其他字段
              rawData: user,
            }));
            // 初始状态显示所有用户
            this.userOptions = [...this.allUserOptions];
          } else {
            this.$message.error(res.msg || "获取用户列表失败");
          }
        })
        .catch((err) => {
          console.error("获取用户列表失败:", err);
          this.$message.error("获取用户列表失败，请检查网络连接");
        });
    },
    // 机构选择变化处理
    handleOrganizationChange(orgId) {
      console.log("选择的机构ID:", orgId);

      // 清空用户选择
      this.purchaseForm.user = "";

      if (orgId) {
        // 根据机构ID过滤用户
        this.userOptions = this.allUserOptions.filter((user) => {
          // 如果用户有orgId且与选择的机构ID匹配，则显示
          return user.orgId && user.orgId.toString() === orgId.toString();
        });

        console.log("过滤后的用户列表:", this.userOptions);

        // 如果该机构下没有用户，给出提示
        if (this.userOptions.length === 0) {
          this.$message.warning("该机构下暂无用户，请选择其他机构或联系管理员");
        }
      } else {
        // 如果没有选择机构，显示所有用户
        this.userOptions = [...this.allUserOptions];
      }
    },
    // 线下购买按钮点击
    handleOfflinePurchase() {
      if (this.selectedCourses.length === 0) {
        this.$message.warning("请先选择要购买的课程");
        return;
      }
      // 重置表单
      this.purchaseForm = {
        organization: "",
        user: "",
        amount: this.selectedCoursesTotalPrice,
      };
      // 重置用户选项为全量数据
      this.userOptions = [...this.allUserOptions];
      this.offlinePurchaseVisible = true;
    },
    // 取消线下购买
    cancelOfflinePurchase() {
      this.offlinePurchaseVisible = false;
      this.purchaseForm = {
        organization: "",
        user: "",
        amount: 0,
      };
      // 重置用户选项为全量数据
      this.userOptions = [...this.allUserOptions];
      if (this.$refs.purchaseForm) {
        this.$refs.purchaseForm.resetFields();
      }
    },
    // 提交线下购买
    submitOfflinePurchase() {
      this.$refs.purchaseForm.validate((valid) => {
        if (valid) {
          this.purchaseSubmitLoading = true;

          // 获取选中的机构和用户信息
          const selectedOrg = this.organizationOptions.find(
            (org) => org.value === this.purchaseForm.organization
          );
          const selectedUser = this.userOptions.find(
            (user) => user.value === this.purchaseForm.user
          );

          if (!selectedOrg || !selectedUser) {
            this.$message.error("请选择有效的机构和用户");
            this.purchaseSubmitLoading = false;
            return;
          }

          // 逐个课程提交购买请求
          const purchasePromises = this.selectedCourses.map((course) => {
            const purchaseData = {
              orgId: selectedOrg.value,
              orgName: selectedOrg.orgName || selectedOrg.label,
              userId: selectedUser.value,
              userName: selectedUser.userName || selectedUser.label,
              paidTime: new Date()
                .toLocaleString("zh-CN", {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                  hour: "2-digit",
                  minute: "2-digit",
                  second: "2-digit",
                  hour12: false,
                })
                .replace(/\//g, "-"),
              price: this.purchaseForm.amount, // 使用用户输入的金额
              paidType: 1, // 线下付费类型
              courseId: course.id,
            };

            return offlinePurchaseCourse(course.id, purchaseData);
          });

          // 等待所有购买请求完成
          Promise.all(purchasePromises)
            .then((results) => {
              const successCount = results.filter(
                (res) => res.code === 200 && res.success
              ).length;
              const totalCount = results.length;

              if (successCount === totalCount) {
                this.$message.success(
                  `线下购买记录创建成功！共处理 ${totalCount} 个课程。`
                );
                this.offlinePurchaseVisible = false;
                // 清空选择
                this.$refs.table && this.$refs.table.clearSelection();
                this.selectedCourses = [];
              } else {
                this.$message.warning(
                  `部分课程购买成功（${successCount}/${totalCount}），请检查失败的记录。`
                );
              }
            })
            .catch((err) => {
              console.error("线下购买失败:", err);
              this.$message.error("线下购买失败，请检查网络连接");
            })
            .finally(() => {
              this.purchaseSubmitLoading = false;
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  .filter-container {
    padding: 16px;
    border-radius: 4px;

    .filter-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
      display: block;
    }
  }

  .data-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
  }

  .cover-uploader {
    width: 178px;
    height: 178px;
    .cover-image {
      width: 100%;
      height: 100%;
      display: block;
      object-fit: cover;
      border-radius: 6px;
      overflow: hidden;
    }
    .cover-upload-placeholder {
      width: 100%;
      height: 100%;
      line-height: 178px;
      text-align: center;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .video-uploader {
    .el-upload__tip {
      margin-top: 10px;
    }
  }

  // 线下购买相关样式
  .el-button + .el-button {
    margin-left: 10px;
  }

  .price-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

// 课程对话框样式
:deep(.course-dialog) {
  .el-dialog {
    max-height: 80vh;
    margin-top: 5vh !important;
    margin-bottom: 5vh !important;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 10px 20px;
  }

  .dialog-content-wrapper {
    height: 100%;
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 10px;

    // 隐藏滚动条但保持滚动功能
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: transparent;
      border-radius: 3px;
    }

    // 鼠标悬停时显示滚动条
    &:hover::-webkit-scrollbar-thumb {
      background: #c1c1c1;

      &:hover {
        background: #a8a8a8;
      }
    }

    // 火狐浏览器
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;

    &:hover {
      scrollbar-color: #c1c1c1 transparent;
    }
  }

  .el-dialog__footer {
    flex-shrink: 0;
    padding: 10px 20px 20px;
    border-top: 1px solid #ebeef5;
  }
}
</style>
