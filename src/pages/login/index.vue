<template>
  <div class="login">
    <div class="login-bg">
      <p>{{ title }}</p>
    </div>
    <div>
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
      >
        <h3 class="loginFormTitle">欢 迎 登 录</h3>
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            type="text"
            auto-complete="off"
            placeholder="账号"
          >
            <svg-icon
              slot="prefix"
              icon-class="user"
              class="el-input__icon input-icon"
            />
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            auto-complete="off"
            placeholder="密码"
            show-password
            @keyup.enter.native="handleLogin"
          >
            <svg-icon
              slot="prefix"
              icon-class="password"
              class="el-input__icon input-icon"
            />
          </el-input>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-button
            :loading="loading"
            size="medium"
            type="primary"
            class="login-btn"
            @click.native.prevent="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getUserType } from "@/api/modules/sysUser.js";
import { setToken } from "@/utils/auth";
import axios from "axios";
import Cookies from "js-cookie";
import { JSEncrypt } from "jsencrypt";

export default {
  name: "Login",
  data() {
    return {
      rolePlamentUrl: "https://ssodev.ejclims.com/",
      codeUrl: "",
      activeName: "first",
      cookiePassword: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "用户名不能为空" },
        ],
        password: [
          { required: true, trigger: "blur", message: "密码不能为空" },
        ],
        code: [
          { required: true, trigger: "change", message: "验证码不能为空" },
        ],
      },
      loading: false,
      redirect: undefined,
      mode: process.env.MODE,
    };
  },

  computed: {
    title() {
      return this.$store.state.settings.title;
    },
  },

  watch: {
    $route: {
      handler(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },

  created() {
    this.getCookie();
  },
  methods: {
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username || "",
        password,
        rememberMe: rememberMe || false,
      };
    },
    handleLogin() {
      const that = this;
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 简化登录逻辑，只发送用户名和密码
          let userInfo = {
            username: this.loginForm.username,
            password: this.loginForm.password,
            // 平台标识：1-PC、2-小程序
            platform: 1,
          };
          // 使用配置中的登录URL
          let loginUrl = this.$config.loginUrl;

          axios
            .post(
              `${process.env.VUE_APP_BASE_API}/auth/login`,
              JSON.stringify(userInfo),
              {
                headers: { "Content-Type": "application/json;charset=UTF-8" },
              }
            )
            .then(async function (res) {
              if (res.status === 200 || res.status === true) {
                // 新的API只返回token
                if (!res.data.data.token) {
                  that.$message({
                    message: "认证失败",
                    type: "error",
                  });
                  return;
                }

                // 保存token
                setToken(res.data.data.token);

                // 直接设置默认值，因为API没有返回这些信息
                Cookies.set("ebs_username", res.data.data.realName);
                Cookies.set("userType", "1"); // 默认设置用户类型为1
                Cookies.set("org", JSON.stringify(res.data.data));
                localStorage.setItem(
                  "stb-orgInfo",
                  JSON.stringify(res.data.data)
                );
                getUserType().then((res) => {
                  Cookies.set("lat", res.data.latitude);
                  Cookies.set("lng", res.data.longitude);
                });
                that.$store.dispatch("app/setMenus", res.data.data.menus);

                // 登录成功后直接跳转
                that.$router.push({ path: that.redirect || "/" });
              } else {
                that.$message({
                  message: res.data.msg || "登录失败",
                  type: "error",
                });
              }
            })
            .catch((res) => {
              console.log(res);
              that.$message.error(res.msg || "登录失败!");
            })
            .finally(() => {
              that.loading = false;
            });
        }
      });
    },
    // getUserType() {
    //   const that = this;
    //   // 直接设置默认值并跳转
    //   Cookies.set("userType", "1");
    //   this.$router.push({ path: that.redirect || "/" });
    // },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.login {
  height: 100%;
  background-color: #e1e8ee;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: radial-gradient(
      circle at 86% 7%,
      rgba(40, 40, 40, 0.04) 0%,
      rgba(40, 40, 40, 0.04) 50%,
      rgba(200, 200, 200, 0.04) 50%,
      rgba(200, 200, 200, 0.04) 100%
    ),
    radial-gradient(
      circle at 15% 16%,
      rgba(99, 99, 99, 0.04) 0%,
      rgba(99, 99, 99, 0.04) 50%,
      rgba(45, 45, 45, 0.04) 50%,
      rgba(45, 45, 45, 0.04) 100%
    ),
    radial-gradient(
      circle at 75% 99%,
      rgba(243, 243, 243, 0.04) 0%,
      rgba(243, 243, 243, 0.04) 50%,
      rgba(37, 37, 37, 0.04) 50%,
      rgba(37, 37, 37, 0.04) 100%
    ),
    linear-gradient(0deg, #22deed, #8759d7);
  animation: move-37dfd6fc 30s infinite alternate linear;
}

.loginFormTitle {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #fff;
  font-size: 18px;
  width: 120px;
}

.login-form {
  box-shadow: 0px 0px 5px #888888;
  border-radius: 6px;
  background: #ffffff;
  width: 480px;
  box-shadow: 0px 18px 30px 0px rgba(0, 96, 196, 0.06);
  border-radius: 10px;
  z-index: 999;
  box-sizing: border-box;
  // text-align: center;
  padding: 40px 50px;
  border-radius: 15px;
  background-color: rgba(255, 255, 255, 0.2);

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.login-btn {
  width: 100%;
  height: 40px;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  border: none;
  border-radius: 5px;
  background-color: #6683d2;
  cursor: pointer;
  transition: all 0.3s;
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  // z-index: 1;

  p {
    position: absolute;
    color: #fff;
    left: 70px;
    top: 23px;
    height: 30px;
    line-height: 30px;
    font-size: 30px;
  }

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
