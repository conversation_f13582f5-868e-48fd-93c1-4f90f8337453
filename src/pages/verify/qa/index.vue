<template>
  <div class="wrapper">
    <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot='search'>
        <el-form :inline='true' :model='form'>
          <el-form-item label='问答标题：'>
            <el-input v-model='form.title'></el-input>
          </el-form-item>
        </el-form>
      </div>
      <simple-table :columns='columns' :data='list' :page='page' :optionsWidth='200' @load='loadData'>
        <div slot='checkStatus' slot-scope="scope">
          {{scope.row.checkStatus == 1 ? '审核通过': scope.row.checkStatus == 2 ? '审核不通过' : '待审核'}}
        </div>

        <div slot='options' slot-scope='scope'>
          <el-button type='text' size='mini' @click='handleView(scope.row)'>查看</el-button>
          <el-button v-if="scope.row.checkStatus == 3" type='text' size='mini' @click='handleVerify(1,scope.row)'>审核通过</el-button>
          <el-button v-if="scope.row.checkStatus == 3" type='text' size='mini' @click='handleVerify(2,scope.row)'>审核不通过</el-button>
        </div>
      </simple-table>
    </simple-list-page>
    <detail-dialog ref="detailDialog" @confirm="loadData"></detail-dialog>
  </div>
</template>

<script>
import { QACheck, QAPageList } from '@/api/modules/verify.js';
import DetailDialog from "./components/detail";
export default {
  components: { DetailDialog },
  data() {
    return {
      form: {},
      columns: [
        {
          label: '标题',
          prop: 'title'
        },
        {
          label: '用户昵称',
          prop: 'nickName'
        },
        {
          label: '真实姓名',
          prop: 'realName'
        },
        {
          label: '账号',
          prop: 'userKey'
        },
        {
          label: '联系方式',
          prop: 'telephone'
        },
        {

          label: '发布时间',
          prop: 'pushTime'
        },
        {
          label: '审核状态',
          prop: 'checkStatus',
          slot: 'checkStatus'
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10,
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      QAPageList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleView(row) {
      this.$refs.detailDialog.open(row.id);
    },
    handleVerify(type, row) {
      let str = ''
      if (type === 1) {
        str = '确定审核通过当前常见问题-问答吗'
      } else {
        str = '确定审核不通过当前常见问题-问答吗'
      }
      this.$confirm(str, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.putCheck(type, row)
      }).catch(() => {
      })
    },
    putCheck(type, row) {

      QACheck({ id: row.id, checkStatus: type }).then(res => {
        if (+res.code === 200) {
          this.$message.success(res.msg)
          this.loadData()
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
