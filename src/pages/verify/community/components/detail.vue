<template>
  <div class='wrapper'>
    <el-dialog title='查看' :visible.sync='dialogVisible' width='60%'>
      <el-form :model='detail' ref='form' label-width='80px' label-position='left' size='small' class='dialog-form col-12'>
        <el-form-item label='标题' class='item'>
          <p>{{ detail.title }}</p>
        </el-form-item>
        <el-form-item label='类型' class='item'>
          <p>{{ detail.typeLabel }}</p>
        </el-form-item>
        <el-form-item label='内容' class='item'>
          <p>{{ detail.content }}</p>
        </el-form-item>
        <el-form-item label='图片：' class='item'>
          <div class="SongList">
            <div class="covers">
              <div class="cover" v-for="img in imgList" :key='img'>
                <el-image style="width:100px;height:100px;" fit="fill" :src="img" :preview-src-list="[img]">
                </el-image>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label='用户昵称' class='item'>
          <p>{{ detail.nickName }}</p>
        </el-form-item>
        <el-form-item label='真实姓名' class='item'>
          <p>{{ detail.realName }}</p>
        </el-form-item>
        <el-form-item label='账号' class='item'>
          <p>{{ detail.loginName }}</p>
        </el-form-item>
        <el-form-item label='联系方式' class='item'>
          <p>{{ detail.phone }}</p>
        </el-form-item>
        <el-form-item label='发布时间' class='item'>
          <p>{{ detail.createTime }}</p>
        </el-form-item>
        <p class='sub'>审核状态</p>
        <el-form-item label='审核状态' class='item'>
          <el-select v-model='detail.status' placeholder='请选择' :disabled="!isCheck">
            <el-option v-for='item in optionStatus' :key='item.value' :label='item.label' :value='item.value'>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button v-if="isCheck" type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { approvalCommunityAdopt, approvalCommunityNotAdopt, pcApprovalDetails } from '@/api/modules/verify.js'

export default {
  data() {
    return {
      isCheck: false,
      dialogVisible: false,
      loading: false,
      detail: {
        title: '',
        type: '',
        content: '',
        nick: '',
        realName: '',
        userName: '',
        tel: '',
        createTime: '',
        status: 1,
      },
      imgList: [],
      optionStatus: [
        { value: 1, label: '审核通过' },
        { value: 2, label: '审核不通过，仅用户本人可见' }
      ]
    }
  },
  methods: {
    submit(type, row) {
      let str = ''
      if (this.detail.status === 1) {
        str = '确定审核通过当前数据吗？'

      } else {
        str = '审核不通过的话题仅用户本人可见'
      }
      this.$confirm(str, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.putCheck(this.detail.status)
      }).catch(() => {
      })
    },
    putCheck(type) {
      if (type == 1) {
        approvalCommunityAdopt({ communityId: this.id }).then(res => {
          if (+res.code === 200) {
            this.$message.success(res.msg)
            this.$emit('confirm')
            this.close()
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        approvalCommunityNotAdopt({ communityId: this.id }).then(res => {
          if (+res.code === 200) {
            this.$message.success(res.msg)
            this.close()
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    loadData() {
      this.imgList = []
      pcApprovalDetails(this.id).then(res => {
        this.isCheck = res.data.status == 0 ? true : false
        res.data.status = res.data.status == 0 ? 1 : res.data.status
        this.detail = res.data

        this.imgList = res.data.fileUrls ? res.data.fileUrls : []
      })
    },
    open(id) {
      this.id = id
      console.log(this.id)
      this.dialogVisible = true
      this.loadData()
    },
    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 25%;

  .item {
    margin-bottom: 0;
  }

  .sub {
    font-weight: bold;
    color: #333333;
    font-size: 16px;
    margin: 20px 0 10px 0;
  }
}
.SongList {
  width: 100%;
}
.covers {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.cover {
  display: flex;
  justify-content: center;
  width: 33%;
  padding: 5px;
}
</style>
