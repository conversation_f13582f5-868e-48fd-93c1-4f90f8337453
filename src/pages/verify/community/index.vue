<template>
  <div class='wrapper'>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot='search'>
        <el-form :inline='true' :model='form'>
          <el-form-item label='社区标题：'>
            <el-input v-model='form.title'></el-input>
          </el-form-item>
        </el-form>
      </div>
      <simple-table :columns='columns' :data='list' :page='page' :optionsWidth='200' @load='loadData'>
      <div slot='statusLabel' slot-scope="scope">
         {{scope.row.status == 0 ? '待审核' : scope.row.status == 1 ? '审核通过': '审核不通过'}}
      </div>
        <div slot='options' slot-scope='scope'>
          <el-button type='text' size='mini' @click='handleView(scope.row)'>查看</el-button>
          <el-button v-if="scope.row.status == 0" type='text' size='mini' @click='handleVerify(1,scope.row)'>审核通过</el-button>
          <el-button v-if="scope.row.status == 0" type='text' size='mini' @click='handleVerify(2,scope.row)'>审核不通过</el-button>
        </div>
      </simple-table>
    </simple-list-page>
    <detail-dialog ref='detailDialog' @confirm='loadData'></detail-dialog>
  </div>
</template>

<script>
import DetailDialog from './components/detail'
import {pcApprovalPage,approvalCommunityAdopt, approvalCommunityNotAdopt} from '@/api/modules/verify.js'

export default {
  components: {DetailDialog},
  data() {
    return {
      form: {},
      columns: [
        {
          label: '标题',
          prop: 'title'
        },
        {
          label: '类型',
          prop: 'typeLabel'
        },
        {
          label: '用户昵称',
          prop: 'nickName'
        },
        {
          label: '真实姓名',
          prop: 'userName'
        },
        {
          label: '账号',
          prop: 'loginName'
        },
        {
          label: '联系方式',
          prop: 'phone'
        },
        {
          label: '发布时间',
          prop: 'createTime'
        },
        {
          label: '审核状态',
          prop: 'statusLabel',
          slot: 'statusLabel'

        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page={
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    loadData() {
      let data={
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pcApprovalPage(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleView(row) {
      this.$refs.detailDialog.open(row.id)
    },
    handleVerify(type,row) {
      if (type === 1) {
        this.$confirm('确定审核通过当前数据吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          approvalCommunityAdopt({communityId: row.id}).then(res => {
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.loadData()
              } else {
                this.$message.error(res.msg)
              }
          })
        }).catch(() => {

        })
      } else {
        this.$confirm('审核不通过的话题仅用户本人可见', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          approvalCommunityNotAdopt({communityId: row.id}).then(res => {
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.loadData()
              } else {
                this.$message.error(res.msg)
              }
          })
        }).catch(() => {
        })
      }
    }
  }
}
</script>

<style lang='scss' scoped></style>
