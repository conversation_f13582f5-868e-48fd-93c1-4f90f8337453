<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='样品名称：' class='col-6'>
          <el-input v-model='form.sampleName'></el-input>
        </el-form-item>
        <el-form-item label='被抽单位：' class='col-6'>
          <el-input v-model='form.sampleCompany'></el-input>
        </el-form-item>
        <el-form-item label='审核状态：' class='col-6'>
          <el-select v-model='form.checkStatus' placeholder='请选择'>
            <el-option
              v-for='item in optionStatus'
              :key='item.value'
              :label='item.label'
              :value='item.value'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label='任务类型：' class='col-6'>
          <el-select v-model='form.taskType' placeholder='请选择'>
            <el-option
              v-for='item in optionStatus1'
              :key='item.value'
              :label='item.label'
              :value='item.value'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label='抽样单编号：' class='col-6'>
          <el-input v-model='form.sampleNo'></el-input>
        </el-form-item>
        <el-form-item label='上传人员：' class='col-6'>
          <el-input v-model='form.uploader'></el-input>
        </el-form-item>
        <el-form-item label='联系方式：' class='col-6'>
          <el-input v-model='form.phone'></el-input>
        </el-form-item>
      </el-form>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
      <div slot='checkStatus' slot-scope="scope">
         {{scope.row.checkStatus == 1 ? '审核通过': scope.row.checkStatus == 2 ? '审核不通过' : '待审核'}}
      </div>
      <div slot='taskType' slot-scope="scope">
         {{scope.row.taskType == 1 ? '监督抽检': scope.row.checkStatus == 2 ? '风险监测' : scope.row.checkStatus == 3 ? '评价性抽检' :'抽检监测'}}
      </div>
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' @click='handleView(scope.row)'>查看</el-button>
        <el-button type='text' size='mini' @click='handleVerify(1,scope.row)'>审核通过</el-button>
        <el-button type='text' size='mini' @click='handleVerify(2,scope.row)'>审核不通过</el-button>
      </div>
    </simple-table>

    <el-dialog :visible.sync='showReason' width='40%' title='审核'>
      <el-form ref='form' class='form' label-width='140px' label-position='left' size='mini'>
        <el-form-item label='审核不通过原因：' required>
          <el-input type='textarea' v-model='reasonContent' :rows='8' />
        </el-form-item>
      </el-form>
      <div slot='footer' class='footer'>
        <el-button size='small' @click='handleClose'>取 消</el-button>
        <el-button class='ml20' type='primary' size='small' @click='handleSubmit'>提 交</el-button>
      </div>
    </el-dialog>
  </simple-list-page>
</template>

<script>
import {sharePageList,shareCheck} from '@/api/modules/verify.js'
export default {
  data() {
    return {
      showReason: false,
      curr: null,
      reasonContent: '',
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      form: {},
      columns: [
        {
          label: '样品名称',
          prop: 'sampleName'
        },
        {
          label: '抽样环节',
          prop: 'sampleLink'
        },
        {
          label: '被抽单位名称',
          prop: 'sampleCompany'
        },
        {
          label: '抽样单编号',
          prop: 'sampleNo'
        },
        {
          label: '任务类型',
          prop: 'taskType',
          slot: 'taskType'
        },
        {
          label: '抽样时间',
          prop: 'sampleTime'
        },
        {
          label: '上传时间',
          prop: 'uploadTime'
        },
        {
          label: '上传人员',
          prop: 'uploader'
        },
        {
          label: '联系方式',
          prop: 'phone'
        },
        {
          label: '企业',
          prop: 'enterprise'
        },
        {
          label: '审核状态',
          prop: 'checkStatus',
          slot: 'checkStatus'
        }
      ],

      optionStatus1: [
        {label: '全部', value: null},
        {label: '监督抽检', value: '1'},
        {label: '风险监测', value: '2'},
        {label: '评价性抽检', value: '3'},
        {label: '抽检监测', value: '4'}
      ],
      optionStatus: [
        {label: '全部', value: null},
        {label: '待审核', value: '1'},
        {label: '审核通过', value: '2'},
        {label: '审核不通过', value: '3'}
      ]
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page={
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    loadData() {
      let data={
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      sharePageList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleView() {
      this.$router.push({
        path: '/verify/share/detail',
        query: {
          id: row.id
        }
      })
    },
    handleVerify(type, row) {
      if (type === 1) {
        this.$confirm('确定审核通过当前抽检单吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          this.putCheck(type,row)
        }).catch(() => {
        })
      } else {
        this.curr = row
        this.showReason = true
        this.reasonContent = null
      }
    },
    putCheck(type,row){
      let data = {id: row.id,checkStatus:type}
      if (type == 2) {
        data.rejectReason = this.reasonContent
      }

      shareCheck(data).then(res => {
        if (+res.code === 200) {
          this.$message.success(res.msg)
          this.loadData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleClose() {
      this.showReason = false
      this.curr = null
      this.reasonContent = null
    },
    handleSubmit() {
      this.putCheck(2,this.curr)
    }
  }
}
</script>

<style lang='scss' scoped>
.form {
  margin: 20px 50px;
}
</style>
