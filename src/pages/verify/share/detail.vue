<template>
  <div class='wrapper'>
    <el-form label-width='145px' label-position='left' size='mini'>
      <el-form-item label='抽检单图片：' class='item'>
        <el-image :src='data.photo' class='img' :preview-src-list='[data.photo]' />
      </el-form-item>
    </el-form>
    <div v-for='group in form' class='group'>
      <h4>{{ group.title }}</h4>
      <el-form :model='data[group.key]' label-width='145px' label-position='left' size='mini' class='form'>
        <el-form-item v-for='item in group.content' :label="item.title + '：'" class='item'>
          <label>{{ data[item.key] }}</label>
        </el-form-item>
      </el-form>
    </div>
    <h4>审核状态</h4>
    <el-form :model='params' label-width='145px' label-position='left' size='mini' class='form'>
      <el-form-item label='审核状态：' class='item' style='width: 80%'>
        <el-select v-model='params.checkStatus'>
          <el-option v-for='item in optionStatus' :key='item.id' :label='item.name' :value='item.id'></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label='审核不通过原因：' v-if='params.checkStatus === 2' class='item'>
        <el-input type='textarea' v-model='params.rejectReason' :rows='4' />
      </el-form-item>
    </el-form>
    <div class='footer'>
      <el-button size='small' @click='handleBack'>取消</el-button>
      <el-button class='ml20' type='primary' size='small' @click='handleSubmit'>确定</el-button>
    </div>
  </div>
</template>

<script>
import { shareDetail } from '@/api/modules/verify.js'

export default {
  data() {
    return {
      loading: false,
      optionStatus: [
        { id: 1, name: '审核通过' },
        { id: 2, name: '审核不通过' }
      ],
      params: {
        id: '',
        checkStatus: 1,
        rejectReason: ''
      },
      form: [
        {
          title: '基础信息',
          content: [
            { title: '抽检单编号', key: 'sampleTaskNumber' },
            { title: '任务来源', key: 'planSource' },
            { title: '任务类型', key: 'planType' },
            { title: '被抽单位名称', key: 'enterpriseName' },
            { title: '抽样环节', key: 'samplingLink' },
            { title: '营业执照号', key: 'socialBusiness' },
            { title: '许可证编号', key: 'licenseNumber' },
            { title: '抽样地点', key: 'enterpriseSamplingSpot' },
            { title: '区域类型', key: 'areaType' },
            { title: '法人代表', key: 'juridicalPerson' },
            // {title: "年销售额（万元）", key: "saleroom"},
            { title: '联系电话', key: 'enterpriseLinkTel' },
            { title: '单位地址', key: 'enterpriseAddress' },
            { title: '联系人', key: 'enterpriseLinkman' }
          ]
        },
        {
          title: '样品信息',
          content: [
            { title: '样品来源', key: 'sampleSource' },
            { title: '样品属性', key: 'property' },
            { title: '样品类型', key: 'type' },
            { title: '样品名称', key: 'sampleName' },
            { title: '购进日期', key: 'productionDate' },
            { title: '商标', key: 'tradeMark' },
            { title: '条形码', key: 'barCode' },
            { title: '规格型号', key: 'specification' },
            { title: '样品批号', key: 'lotNumber' },
            { title: '保质期', key: 'durabilityDate' },
            { title: '执行标准/技术文件', key: 'executiveStandard' },
            { title: '质量等级', key: 'qualityGrade' },
            { title: '单价', key: 'price' },
            { title: '是否出口', key: 'isImport' },
            { title: '原产地', key: 'countryOrigin' },
            { title: '抽样基数', key: 'samplingBaseAmount' },
            { title: '抽样数量', key: 'sampleAmount' },
            { title: '备样数量', key: 'preparationAmount' },
            { title: '包装分类', key: 'packageType' },
            { title: '抽样方式', key: 'sampleMethod' },
            { title: '存储条件', key: 'storageCondition' },
            { title: '抽样日期', key: 'sampleDate' }
          ]
        },
        {
          title: '生产企业信息（标示）',
          content: [
            { title: '生产者名称', key: 'producerName' },
            { title: '生产许可证', key: 'productionLicenseNumber' },
            { title: '生产者地址', key: 'producerAddress' },
            { title: '联系电话', key: 'producerTelephone' }
          ]
        },
        {
          title: '第三方企业信息（标示）',
          content: [
            { title: '企业名称', key: 'thirdName' },
            { title: '企业许可证号', key: 'thirdLicenseNumber' },
            { title: '企业性质', key: 'thirdPartyType' },
            { title: '企业地址', key: 'thirdAddress' },
            { title: '联系电话', key: 'thirdTelephone' }
          ]
        },
        {
          title: '抽样单位信息',
          content: [
            { title: '抽样单位', key: 'sampleOrgName' },
            { title: '单位地址', key: 'sampleOrgAddress' },
            { title: '联系人', key: 'sampleOrgContactPerson' },
            { title: '联系方式', key: 'sampleOrgTelephone' },
            { title: '传真', key: 'sampleOrgFax' }
          ]
        },
        {
          title: '上传人员',
          content: [
            { title: '上传人员', key: 'addUser' },
            { title: '联系方式', key: 'addTel' },
            { title: '企业', key: 'addEnterprise' }
          ]
        }
      ],
      data: {}
    }
  },
  created() {
    this.params.id = this.$route.query.id
    this.loadData()
  },
  methods: {
    loadData() {
      this.loading = true
      shareDetail(this.params.id)
        .then((res) => {

          this.form[1].content[4].title = res.data.data.sampleSourceDate + '日期'
          this.data = res.data.data
          console.log(this.data)
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSubmit() {
      changeStatus(this.params)
        .then((res) => {
          this.handleBack()
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleBack() {
      this.$router.$avueRouter.closeTag()
      this.$router.back()
    }
  }
}
</script>

<style lang='scss' scoped>
.wrapper {
  width: 100%;
  border-radius: 10px;
  background-color: #ffffff;
  //display: flex;
  //justify-content: center;
  margin: 15px 0;
  padding: 20px 60px;

  .group {
    width: 100%;
  }

  h4 {
    margin: 20px 0 10px 0;
  }

  .form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }

  .item {
    width: 32%;
    margin: 10px 10px 10px 0;

    .img {
      height: 200px;
    }
  }

  .footer {
    text-align: center;
    margin: 30px 0;
  }
}
</style>
