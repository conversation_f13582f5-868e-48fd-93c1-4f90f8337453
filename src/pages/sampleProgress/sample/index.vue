<template>
  <div class="wrapper">
    <simple-list-page
      @query="handleQuery"
      @reset="handleReset"
      v-loading="loading"
    >
      <div slot="top-options">
        <el-form :inline="true" :model="form" size="small">
          <el-form-item label="任务来源：" prop="taskSource">
            <el-select v-model="form.taskSource" filterable clearable>
              <el-option
                v-for="item in taskSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报送分类B：" prop="classB">
            <el-select v-model="form.classB" filterable clearable>
              <el-option
                v-for="item in options"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否完成：" prop="isComplete">
            <el-select v-model="form.isComplete" clearable>
              <el-option label="是" value="1"></el-option>
              <el-option label="否" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="食品大类：" prop="cate1">
            <el-select v-model="form.cate1" filterable clearable>
              <el-option
                v-for="item in options2"
                :key="item.cateName"
                :label="item.cateName"
                :value="item.cateName"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="食品细类：" prop="cate4">
            <el-cascader
              v-model="form.cate4"
              :props="propsObj"
              :options="options2"
              :show-all-levels="false"
            ></el-cascader>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div slot="bottom-options">
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="importExcel"
          >导出</el-button
        >
      </div>

      <u-table
        ref="uTable"
        :span-method="objectSpanMethod"
        :summary-method="getSummaries"
        :cell-style="tableRowClassName"
        :use-virtual="useVirtual"
        :row-height="37"
        :max-height="700"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        :border="true"
        show-summary
        size="mini"
        style="width: 100%; margin-top: 5px; margin-bottom: 5px"
      >
        <u-table-column label="序号" type="index" align="center" width="60%" />
        <u-table-column
          prop="taskSource"
          align="center"
          label="任务来源"
          min-width="120"
          :show-overflow-tooltip="true"
        ></u-table-column>
        <u-table-column
          prop="newscbname"
          align="center"
          label="任务类别"
          min-width="150"
          :show-overflow-tooltip="true"
        ></u-table-column>
        <u-table-column
          prop="classB"
          align="center"
          label="报送分类B"
          min-width="150"
          :show-overflow-tooltip="true"
        ></u-table-column>
        <u-table-column
          prop="cate1"
          align="center"
          label="食品大类"
          min-width="60"
          :show-overflow-tooltip="true"
        ></u-table-column>
        <!-- <u-table-column prop="cate2" align="center" label="食品亚类" min-width="60" :show-overflow-tooltip="true"></u-table-column>
        <u-table-column prop="cate3" align="center" label="食品次亚类" min-width="60" :show-overflow-tooltip="true"></u-table-column> -->
        <u-table-column
          prop="cate4"
          align="center"
          label="食品细类"
          min-width="60"
          :show-overflow-tooltip="true"
        ></u-table-column>
        <u-table-column
          prop="planCount"
          align="center"
          label="计划数"
        ></u-table-column>
        <u-table-column
          prop="released"
          align="center"
          label="完成数"
        ></u-table-column>
        <u-table-column
          prop="residueCount"
          align="center"
          label="剩余数"
        ></u-table-column>
        <u-table-column
          prop="planFinishDate"
          align="center"
          label="计划抽样完成日期"
          :show-overflow-tooltip="true"
        ></u-table-column>
        <u-table-column
          prop="delC"
          align="center"
          label="备注"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row, $index }">
            <el-link
              style="font-size: 12px"
              alink="blue"
              @click="showBackSample(row, $index)"
              >{{ row.delC }}</el-link
            >
          </template>
        </u-table-column>
      </u-table>

      <!-- 隐藏table用于导出，虚拟表格无法获取所有元素 -->
      <el-table
        :data="progressArr"
        :span-method="objectSpanMethod"
        :summary-method="getSummariesElementUI"
        border
        ref="handSelectTest"
        show-summary
        v-show="false"
      >
        <el-table-column label="序号" type="index" align="center" />
        <el-table-column prop="taskSource" label="任务来源"></el-table-column>
        <el-table-column prop="newscbname" label="任务类别"></el-table-column>
        <el-table-column prop="classB" label="报送分类B"></el-table-column>
        <el-table-column prop="cate1" label="食品大类"></el-table-column>
        <el-table-column prop="cate4" label="食品细类"></el-table-column>
        <el-table-column prop="planCount" label="计划数"></el-table-column>
        <el-table-column prop="released" label="完成数"></el-table-column>
        <el-table-column prop="residueCount" label="剩余数"></el-table-column>
        <el-table-column
          prop="planFinishDate"
          label="计划抽样完成日期"
        ></el-table-column>
        <el-table-column prop="delC" label="备注"></el-table-column>
      </el-table>
    </simple-list-page>
  </div>
</template>

<script>
import { selectClassB, findTreeListAll } from "@/api/modules/entrust.js";
import {
  selectNewscbname,
  selectSgList,
} from "@/api/modules/sampleProgress.js";
import { fetchAndCacheData } from "../../../utils/cacheUtils.js";
import FileSaver from "file-saver";
import { UTable, UTableColumn } from "umy-ui";
import XLSX from "xlsx";
import { getClassTree } from "@/api/modules/task.js";

export default {
  components: {
    [UTable.name]: UTable,
    [UTableColumn.name]: UTableColumn,
  },
  data() {
    return {
      loading: false,
      form: {},
      tableData: [],
      progressArr: [],
      options: [],
      useVirtual: true,
      options2: [], // 食品类别树型数据
      taskSourceOptions: [], // 任务来源选项
      propsObj: {
        // emitPath: false,
        value: "cateName",
        label: "cateName",
        children: "children",
      }, // 细类级联配置
    };
  },
  mounted() {
    this.loading = true;
    setTimeout(() => {
      this.loadData();
    }, 1000);
    this.selectB();
    this.findTreeListAll();
    this.fetchTaskSourceOptions(); // 获取任务来源选项
  },
  methods: {
    handleQuery(reset = false) {
      this.loadData(reset);
    },
    handleReset() {
      this.form = {};
      this.handleQuery(true);
    },
    async loadData(reset = false) {
      let param = {
        ...this.form,
        cate4: this.form.cate4?.length ? this.form.cate4[3] : null,
      };
      const that = this;
      fetchAndCacheData((data) => {
        if (
          data.length > 0 &&
          Object.getOwnPropertyNames(param).length === 0 &&
          !reset
        ) {
          that.tableData = [];
          that.progressArr = data;
          that.$refs.uTable.reloadData(data);
          that.loadRowData(data);
        } else {
          that.getData(param);
        }
      });
    },
    // 获取数据
    getData(data) {
      this.loading = true;
      selectSgList(data)
        .then((res) => {
          const progressArr = localStorage.setItem(
            "progressArr",
            JSON.stringify(res.data)
          );
          // this.loading = false
          if (!progressArr) {
            this.tableData = [];
            this.progressArr = res.data;
            this.loadRowData(res.data);
            this.$refs.uTable.reloadData(res.data);
          }
          // [{ spotType: 'spotType', classB: 'classB' }]
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.residueCount <= 0) {
        return { color: "green" };
      }
      return "";
    },
    // selectA: function(data) {
    //   this.$api.foodtest_statistics_sample.getHnyScanameList(data).then(res => {
    //     if (res.data.code === 200) {
    //       const op = res.data.object;
    //       op.forEach((item, index) => {
    //         this.classAArr.push({
    //           label: item.label,
    //           value: item.value
    //         })
    //       })
    //     } else {
    //       this.$message({message: '获取报送分类a列表失败, ' + res.data.message, type: 'error'})
    //     }
    //   });
    // },
    showBackSample: function (row, index) {
      window.dialogOpen(
        this,
        BackSamplePage,
        { cate4: row.cate4, classB: row.classB },
        "退样中样品",
        "80%"
      );
    },

    selectB: function (data) {
      selectClassB().then((res) => {
        this.options = res.data;
      });
    },
    loadRowData(data) {
      let conDot = 0;
      data.forEach((item, index) => {
        if (index == 0) {
          this.tableData.push(1);
        } else {
          if (item.key === data[index - 1].key) {
            this.tableData[conDot] += 1;
            this.tableData.push(0);
          } else {
            conDot = index;
            this.tableData.push(1);
          }
        }
      });
      this.loading = false;
      // this.tableData
    },

    showSearch: function () {
      this.loading = true;
      var data = {};
      data.newscbname = this.newClassB;
      data.classB = this.classB;
      data.isComplete = this.isComplete;
      data.cate1 = this.cate1;
      data.cate4 = this.cate4;
      this.$api.foodtest_statistics_sample
        .getHnySampleProgress(data)
        .then((res) => {
          this.loading = false;
          if (res.data.code === 200) {
            this.tableData = [];
            this.progressArr = res.data.object;
            this.loadRowData();
          } else {
            this.$message({
              message: "操作失败, " + res.data.message,
              type: "error",
            });
          }
        });
    },

    importExcel: function () {
      //excelName --设置导出的excel名称
      //report-table --对应的要导出的el-table的ref名称
      try {
        // 首先检查数据是否存在
        if (!this.progressArr || this.progressArr.length === 0) {
          this.$message.warning("暂无数据可导出");
          return;
        }

        // 确保导出表格的数据中包含任务类别
        const exportData = this.progressArr.map((item) => {
          // 如果数据中缺少newscbname字段但有spotType字段，使用spotType作为备选
          if (!item.newscbname && item.spotType) {
            item.newscbname = item.spotType;
          }
          // 如果两个字段都没有，使用默认值
          if (!item.newscbname) {
            item.newscbname = item.newClassB || "未知类别";
          }
          return { ...item }; // 创建数据的副本，避免修改原始数据
        });

        // 使用Vue的响应式方法更新数据，避免直接修改props
        this.$nextTick(() => {
          // 先将数据设置到隐藏表格的data属性
          this.$set(this.$refs.handSelectTest, "data", exportData);

          // 再次等待DOM更新
          this.$nextTick(() => {
            // 获取表格DOM
            const $e = this.$refs.handSelectTest.$el;
            // 如果表格加了fixed属性，则导出的文件会生产两份一样的数据，所以可在这里判断一下
            let $table = $e.querySelector(".el-table__fixed");
            if (!$table) {
              $table = $e;
            }

            // 为了返回单元格原始字符串，设置{ raw: true }
            const wb = XLSX.utils.table_to_book($table, { raw: true });

            // 设置样式
            for (const key in wb) {
              if (key.indexOf("!") === -1 && wb[key].v) {
                wb[key].s = {
                  font: {
                    sz: 13,
                    bold: false,
                    color: { rgb: "000000" },
                  },
                  alignment: {
                    horizontal: "center",
                    vertical: "center",
                    wrap_text: true,
                  },
                  border: {
                    top: { style: "thin" },
                    bottom: { style: "thin" },
                    left: { style: "thin" },
                    right: { style: "thin" },
                  },
                };
              }
            }

            // 生成并导出Excel文件
            const wbout = XLSX.write(wb, {
              bookType: "xlsx",
              bookSST: true,
              type: "array",
            });

            FileSaver.saveAs(
              new Blob([wbout], { type: "application/octet-stream" }),
              `抽样进度统计.xlsx`
            );
          });
        });
      } catch (e) {
        if (typeof console !== "undefined") console.error(e);
      }
    },

    // 用于导出excel的合并格式
    getSummariesElementUI(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = "合计";
          return;
        }
        if (!(index === 5 || index === 6 || index === 7)) {
          sums[index] = "/";
          return;
        }
        const values = data.map((item) =>
          Number(item[column.property] == "" ? "/" : item[column.property])
        );
        const tData = this.tableData;
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr, i) => {
            const value = Number(curr);
            //判断当前行是否被合并，tData[i]等于0时为被合并行
            //排除6列和8列以外无合并列
            if (index === 8 || index === 10) {
              if (tData[i] !== 0) {
                if (!isNaN(value) && value != "") {
                  return prev + curr;
                } else {
                  return prev;
                }
              } else {
                return prev;
              }
            } else {
              if (!isNaN(value) && value != "") {
                return prev + curr;
              } else {
                return prev;
              }
            }
          }, 0);
          sums[index] += " 批";
        } else {
          sums[index] = "/";
        }
      });
      return sums;
    },

    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = "合计";
          return;
        }
        if (!(index === 5 || index === 6 || index === 7)) {
          sums[index] = "/";
          return;
        }
        const values = data.map((item) =>
          Number(item[column.property] == "" ? "/" : item[column.property])
        );
        const tData = this.tableData;
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr, i) => {
            const value = Number(curr);
            //判断当前行是否被合并，tData[i]等于0时为被合并行
            //排除6列和8列以外无合并列
            if (index === 8 || index === 10) {
              if (tData[i] !== 0) {
                if (!isNaN(value) && value != "") {
                  return prev + curr;
                } else {
                  return prev;
                }
              } else {
                return prev;
              }
            } else {
              if (!isNaN(value) && value != "") {
                return prev + curr;
              } else {
                return prev;
              }
            }
          }, 0);
          sums[index] += " 批";
        } else {
          sums[index] = "/";
        }
      });
      return [sums];
    },

    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 8) {
        if (this.tableData[rowIndex]) {
          return {
            rowspan: this.tableData[rowIndex],
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }

      if (columnIndex === 10) {
        if (this.tableData[rowIndex]) {
          return {
            rowspan: this.tableData[rowIndex],
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    // 食品分类树型列表查询
    findTreeListAll() {
      findTreeListAll().then((res) => {
        this.options2 = this.addLeafProperty(res.data);
      });
    },
    // 增加树型节点标识
    addLeafProperty(data) {
      return data.map((item) => {
        if (item.children && item.children.length > 0) {
          item.children = this.addLeafProperty(item.children);
        } else {
          item.children = null;
        }
        return item;
      });
    },
    // 获取任务来源选项
    fetchTaskSourceOptions() {
      getClassTree()
        .then((res) => {
          if (res.code === 200 && res.data) {
            // 处理任务来源选项
            this.taskSourceOptions = res.data.map((item) => ({
              value: item.name,
              label: item.name,
            }));
          } else {
            this.$message.error(res.msg || "获取任务来源数据失败");
          }
        })
        .catch((err) => {
          console.error("获取任务来源数据失败:", err);
          this.$message.error("获取任务来源数据失败，请检查网络连接");
        });
    },
  },
};
</script>

<style lang="scss" scoped>
/* 级联选择器样式 */
:deep(.el-cascader) {
  line-height: normal;
  height: auto;
  display: block;
  width: 100%;
}

:deep(.el-cascader__dropdown) {
  min-width: 200px !important;
}

:deep(.el-cascader-menus) {
  min-width: 200px !important;
}

:deep(.el-cascader-menu) {
  min-width: 200px !important;
  max-height: 300px !important;
}
</style>
