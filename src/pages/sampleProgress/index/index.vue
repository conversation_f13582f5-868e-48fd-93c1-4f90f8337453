<template>
  <div style="width: 100%">
    <el-tabs v-model="tabIndex" @tab-click="handleClick">
      <el-tab-pane label="计划完成情况" name="0"></el-tab-pane>
      <el-tab-pane label="抽样进度查询" name="1"></el-tab-pane>
      <el-tab-pane label="总进度查询" name="2"></el-tab-pane>
    </el-tabs>
    <plan v-if="tabIndex == 0"></plan>
    <sample v-if="tabIndex == 1"></sample>
    <total v-if="tabIndex == 2"></total>
  </div>
</template>

<script>
import sample from "../sample/index.vue";
import total from "../total/index.vue";
import plan from "../plan/index.vue";

export default {
  components: { sample, total, plan },
  data() {
    return {
      tabIndex: "0",
    };
  },
  methods: {
    handleClick(tab, event) {
      this.tabIndex = tab.name;
    },
  },
};
</script>

<style></style>
