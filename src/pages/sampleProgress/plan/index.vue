<template>
  <div class="wrapper">
    <simple-list-page
      @query="handleQuery"
      @reset="handleReset"
      v-loading="loading"
    >
      <div slot="top-options">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="任务来源：" prop="taskSource">
            <el-select v-model="filterForm.taskSource" filterable clearable>
              <el-option
                v-for="item in taskSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报送分类B：" prop="classB">
            <el-select v-model="filterForm.classB" filterable clearable>
              <el-option
                v-for="item in options"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="任务名称：" prop="taskName">
            <el-select v-model="filterForm.taskName" filterable clearable>
              <el-option
                v-for="item in taskNameOptions"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="计划执行状态：" prop="executionStatus">
            <el-select v-model="filterForm.executionStatus" clearable>
              <el-option label="执行中" value="执行中"></el-option>
              <el-option label="未部署" value="未部署"></el-option>
              <el-option label="已完成" value="已完成"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div slot="bottom-options" style="font-size: 16px; font-weight: bold">
        计划完成情况
      </div>

      <el-table
        :data="flattenedData"
        border
        size="mini"
        style="width: 100%; margin-top: 5px; margin-bottom: 5px"
        :header-cell-style="{ background: '#f5f7fa', color: '#333' }"
        :span-method="objectSpanMethod"
      >
        <el-table-column label="序号" width="60" align="center">
          <template slot-scope="scope">
            {{ scope.row.recordIndex }}
          </template>
        </el-table-column>
        <el-table-column
          prop="taskSource"
          label="任务来源"
          align="center"
          min-width="120"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="categoryA"
          label="报送分类A"
          align="center"
          min-width="120"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="classB"
          label="报送分类B"
          align="center"
          min-width="150"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="taskName"
          label="任务名称"
          align="center"
          min-width="150"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="sourceType"
          label="任务来源省份"
          align="center"
          min-width="120"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="sampleSection"
          label="抽样环节"
          align="center"
          min-width="120"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="batchCount"
          label="计划批次数量"
          align="center"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="completedBatchCount"
          label="已完成批次数"
          align="center"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="totalPlanBatch"
          label="总计划批次"
          align="center"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="executionStatus"
          label="计划执行状态"
          align="center"
          min-width="120"
        >
          <template slot-scope="scope">
            <span :class="getStatusClass(scope.row.executionStatus)">{{
              scope.row.executionStatus
            }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-show="pagination.total > 0" style="height: 45px">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.current"
          :limit.sync="pagination.size"
          @pagination="handlePage"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </simple-list-page>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import FileSaver from "file-saver";
import XLSX from "xlsx";
import { selectClassB } from "@/api/modules/entrust.js";
import { getClassTree } from "@/api/modules/task.js";
import { http } from "@/api/request";

export default {
  name: "PlanProgress",
  components: {
    Pagination,
  },
  data() {
    return {
      loading: false,
      // 筛选表单
      filterForm: {
        taskSource: "",
        classB: "",
        taskName: "",
        executionStatus: "",
      },
      // 选项数据
      taskSourceOptions: [], // 任务来源选项
      options: [], // 报送分类B选项
      taskNameOptions: [], // 任务名称选项，将由API数据填充
      // 表格数据
      tableData: [],
      // 表格数据处理后的展平结果
      flattenedData: [],
      // 单元格合并配置
      spanArr: {
        recordIndex: [],
        taskSource: [],
        categoryA: [],
        classB: [],
        taskName: [],
        sourceType: [],
      },
      // 分页
      pagination: {
        current: 1,
        size: 10,
        total: 0,
      },
    };
  },
  mounted() {
    this.loading = true;
    // 获取筛选选项数据
    this.fetchTaskSourceOptions();
    this.selectB();
    // 获取表格数据
    this.fetchData();
  },
  methods: {
    // 搜索
    handleQuery(reset = false) {
      if (reset) {
        this.pagination.current = 1;
      }
      this.fetchData();
    },
    // 重置
    handleReset() {
      this.filterForm = {
        taskSource: "",
        classB: "",
        taskName: "",
        executionStatus: "",
      };
      this.handleQuery(true);
    },
    // 处理分页
    handlePage() {
      this.fetchData();
    },
    // 切换每页显示条数
    handleSizeChange(size) {
      this.pagination.size = size;
      this.fetchData();
    },
    // 切换页码
    handleCurrentChange(current) {
      this.pagination.current = current;
      this.fetchData();
    },
    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case "执行中":
          return "status-running";
        case "已完成":
        case "完成":
          return "status-completed";
        case "未部署":
          return "status-pending";
        default:
          return "";
      }
    },
    // 获取数据
    fetchData() {
      this.loading = true;

      // 构造请求参数
      const params = {
        current: this.pagination.current,
        size: this.pagination.size,
      };

      // 添加筛选条件
      if (this.filterForm.taskSource) {
        params.taskSource = this.filterForm.taskSource;
      }

      if (this.filterForm.classB) {
        params.classB = this.filterForm.classB;
      }

      if (this.filterForm.taskName) {
        params.taskName = this.filterForm.taskName;
      }

      // 调用真实API
      http
        .get("/ampleSchedule/planOver", params)
        .then((res) => {
          if (res.code === 200 && res.success) {
            this.tableData = res.data.records || [];
            this.pagination.total = parseInt(res.data.total) || 0;
            // 处理数据格式转换
            this.processTableData();
          } else {
            this.$message.error(res.msg || "获取计划完成情况数据失败");
            this.tableData = [];
            this.flattenedData = [];
            this.pagination.total = 0;
          }
        })
        .catch((err) => {
          console.error("获取计划完成情况数据失败:", err);
          this.$message.error("获取计划完成情况数据失败，请检查网络连接");
          this.tableData = [];
          this.flattenedData = [];
          this.pagination.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 处理表格数据格式转换
    processTableData() {
      // 清空之前的数据
      this.flattenedData = [];
      this.spanArr = {
        recordIndex: [],
        taskSource: [],
        categoryA: [],
        classB: [],
        taskName: [],
        sourceType: [],
      };

      // 如果没有数据则直接返回
      if (!this.tableData || this.tableData.length === 0) {
        return;
      }

      // 展平嵌套数据结构
      this.tableData.forEach((item, recordIndex) => {
        // 确保有任务名称列表
        if (!item.taskNameList || item.taskNameList.length === 0) {
          return;
        }

        // 遍历每个任务名称
        item.taskNameList.forEach((taskNameItem) => {
          // 确保有环节类型列表
          if (
            !taskNameItem.stepTypeList ||
            taskNameItem.stepTypeList.length === 0
          ) {
            return;
          }

          // 遍历每个环节类型
          taskNameItem.stepTypeList.forEach((stepItem) => {
            // 创建一条扁平化的数据
            this.flattenedData.push({
              // 记录原始数据的索引，用于序号显示
              recordIndex: recordIndex + 1,
              taskSource: item.taskSource || "",
              categoryA: item.classA || "", // 注意这里字段名不同
              classB: item.classB || "",
              taskName: taskNameItem.taskName || "",
              sourceType: item.taskSourceProvince || "", // 注意这里字段名不同
              sampleSection: stepItem.stepTypeName || "",
              batchCount: stepItem.planTotal || 0,
              completedBatchCount: stepItem.finishNum || 0,
              totalPlanBatch: item.planBatchCount || 0,
              executionStatus: stepItem.deployStatus || "",
            });
          });
        });
      });

      // 计算单元格合并数据
      this.getSpanArr(this.flattenedData);
    },
    // 计算单元格合并配置
    getSpanArr(data) {
      // 初始化合并数组
      this.spanArr = {
        recordIndex: [],
        taskSource: [],
        categoryA: [],
        classB: [],
        taskName: [],
        sourceType: [],
      };

      // 临时保存行索引
      const recordPos = {};
      const taskSourcePos = {};
      const categoryAPos = {};
      const classBPos = {};
      const taskNamePos = {};
      const sourceTypePos = {};

      // 初始化计数和位置
      data.forEach((item, index) => {
        // 序号合并计算
        if (index === 0) {
          this.spanArr.recordIndex.push(1);
          recordPos[item.recordIndex] = 0;
        } else {
          if (item.recordIndex === data[index - 1].recordIndex) {
            this.spanArr.recordIndex[recordPos[item.recordIndex]] += 1;
            this.spanArr.recordIndex.push(0);
          } else {
            this.spanArr.recordIndex.push(1);
            recordPos[item.recordIndex] = index;
          }
        }

        // 任务来源合并计算
        if (index === 0) {
          this.spanArr.taskSource.push(1);
          taskSourcePos[item.taskSource] = 0;
        } else {
          if (item.taskSource === data[index - 1].taskSource) {
            this.spanArr.taskSource[taskSourcePos[item.taskSource]] += 1;
            this.spanArr.taskSource.push(0);
          } else {
            this.spanArr.taskSource.push(1);
            taskSourcePos[item.taskSource] = index;
          }
        }

        // 报送分类A合并计算
        if (index === 0) {
          this.spanArr.categoryA.push(1);
          categoryAPos[item.categoryA] = 0;
        } else {
          if (
            item.categoryA === data[index - 1].categoryA &&
            item.taskSource === data[index - 1].taskSource
          ) {
            this.spanArr.categoryA[categoryAPos[item.categoryA]] += 1;
            this.spanArr.categoryA.push(0);
          } else {
            this.spanArr.categoryA.push(1);
            categoryAPos[item.categoryA] = index;
          }
        }

        // 报送分类B合并计算
        if (index === 0) {
          this.spanArr.classB.push(1);
          classBPos[item.classB] = 0;
        } else {
          if (
            item.classB === data[index - 1].classB &&
            item.categoryA === data[index - 1].categoryA &&
            item.taskSource === data[index - 1].taskSource
          ) {
            this.spanArr.classB[classBPos[item.classB]] += 1;
            this.spanArr.classB.push(0);
          } else {
            this.spanArr.classB.push(1);
            classBPos[item.classB] = index;
          }
        }

        // 任务名称合并计算
        const taskNameKey = `${item.taskSource}_${item.classB}_${item.taskName}`;
        if (index === 0) {
          this.spanArr.taskName.push(1);
          taskNamePos[taskNameKey] = 0;
        } else {
          const prevTaskNameKey = `${data[index - 1].taskSource}_${
            data[index - 1].classB
          }_${data[index - 1].taskName}`;
          if (taskNameKey === prevTaskNameKey) {
            this.spanArr.taskName[taskNamePos[taskNameKey]] += 1;
            this.spanArr.taskName.push(0);
          } else {
            this.spanArr.taskName.push(1);
            taskNamePos[taskNameKey] = index;
          }
        }

        // 任务来源省份合并计算
        if (index === 0) {
          this.spanArr.sourceType.push(1);
          sourceTypePos[item.sourceType] = 0;
        } else {
          if (
            item.sourceType === data[index - 1].sourceType &&
            item.taskSource === data[index - 1].taskSource
          ) {
            this.spanArr.sourceType[sourceTypePos[item.sourceType]] += 1;
            this.spanArr.sourceType.push(0);
          } else {
            this.spanArr.sourceType.push(1);
            sourceTypePos[item.sourceType] = index;
          }
        }
      });
    },
    // 单元格合并方法 - 用于表格的span-method属性
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const rowspan = this.spanArr.recordIndex[rowIndex];
        const colspan = rowspan > 0 ? 1 : 0;
        return {
          rowspan,
          colspan,
        };
      } else if (columnIndex === 1) {
        const rowspan = this.spanArr.taskSource[rowIndex];
        const colspan = rowspan > 0 ? 1 : 0;
        return {
          rowspan,
          colspan,
        };
      } else if (columnIndex === 2) {
        const rowspan = this.spanArr.categoryA[rowIndex];
        const colspan = rowspan > 0 ? 1 : 0;
        return {
          rowspan,
          colspan,
        };
      } else if (columnIndex === 3) {
        const rowspan = this.spanArr.classB[rowIndex];
        const colspan = rowspan > 0 ? 1 : 0;
        return {
          rowspan,
          colspan,
        };
      } else if (columnIndex === 4) {
        const rowspan = this.spanArr.taskName[rowIndex];
        const colspan = rowspan > 0 ? 1 : 0;
        return {
          rowspan,
          colspan,
        };
      } else if (columnIndex === 5) {
        const rowspan = this.spanArr.sourceType[rowIndex];
        const colspan = rowspan > 0 ? 1 : 0;
        return {
          rowspan,
          colspan,
        };
      } else if (columnIndex === 9) {
        const rowspan = this.spanArr.classB[rowIndex];
        const colspan = rowspan > 0 ? 1 : 0;
        return {
          rowspan,
          colspan,
        };
      }
    },
    // 获取任务来源选项
    fetchTaskSourceOptions() {
      getClassTree()
        .then((res) => {
          if (res.code === 200 && res.data) {
            // 处理任务来源选项
            this.taskSourceOptions = res.data.map((item) => ({
              value: item.name,
              label: item.name,
            }));

            // 提取任务名称选项
            this.taskNameOptions = this.extractTaskNames(res.data);
          } else {
            this.$message.error(res.msg || "获取任务来源数据失败");
          }
        })
        .catch((err) => {
          console.error("获取任务来源数据失败:", err);
          this.$message.error("获取任务来源数据失败，请检查网络连接");
        });
    },
    // 递归提取任务名称
    extractTaskNames(treeData) {
      const taskNames = [];

      // 递归函数，用于遍历树结构
      const extractNames = (nodes) => {
        if (!nodes || !nodes.length) return;

        nodes.forEach((node) => {
          if (node.children === null) {
            // 找到叶子节点，这是任务名称
            taskNames.push(node.name);
          } else if (node.children && node.children.length) {
            // 继续递归遍历子节点
            extractNames(node.children);
          }
        });
      };

      // 开始递归
      extractNames(treeData);

      // 返回去重后的任务名称数组
      return [...new Set(taskNames)];
    },
    // 获取报送分类B选项
    selectB() {
      selectClassB()
        .then((res) => {
          this.options = res.data;
        })
        .catch((err) => {
          console.error("获取报送分类B数据失败:", err);
          this.$message.error("获取报送分类B数据失败，请检查网络连接");
        });
    },
    // 导出Excel
    exportExcel() {
      try {
        // 创建工作簿
        const wb = XLSX.utils.book_new();

        // 将表格数据转为工作表
        const ws = XLSX.utils.json_to_sheet(this.flattenedData);

        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, "计划完成情况");

        // 生成并导出Excel文件
        const wbout = XLSX.write(wb, {
          bookType: "xlsx",
          bookSST: true,
          type: "array",
        });

        FileSaver.saveAs(
          new Blob([wbout], { type: "application/octet-stream" }),
          `计划完成情况.xlsx`
        );
      } catch (e) {
        console.error(e);
        this.$message.error("导出失败");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
// 状态样式
.status-running {
  color: #e6a23c;
}

.status-completed {
  color: #67c23a;
}

.status-pending {
  color: #909399;
}
</style>
