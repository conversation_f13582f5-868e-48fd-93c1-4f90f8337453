<template>
  <div class="wrapper">
    <simple-list-page
      @query="handleQuery"
      @reset="handleReset"
      :showSearch="true"
    >
      <div slot="top-options">
        <el-form :inline="true" :model="form" size="small">
          <el-form-item label="任务来源：" prop="taskSource">
            <el-select v-model="form.taskSource" filterable clearable>
              <el-option
                v-for="item in taskSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="bottom-options">
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="exportExcel"
          >导出</el-button
        >
      </div>
      <el-table
        :data="list"
        border
        show-overflow-tooltip="true"
        size="mini"
        ref="handSelectTest"
        max-height="700px"
        :summary-method="getSummaries"
        show-summary
      >
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column
          label="任务来源"
          min-width="120"
          prop="taskSource"
          show-tooltip-when-overflow
        >
        </el-table-column>
        <el-table-column
          label="报送分类A"
          min-width="200"
          prop="newscbname"
          show-tooltip-when-overflow
        >
        </el-table-column>

        <el-table-column
          label="计划任务数"
          min-width="100"
          prop="total"
          show-tooltip-when-overflow
        >
        </el-table-column>

        <el-table-column
          label="剩余任务数"
          min-width="100"
          prop="remainingTasks"
          show-tooltip-when-overflow
        >
        </el-table-column>

        <el-table-column
          label="抽样完成数"
          min-width="100"
          prop="totalBatch"
          show-tooltip-when-overflow
        >
        </el-table-column>

        <el-table-column
          label="抽样完成率"
          min-width="100"
          prop="samplingCompletionRate"
          show-tooltip-when-overflow
        >
        </el-table-column>

        <el-table-column
          label="未接收数"
          prop="receivingSample"
          min-width="100"
          show-tooltip-when-overflow
        >
        </el-table-column>

        <el-table-column
          label="已接收数"
          prop="receivedQuantity"
          min-width="100"
          show-tooltip-when-overflow
        >
        </el-table-column>

        <el-table-column
          label="计划抽样完成日期"
          prop="planFinishDate"
          min-width="100"
          show-tooltip-when-overflow
        >
        </el-table-column>

        <el-table-column
          label="完全提交数"
          prop="finishSampleTotal"
          min-width="100"
          show-tooltip-when-overflow
        >
        </el-table-column>

        <el-table-column
          label="不合格（问题）数"
          prop="finishUnqualifiedBatch"
          min-width="120"
          show-tooltip-when-overflow
        >
        </el-table-column>

        <el-table-column
          label="问题发现率"
          prop="problemDetectionRate"
          min-width="100"
          show-tooltip-when-overflow
        >
        </el-table-column>
      </el-table>
    </simple-list-page>
  </div>
</template>

<script>
import { selectHnyList } from "@/api/modules/sampleProgress.js";
import { getClassTree } from "@/api/modules/task.js";
import FileSaver from "file-saver";
import XLSX from "xlsx";
export default {
  data() {
    return {
      form: {
        taskSource: "",
      },
      list: [],
      taskSourceOptions: [],
    };
  },
  mounted() {
    this.loadData();
    this.fetchTaskSourceOptions();
  },
  methods: {
    fetchTaskSourceOptions() {
      getClassTree()
        .then((res) => {
          if (res.code === 200 && res.data) {
            this.taskSourceOptions = res.data.map((item) => ({
              value: item.name,
              label: item.name,
            }));
          } else {
            this.$message.error(res.msg || "获取任务来源数据失败");
          }
        })
        .catch((err) => {
          console.error("获取任务来源数据失败:", err);
          this.$message.error("获取任务来源数据失败，请检查网络连接");
        });
    },
    handleQuery() {
      this.loadData();
    },
    handleReset() {
      this.form = {
        taskSource: "",
      };
      this.handleQuery();
    },
    loadData() {
      let data = {
        ...this.form,
      };
      selectHnyList(data).then((res) => {
        this.list = res.data;
      });
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "总计";
          return;
        }
        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = sums[index] == 0 ? "/" : (sums[index] += " 批");
        } else {
          sums[index] = "/";
        }
      });

      return sums;
    },
    exportExcel: function () {
      try {
        if (!this.list || this.list.length === 0) {
          this.$message.warning("暂无数据可导出");
          return;
        }

        const $e = this.$refs["handSelectTest"].$el;
        let $table = $e.querySelector(".el-table__fixed");
        if (!$table) {
          $table = $e;
        }
        const wb = XLSX.utils.table_to_book($table, { raw: true });
        for (const key in wb) {
          if (key.indexOf("!") === -1 && wb[key].v) {
            wb[key].s = {
              font: {
                sz: 13,
                bold: false,
                color: {
                  rgb: "000000",
                },
              },
              alignment: {
                horizontal: "center",
                vertical: "center",
                wrap_text: true,
              },
              border: {
                top: { style: "thin" },
                bottom: { style: "thin" },
                left: { style: "thin" },
                right: { style: "thin" },
              },
            };
          }
        }
        const wbout = XLSX.write(wb, {
          bookType: "xlsx",
          bookSST: true,
          type: "array",
        });
        FileSaver.saveAs(
          new Blob([wbout], { type: "application/octet-stream" }),
          `总进度查询.xlsx`
        );
      } catch (e) {
        if (typeof console !== "undefined") console.error(e);
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
