<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="500px"
    :close-on-click-modal="false"
    @close="closeDialog"
    :destroy-on-close="true"
  >
    <el-form
      :model="orgForm"
      :rules="rules"
      ref="orgFormRef"
      label-width="100px"
      autocomplete="off"
    >
      <el-form-item label="机构名称" prop="name">
        <el-input
          v-model="orgForm.name"
          placeholder="请输入机构名称"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="机构代码" prop="licenseNo">
        <el-input
          v-model="orgForm.licenseNo"
          placeholder="请输入机构代码"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="所在省份" prop="provinceName">
        <el-input
          v-model="orgForm.provinceName"
          placeholder="请输入所在省份"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="所在城市" prop="cityName">
        <el-input
          v-model="orgForm.cityName"
          placeholder="请输入所在城市"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="所在区/县" prop="areaName">
        <el-input
          v-model="orgForm.areaName"
          placeholder="请输入所在区/县"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input
          v-model="orgForm.address"
          type="textarea"
          placeholder="请输入详细地址"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <!-- address 地址   contactPostcode  邮编 contactEmail  邮箱  contactTel 电话 fax 传真 -->
      <el-form-item label="邮政编码" prop="contactPostcode">
        <el-input
          v-model="orgForm.contactPostcode"
          placeholder="请输入邮政编码"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系人" prop="contactName">
        <el-input
          v-model="orgForm.contactName"
          placeholder="请输入联系人"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="电话" prop="contactTel">
        <el-input
          v-model="orgForm.contactTel"
          placeholder="请输入电话"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="传真" prop="fax">
        <el-input
          v-model="orgForm.fax"
          placeholder="请输入传真"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="电子邮箱" prop="contactEmail">
        <el-input
          v-model="orgForm.contactEmail"
          placeholder="请输入电子邮箱"
          autocomplete="off"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="submitLoading"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { updateOrganization } from "@/api/modules/organization";

export default {
  name: "EmployeeEdit",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dialogTitle: {
      type: String,
      default: "添加机构",
    },
    orgFormData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      orgForm: {},
      orgOptions: [],
      rules: {
        name: [{ required: true, message: "请输入机构账号", trigger: "blur" }],
        licenseNo: [
          { required: true, message: "请输入机构代码", trigger: "blur" },
        ],
        provinceName: [
          { required: true, message: "请输入所在省份", trigger: "blur" },
        ],
        cityName: [
          { required: true, message: "请输入所在城市", trigger: "blur" },
        ],
        areaName: [
          { required: true, message: "请输入所在区/县", trigger: "blur" },
        ],
        contactPostcode: [
          { required: false, message: "请输入邮政编码", trigger: "blur" },
        ],
        contactName: [
          { required: false, message: "请输入联系人", trigger: "blur" },
        ],
        contactTel: [
          { required: false, message: "请输入电话号码", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        fax: [{ required: false, message: "请输入传真", trigger: "blur" }],
        contactEmail: [
          { required: false, message: "请输入电子邮箱", trigger: "blur" },
          {
            type: "email",
            message: "请输入正确的邮箱格式",
            trigger: "blur",
          },
        ],
      },
      roleOptions: [],
      roleOptionsSelect: [],
      passwordVisible: false,
      submitLoading: false,
    };
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.orgForm = this.orgFormData;
        }
      },
    },
  },
  methods: {
    closeDialog() {
      this.$parent.dialogVisible = false;
    },
    submitForm() {
      this.$refs.orgFormRef.validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          // 只保留需要的字段
          const formData = {
            id: this.orgForm.id,
            name: this.orgForm.name,
            licenseNo: this.orgForm.licenseNo,
            provinceName: this.orgForm.provinceName,
            cityName: this.orgForm.cityName,
            areaName: this.orgForm.areaName,
            address: this.orgForm.address,
            contactPostcode: this.orgForm.contactPostcode,
            contactName: this.orgForm.contactName,
            contactTel: this.orgForm.contactTel,
            fax: this.orgForm.fax,
            contactEmail: this.orgForm.contactEmail,
          };

          updateOrganization(formData)
            .then((res) => {
              if (res.code === 200) {
                this.$message.success(
                  `机构${formData.id ? "修改" : "添加"}成功!`
                );
                this.dialogVisible = false;
                this.$parent.loadData();
                this.$parent.dialogVisible = false;
              } else {
                this.$message.error(res.msg || "机构信息更新失败");
              }
            })
            .catch((err) => {
              console.error("更新机构信息失败:", err);
              this.$message.error("更新机构信息失败，请检查网络连接");
            })
            .finally(() => {
              this.submitLoading = false;
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
