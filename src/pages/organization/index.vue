<template>
  <div class="organization-page">
    <!-- 筛选 -->
    <div class="filter-options">
      <el-form ref="searchForm" :inline="true" :model="form" size="small">
        <el-form-item label="机构名称：">
          <el-input
            v-model="form.name"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadData()">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="operation-btns">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAdd"
          >添加机构</el-button
        >
      </div>
    </div>
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="list"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        height="calc(100vh - 360px)"
      >
        <el-table-column
          prop="name"
          label="机构名称"
          align="center"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="licenseNo"
          label="机构代码"
          align="center"
          min-width="120"
        ></el-table-column>
        <!-- <el-table-column
          prop="contactName"
          label="联系人"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="contactTel"
          label="联系电话"
          align="center"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="contactEmail"
          label="联系邮箱"
          align="center"
          min-width="150"
        ></el-table-column> -->
        <el-table-column
          prop="provinceName"
          label="所在省份"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="cityName"
          label="所在城市"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="areaName"
          label="所在区/县"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="address"
          label="详细地址"
          align="center"
          min-width="200"
        ></el-table-column>
        <!-- <el-table-column label="机构类型" align="center" min-width="100">
          <template slot-scope="scope">
            {{
              scope.row.type === 1
                ? "政府机构"
                : scope.row.type === 2
                ? "企业机构"
                : scope.row.type === 3
                ? "教育机构"
                : scope.row.type === 4
                ? "其他"
                : "未知"
            }}
          </template>
        </el-table-column>
        <el-table-column label="机构级别" align="center" min-width="100">
          <template slot-scope="scope">
            {{
              scope.row.level === 1
                ? "国家级"
                : scope.row.level === 2
                ? "省级"
                : scope.row.level === 3
                ? "市级"
                : scope.row.level === 4
                ? "区县级"
                : scope.row.level === 5
                ? "其他"
                : "未知"
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          align="center"
          min-width="150"
        ></el-table-column> -->
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="text"
              icon="el-icon-delete"
              class="delete-btn"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
        ></el-pagination>
      </div>
    </div>

    <!-- 添加/编辑机构弹窗 -->
    <m-edit
      :dialogTitle="dialogTitle"
      :visible.sync="dialogVisible"
      :orgFormData="orgForm"
    ></m-edit>
  </div>
</template>

<script>
import {
  getOrganizationList,
  deleteOrganization,
} from "@/api/modules/organization";
import Edit from "./components/edit.vue";

export default {
  name: "OrganizationPage",
  components: {
    "m-edit": Edit,
  },
  data() {
    return {
      form: {
        name: "",
        contactName: "",
        contactTel: "",
      },
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10,
      },
      loading: false,
      dialogVisible: false,
      dialogTitle: "添加机构",
      orgForm: {},
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 加载数据
    loadData() {
      this.loading = true;
      const params = {
        ...this.form,
        current: this.page.current,
        size: this.page.size,
      };

      getOrganizationList(params)
        .then((res) => {
          if (res.code === 200) {
            // 处理返回数据中的null值
            this.list = (res.data.records || []).map((item) => {
              const newItem = { ...item };
              Object.keys(newItem).forEach((key) => {
                if (newItem[key] === null || newItem[key] === undefined) {
                  newItem[key] = "—"; // 使用破折号表示空值
                }
              });
              return newItem;
            });
            this.page.total = Number(res.data.total || 0);
          } else {
            this.$message.error(res.msg || "获取机构列表失败");
          }
        })
        .catch((err) => {
          console.error("获取机构列表失败:", err);
          this.$message.error("获取机构列表失败，请检查网络连接");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 查询按钮
    handleQuery() {
      this.page.current = 1;
      this.loadData();
    },
    // 重置按钮
    handleReset() {
      this.form = {
        name: "",
        contactName: "",
        contactTel: "",
      };
      this.page.current = 1;
      this.loadData();
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.page.size = val;
      this.loadData();
    },
    // 页码变化
    handleCurrentChange(val) {
      this.page.current = val;
      this.loadData();
    },
    handleAdd() {
      this.dialogTitle = "添加机构";
      this.dialogVisible = true;
      this.orgForm = {
        id: null,
        name: "",
        licenseNo: "",
        provinceName: "",
        cityName: "",
        areaName: "",
        address: "",
      };
    },
    handleEdit(row) {
      this.dialogTitle = "编辑机构";
      this.orgForm = JSON.parse(JSON.stringify(row));
      this.dialogVisible = true;
    },
    handleDelete(row) {
      this.$confirm("确定删除当前机构？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteOrganization(row.id)
          .then((res) => {
            if (res.code === 200) {
              this.$message.success("机构删除成功");
              this.deleteDialogVisible = false;
              this.loadData();
            } else {
              this.$message.error(res.msg || "机构删除失败");
            }
          })
          .catch((err) => {
            console.error("删除机构失败:", err);
            this.$message.error("删除机构失败，请检查网络连接");
          })
          .finally(() => {});
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.organization-page {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  height: calc(100vh - 110px);
  display: flex;
  flex-direction: column;

  .table-container {
    width: 100%;
    flex: 1;
    overflow: hidden;
    margin-top: 20px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
.delete-btn {
  color: #f56c6c;
  margin-left: 10px;
}
</style>
