<template>
  <simple-page title="编辑常见问题" @back="handleBack">
      <el-form :model="ruleForm"  :rules="rules" ref="ruleForm" label-width="130px" class="demo-ruleForm">
        <el-form-item label="常见问题名称：" prop="problemName">
          <el-input v-model="ruleForm.problemName"></el-input>
        </el-form-item>
        <el-row :gutter="20">
       <el-form-item label="常见问题类型：">
             <el-col :span="6">
              <el-select v-model="ruleForm.typeId" placeholder="请选择常见问题类型">
                <el-option
                  v-for="item in typeList"
                  :key="item.id"
                  :label="item.problemTypeName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-col>
          <el-col :span="4" >
            <el-button  type='text' size='mini' @click='addTypeList'>新增常见问题类型</el-button> 
          </el-col>
       </el-form-item>
          </el-row>
        <el-form-item label="解答：" prop="answer">  
          
           <simple-editor class="editor-content" v-model="ruleForm.answer" 
           :showImage="true" :showVideo="false"></simple-editor>
      
      </el-form-item>


        <el-form-item style="margin-top: 100px">  
          <el-col :offset="16">
              <span  class="dialog-footer">
              <el-button @click="handleBack">取 消</el-button>
              <el-button type="primary" @click="submit" v-loading="loading">确 定</el-button>
            </span>
          </el-col>
       
           
      </el-form-item>

      

      </el-form>
      

    <el-dialog
      title="新增常见问题类型"
      :visible.sync="dialogTypeVisible"
      width="70%"
      :before-close="handleClose">
      <el-form :model="ruleTypeForm"  :rules="typeRules" ref="ruleTypeForm" label-width="130px" class="demo-ruleForm">
        <el-form-item label="常见问题类型：" prop="problemTypeName">
          <el-input v-model="ruleTypeForm.problemTypeName"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogTypeVisible = false">取 消</el-button>
        <el-button type="primary" @click="typeSubmit" v-loading="typeLoading">确 定</el-button>
      </span>
    </el-dialog>
  </simple-page>
</template>

<script>
import {pageList ,add ,detail,commonProblemDelete,update,commonProblemTypeList,adjustType } from '@/api/modules/common-problem.js'
export default {
  data() {
    return {
      loading: false,
      typeLoading: false,
      dialogTypeVisible: false,
      typeList: [],
      ruleForm: {
        id: '',
        problemName: '',
        typeId: '',
        answer: ''
      },
      ruleTypeForm: {
        problemTypeName: ''
      },
      rules: {
        problemName: [
          { required: true, message: '请输入常见问题名称', trigger: 'blur' },
          { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
        ],
        typeId: [
          { required: true, message: '请选择常见问题类型', trigger: 'change' }
        ],
        answer: [
          { required: true, message: '请输入解答', trigger: 'blur' }
        ],
      },
      typeRules: {
        problemTypeName: [
          { required: true, message: '请输入常见问题类型名称', trigger: 'blur' },
          { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.ruleForm.id = this.$route.query.id
    this.commonProblemTypeList()
    this.details()
  },
  methods: {
    details() {
      // 获取详情数据
      detail({id: this.ruleForm.id}).then(res => {
          if (+res.code === 200) {
            this.ruleForm = res.data
          } else {
            this.$message.error(res.msg)
          }
      }).catch(() => {
          this.loading = false
        })


    },
    typeSubmit() {
      this.$refs.ruleTypeForm.validate(valid => {
        if (valid) {
          this.typeLoading = true
          adjustType(this.ruleTypeForm).then(res => {
            this.typeLoading = false
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.dialogTypeVisible = false
                this.commonProblemTypeList()
              } else {
                this.$message.error(res.msg)
              }
              this.handleQuery()
          }).catch(() => {
              this.typeLoading = false
          })
        } else {
          // 登录表单校验失败
          this.$message.error('表单校验失败，请检查')
        }
      })
    },
    handleClose(done) {
      done()
    },
    // 提交
    submit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          let request;
          if (this.ruleForm.id) {
            request = update(this.ruleForm)
          } else {
            request = add(this.ruleForm)
          }
          request.then(res => {
            this.loading = false
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.$router.push({
                  path: '/learnPublish/commonProblem'
                })
              } else {
                this.$message.error(res.msg)
              }
              this.handleQuery()
          }).catch(() => {
              this.loading = false
          })
        } else {
          // 登录表单校验失败
          this.$message.error('表单校验失败，请检查')
        }
      })
    },
    addTypeList() {
      this.dialogTypeVisible = true
      this.ruleTypeForm = {
        problemTypeName: ''
      }
    },
    // 常见问题类型列表查询
    commonProblemTypeList(){
      commonProblemTypeList().then(res => {
        this.typeList = res.data
      })
    },
    handleBack() {
       this.$router.push({
                  path: '/learnPublish/commonProblem'
                })
    },
  },
}
</script>
