<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='细则名称：'>
          <el-input v-model='form.regulationName'></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot='bottom-options'>
      <el-button type='primary' size='mini' icon='el-icon-plus' @click='addView'>新增</el-button>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' icon='el-icon-edit' @click='editView(scope.row)'>编辑</el-button>
        <el-button type='text' size='mini' icon='el-icon-delete' @click='delte(scope.row)'>删除</el-button>
      </div>
    </simple-table>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="70%"
      :before-close="handleClose">
      <el-form :model="ruleForm"  :rules="rules" ref="ruleForm" label-width="130px" class="demo-ruleForm">
        <el-form-item label="抽检细则名称：" prop="regulationName">
          <el-input v-model="ruleForm.regulationName"></el-input>
        </el-form-item>
       <el-form-item label="抽检实施细则：">
          <el-upload
          class="upload-demo"
          drag
          :action="uploadUrl"
          :headers="headers"
          :file-list="fileList"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
          accept='.pdf'>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传.pdf文件</div>
        </el-upload>
       </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit" v-loading="loading">确 定</el-button>
      </span>
    </el-dialog>
  </simple-list-page>
</template>

<script>
import {pageList ,add ,detail,samplingRegulationDelete,update } from '@/api/modules/sampling-regulation.js'

import { getToken } from '@/utils/auth';
export default {
  data() {
    return {
      title: "新增抽样规则",
      loading: false,
      dialogVisible: false,
      form: {},
      uploadUrl: process.env.VUE_APP_BASE_API + '/oss/endpoint/put-public-file',
      headers: {
        Authorization: getToken()
      },
      fileList: [],
      ruleForm: {
        regulationName: '',
        originalFileName: '',
        originalFileUrl: ''
      },
      rules: {
        regulationName: [
          { required: true, message: '请输入抽检细则名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      },
      columns: [
        {
          label: '抽检实施细则名称',
          prop: 'regulationName'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      let data={
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      console.log(data)
      pageList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleQuery() {
      this.page={
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    // 提交
    submit() {

      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          let request;
          if (this.ruleForm.id) {
            request = update(this.ruleForm)
          } else {
            request = add(this.ruleForm)
          }
          request.then(res => {
            this.loading = false
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.dialogVisible = false
              } else {
                this.$message.error(res.msg)
              }
              this.handleQuery()
          }).catch(() => {
              this.loading = false
          })
        } else {
          // 登录表单校验失败
          this.$message.error('表单校验失败，请检查')
        }
      })


    },
    // 删除
    delte(row) {
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          samplingRegulationDelete({id: row.id}).then(res => {
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.handleQuery()
              } else {
                this.$message.error(res.msg)
              }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    addView() {
      this.title = "新增抽样规则"
      this.dialogVisible = true
      // this.$nextTick(() => {
      //   this.$refs.ruleForm.resetFields();
      // });
      this.ruleForm = {
        regulationName: '',
        originalFileName: '',
        originalFileUrl: ''
      }
    },
    // 编辑
    editView(row) {
      this.title = "编辑抽样规则"
      // 获取详情数据
      detail({id: row.id}).then(res => {
        this.loading = false
          if (+res.code === 200) {
            this.dialogVisible = true
            this.ruleForm = res.data
            console.log(this.ruleForm)
            this.fileList = []
            // 组装显示文件
            if (this.ruleForm.originalFileUrl) {
              this.fileList.push({
                name: this.ruleForm.originalFileName,
                url: this.ruleForm.originalFileUrl
              })
            }
          } else {
            this.$message.error(res.msg)
          }
      }).catch(() => {
          this.loading = false
        })
    },
    handleClose(done) {
      done();
    },
    handleAvatarSuccess (res, file) {
      this.fileList = []
      this.fileList.push({
        name: res.data.originalName,
        url: res.data.link
      })
      this.ruleForm.originalFileName = res.data.originalName
      this.ruleForm.originalFileUrl = res.data.link
    },
    beforeAvatarUpload (file) {
      const isJPG = file.type==="application/pdf"
      // const isLt2M = file.size / 1024 / 1024 < 2
      if (!isJPG) {
        this.$message.error('上传文件只能是 pdf 格式!')
      }
      this.ruleForm.originalFileUrl = ''
      this.ruleForm.originalFileName = ''
      // if (!isLt2M) {
      //   this.$message.error('上传头像图片大小不能超过 2MB!')
      // }
      return isJPG
    },
  }
}
</script>

<style lang="scss" scoped></style>
