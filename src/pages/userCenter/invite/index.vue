<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='分享/邀请用户昵称'>
          <el-input v-model='form.allocationUserNickName'></el-input>
        </el-form-item>
        <el-form-item label='分享/邀请真实姓名'>
          <el-input v-model='form.allocationRealName'></el-input>
        </el-form-item>
        <el-form-item label='分享/邀请联系方式'>
          <el-input v-model='form.allocationUserTelephone'></el-input>
        </el-form-item>
        <el-form-item label='受邀用户昵称'>
          <el-input v-model='form.invitationUserNickName'></el-input>
        </el-form-item>
        <el-form-item label='受邀用户真实姓名'>
          <el-input v-model='form.invitationRealName'></el-input>
        </el-form-item>
        <el-form-item label='受邀用户联系方式'>
          <el-input v-model='form.invitationUserTelephone'></el-input>
        </el-form-item>
      </el-form>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData' :showOptions='false'>
    </simple-table>
  </simple-list-page>
</template>

<script>
import {selectShareManagePage} from '@/api/modules/invite.js'

export default {
  data() {
    return {
      form: {},
      columns: [
        {
          label: '分享/邀请用户昵称',
          prop: 'allocationUserNickName'
        },
        {
          label: '分享/邀请真实姓名',
          prop: 'allocationRealName'
        },
        {
          label: '分享/邀请联系方式',
          prop: 'allocationUserTelephone'
        },
        {
          label: '受邀用户昵称',
          prop: 'invitationUserNickName'
        },
        {
          label: '受邀用户真实姓名',
          prop: 'invitationRealName'
        },
        {
          label: '受邀用户联系方式',
          prop: 'invitationUserTelephone'
        },
        {
          label: '邀请方式',
          prop: 'mode',
          formatter:(row, column, value, index)=>{
            return value===1?'邀请码':'分享'
          }
        },
        {
          label: '邀请时间',
          prop: 'invitationTime'
        },
        {
          label: '注册时间',
          prop: 'registerTime'
        },
        {
          label: '状态',
          prop: 'registerResultDesc'
        }

      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      let data={
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      selectShareManagePage(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleQuery() {
      this.page={
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
    },
    detailView(row) {
      this.$router.push({
        path: '/userCenter/userInfo/detail',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
