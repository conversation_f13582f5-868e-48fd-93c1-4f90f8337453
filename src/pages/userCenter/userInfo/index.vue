<template>
  <div>
    <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot='search'>
        <el-form :inline='true' :model='form'>
          <el-form-item label='用户昵称'>
            <el-input v-model='form.nickName'></el-input>
          </el-form-item>
          <el-form-item label='真实姓名'>
            <el-input v-model='form.realName'></el-input>
          </el-form-item>
          <el-form-item label='联系电话'>
            <el-input v-model='form.telephone'></el-input>
          </el-form-item>
          <el-form-item label="注册时间" prop="deadline">
            <el-date-picker v-model='times' format='yyyy-MM-dd' value-format='yyyy-MM-dd' type='daterange' align='right' unlink-panels range-separator='至' start-placeholder='开始日期' end-placeholder='结束日期'
              @change="timesChange">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div slot='bottom-options'>
        <el-button type='primary' size='mini' icon='el-icon-plus' @click='changeClick()'>批量调整会员</el-button>
        <el-button type='primary' size='mini' icon='el-icon-plus' @click='notification()'>注册</el-button>
      </div>
      <simple-table :columns='columns' :showSelection="true" :data='list' :page='page' @load='loadData' :optionsWidth="240" @selected='selectedId'>
        <div slot='options' slot-scope='scope'>
          <el-button type='text' size='mini' icon='el-icon-view' @click='detailView(scope.row)'>查看详情</el-button>
          <el-button type='text' size='mini' icon='el-icon-edit' @click='changeClick(scope.row)'>会员调整</el-button>
          <el-button type='text' size='mini' icon='el-icon-edit' @click='modify(scope.row)'>修改</el-button>
        </div>
      </simple-table>
    </simple-list-page>

    <el-dialog :visible.sync='showDialog' v-loading="loading" width='50%' title='回复'>
      <el-form :rules="rules" ref='form' class='form' label-width='100px' label-position='left' size='mini' :model="changeInfo">
        <div style="padding:10px;">
          <el-form-item label='用户昵称：' v-show="!isBatch">
            <div>{{ changeInfo.nickName }}</div>
          </el-form-item>
          <el-form-item label='真实姓名：' v-show="!isBatch">
            <div>{{ changeInfo.realName }}</div>
          </el-form-item>
          <el-form-item label='联系方式：' v-show="!isBatch">
            <div>{{ changeInfo.telephone }}</div>
          </el-form-item>
          <el-form-item label='调整类型：' prop="payType">
            <el-select v-model='changeInfo.payType' placeholder='请选择'>
              <el-option v-for='item in typeOption' :key='item.value' :label='item.label' :value='item.value'>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label='会员类型：' prop="memberType">
            <el-select v-model='changeInfo.memberType' placeholder='请选择'>
              <el-option v-for='item in memberOption' :key='item.value' :label='item.label' :value='item.value'>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label='赠送时间：' prop="days">
            <el-input-number v-model='changeInfo.days' size='mini' :min='1' :max='99999999' style='margin-right: 20px' />天
          </el-form-item>
        </div>
      </el-form>
      <div slot='footer' class='footer'>
        <el-button size='small' @click='showDialog = false'>取 消</el-button>
        <el-button class='ml20' type='primary' size='small' @click='handleSubmit'>提 交</el-button>
      </div>
    </el-dialog>

    <el-dialog title="注册" :visible.sync="dialogVisible" width="30%">
      <div>
        <el-form :model="ruleForm" :rules="ruless" ref="ruleForm" label-width="120px" class="login-form">
          <el-form-item label="登录名" prop="username">
            <el-input v-model="ruleForm.username" style="width: 260px;"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input v-model="ruleForm.password" show-password style="width: 260px;"></el-input>
          </el-form-item>
          <el-form-item label=" 普通用户性别" prop="sex">
            <el-select v-model="ruleForm.sex" placeholder="请选择活动区域" style="width: 260px;">
              <el-option label="男" value='1'></el-option>
              <el-option label="女" value='2'></el-option>
              <el-option label="保密" value='0'></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联系电话" prop="telephone">
            <el-input v-model="ruleForm.telephone" style="width: 260px;"></el-input>
          </el-form-item>
          <el-form-item style="width: 100%">
            <el-button :loading="loading" size="medium" type="primary" class="login-btn" @click.native.prevent="register('ruleForm')">
              <span>注 册</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
    </span> -->
    </el-dialog>
    <el-dialog title="修改" :visible.sync="dialogVisibleEdit" width="30%">
      <div>
        <el-form :model="ruleFormEdit" :rules="rulessEdit" ref="ruleFormEdit" label-width="120px" class="login-form">
          <el-form-item label="用户昵称" prop="username">
            <el-input v-model="ruleFormEdit.username" style="width: 260px;"></el-input>
          </el-form-item>
          <el-form-item label=" 普通用户性别" prop="sex">
            <el-select v-model="ruleFormEdit.sex" placeholder="请选择活动区域" style="width: 260px;">
              <el-option label="男" value='1'></el-option>
              <el-option label="女" value='2'></el-option>
              <el-option label="保密" value='0'></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联系方式" prop="telephone">
            <el-input v-model="ruleFormEdit.telephone" style="width: 260px;"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="enable">
            <el-switch v-model="ruleFormEdit.enable" active-color="#13ce66" inactive-color="#ff4949">
            </el-switch>
          </el-form-item>
          <el-form-item style="width: 100%">
            <el-button :loading="loading" size="medium" type="primary" class="login-btn" @click.native.prevent="submitTo('ruleFormEdit')">
              <span>提 交</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pageVideoOrder } from '@/api/modules/login'
import { adjustVIP, selectUserById, updateUserApp, userCenterListPage } from '@/api/modules/sysUser.js'

export default {
  data() {
    return {
      times: [],
      showDialog: false,
      loading: false,
      changeInfo: {},
      isBatch: true,
      dialogVisible: false,
      dialogVisibleEdit: false,
      ruleFormEdit: {
        enable: true,
        username: '',
        sex: '0',
        telephone: null,
      },
      ruleForm: {
        username: '',
        password: '',
        sex: '0',
        telephone: null,
      },
      rulessEdit: {
        username: [
          { required: true, message: '请输入用户昵称', trigger: 'blur' },
        ],
        sex: [
          { required: true, message: '请选择性别', trigger: 'change' }
        ],
        telephone: [
          { required: true, message: "请输入联系电话", trigger: "blur" },
          // 这个只能验证手机号
          // { pattern:/^0{0,1}(13[0-9]|15[7-9]|153|156|18[7-9])[0-9]{8}$/, message: "请输入合法手机号", trigger: "blur" }
          { pattern: /^1[3-9]\d{9}$/, message: "请输入合法手机号/电话号", trigger: "blur" }
        ],
      },
      ruless: {
        username: [
          { required: true, message: '请输入登录名', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入登录名', trigger: 'blur' },
        ],
        sex: [
          { required: true, message: '请选择性别', trigger: 'change' }
        ],
        telephone: [
          { required: true, message: "请输入联系电话", trigger: "blur" },
          // 这个只能验证手机号
          // { pattern:/^0{0,1}(13[0-9]|15[7-9]|153|156|18[7-9])[0-9]{8}$/, message: "请输入合法手机号", trigger: "blur" }
          { pattern: /^1[3-9]\d{9}$/, message: "请输入合法手机号/电话号", trigger: "blur" }
        ],
      },
      rules: {
        payType: [
          { required: true, message: '请选择调整类型', trigger: 'blur' }
        ],
        memberType: [
          { required: true, message: '请选择会员类型', trigger: 'blur' }
        ],
        days: [
          { required: true, message: '赠送时间不能为空', trigger: 'blur' }
        ]
      },
      form: {
        startCreateTime: null,
        endCreateTime: null
      },
      typeOption: [
        { label: '赠送', value: '2' },
        { label: '扣除', value: '3' },
      ],
      memberOption: [
        { label: 'VIP会员', value: 1 },
        { label: 'SVIP会员', value: 2 }
      ],
      columns: [
        {
          label: '用户昵称',
          prop: 'nickName'
        },
        {
          label: '真实姓名',
          prop: 'realName'
        },
        {
          label: '联系方式',
          prop: 'telephone'
        },
        {
          label: '机构名称',
          prop: 'orgName'
        },
        // {
        //   label: '会员等级',
        //   prop: 'memberType',
        //   formatter: (row, column, value, index) => {
        //     return value === 0 ? '普通会员' : value === 1 ? 'vip' : 'svip'
        //   }
        // },
        // {
        //   label: '会员到期时间',
        //   prop: 'memberTime'
        // },
        // {
        //   label: '是否实名认证',
        //   prop: 'examineState',
        //   formatter: (row, column, value, index) => {
        //     return value === 1 ? '是' : '否'
        //   }
        // },
        // {
        //   label: '注册渠道',
        //   prop: 'channelName'
        // },
        {
          label: '注册时间',
          prop: 'createTime'
        },
        {
          label: '状态',
          prop: 'enable',
          formatter: (row) => {
            return row.enable === 0 ? '禁用' : '启用'
          }
        }

      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      menuSelection: [],
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    register(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          pageVideoOrder(this.ruleForm).then(res => {

            this.$message({
              showClose: true,
              message: '注册成功',
              type: 'success'
            });
            this.dialogVisible = false
            this.ruleForm.username = '',
              this.ruleForm.password = '',
              this.ruleForm.sex = '0',
              this.ruleForm.telephone = null
          }).catch((err) => {
            console.log(err.msg)
            this.$message.error(err.msg);
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    notification() {
      this.dialogVisible = true
    },
    loadData() {
      this.loading = true
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      userCenterListPage(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
        this.loading = false
      })
    },
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
    },
    detailView(row) {
      this.$router.push({
        path: '/userCenter/userInfo/detail',
        query: {
          id: row.id
        }
      })
    },
    modify(row) { // 详情
      this.ruleFormEdit.enable = true,
        this.ruleFormEdit.username = '',
        this.ruleFormEdit.sex = '0',
        this.ruleFormEdit.telephone = null,
        selectUserById(row.id).then(res => {
          this.ruleFormEdit = res.data
          this.ruleFormEdit.enable = this.ruleFormEdit.enable == 1 ? true : false
          this.ruleFormEdit.sex = this.ruleFormEdit.sex + ""
          this.dialogVisibleEdit = true
        })
    },
    timesChange() {

      if (this.times.length > 0) {
        this.form.startCreateTime = this.times[0]
        this.form.endCreateTime = this.times[1]
      } else {
        this.form.startCreateTime = null
        this.form.endCreateTime = null
      }
    },
    changeClick(row) {
      this.showDialog = true
      if (row) {
        this.isBatch = false
        this.changeInfo = JSON.parse(JSON.stringify(row))
        this.changeInfo.memberType = null
      }
    },
    submitTo(formName) { // 提交
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.ruleFormEdit.enable = this.ruleFormEdit.enable == true ? 1 : 0
          updateUserApp(this.ruleFormEdit).then(res => {
            this.$message({
              showClose: true,
              message: '修改成功',
              type: 'success'
            });
            this.dialogVisibleEdit = false
            this.loadData()
          }).catch((err) => {
            console.log(err.msg)
            this.$message.error(err.msg);
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let param = {
            userId: this.changeInfo.id,
            payType: this.changeInfo.payType,
            memberType: this.changeInfo.memberType,
            days: this.changeInfo.days
          }
          if (this.isBatch) {
            param.userIds = this.menuSelection
          }
          adjustVIP(param).then(res => {
            this.showDialog = false
            this.changeInfo = {}
            this.$message.success('操作成功')
            this.loadData()
          })
        }
      })
    },
    selectedId(val) {
      this.menuSelection = [];
      for (let i = 0; i < val.length; i++) {
        this.menuSelection[i] = val[i].id;
      }
    },
  }
}
</script>

<style lang="scss" scoped></style>
