<template>
  <simple-page title='查看详情' @back='handleBack'>
    <simple-line-card header='基础资料'>
      <simple-detail-panel :columns='userInfoCol' :data='userInfo' :labelWidth='100'></simple-detail-panel>
    </simple-line-card>
    <simple-line-card header='会员资产'>
      <simple-detail-panel :columns='asset' :data='userInfo' :labelWidth='100'></simple-detail-panel>
    </simple-line-card>
    <simple-line-card>
      <el-tabs v-model='activeName' @tab-click='tabsclick'>
        <el-tab-pane label='智能币信息' name='specieChange'>
          <simple-table :columns='specieChangeCol' :data='specieChangeList' :page='specieChangePage' :showOptions='false' @load='specieChangeLoadData'>
          </simple-table>
        </el-tab-pane>
        <el-tab-pane label='智能币兑换记录' name='specieConsume'>
          <simple-table :columns='specieConsumeCol' :data='specieConsumeList' :page='specieConsumePage' @load='specieConsumeLoadData'>
            <div slot='options' slot-scope='scope'>
              <el-button type='text' size='mini' icon='el-icon-view' @click='specieConsumeView(scope.row)'>查看订单详情
              </el-button>
            </div>
          </simple-table>
        </el-tab-pane>
        <el-tab-pane label='订单信息' name='orderInfo'>
          <simple-table :columns='orderInfoCol' :data='orderInfoList' :page='orderInfoPage' @load='orderInfoLoadData' :showOptions='false'>
          </simple-table>
        </el-tab-pane>
        <el-tab-pane label='优惠券' name='coupons'>
          <simple-table :columns='couponsCol' :data='couponsList' :page='couponsPage' @load='couponsLoadData'>
            <div slot='options' slot-scope='scope'>
              <el-button type='text' size='mini' icon='el-icon-view' @click='couponsRemove(scope.row)'>移除</el-button>
            </div>
          </simple-table>
        </el-tab-pane>
        <el-tab-pane label='会员变更记录' name='vipChange'>
          <simple-table :columns='vipCol' :data='vipList' :page='vipPage' @load='vipLoadData' :showOptions='false'>
          </simple-table>
        </el-tab-pane>
      </el-tabs>
    </simple-line-card>
  </simple-page>
</template>

<script>
import { specieChangePageList, specieConsumePageList, userCenterDetail, vipList } from '@/api/modules/sysUser.js'

export default {
  data() {
    return {
      activeName: 'specieChange',
      id: '',
      asset: [
        {
          label: '累计智能币',
          prop: 'smartCoinsTotal',
          formatter: (data) => {
            console.log(data)
            if (data['smartCoinsTotal']) {
              return data['smartCoinsTotal']
            } else {
              return 0
            }
          }

        },
        {
          label: '剩余智能币',
          prop: 'smartCoinsRemaining',
          formatter: (data) => {
            console.log(data)
            if (data['smartCoinsRemaining']) {
              return data['smartCoinsRemaining']
            } else {
              return 0
            }
          }
        },
        {
          label: '会员卡',
          prop: 'memberTime',
          formatter: (data) => {
            console.log(data)
            if (data['memberTime']) {
              return data['memberTime'] + ' 到期'
            } else {
              return '--'
            }

          }
        }
      ],
      userInfoCol: [
        {
          label: '用户昵称：',
          prop: 'nickName'
        },
        {
          label: '真实姓名：',
          prop: 'realName'
        },
        {
          label: '联系方式：',
          prop: 'telephone'
        },
        {
          label: '邮箱账号：',
          prop: 'emailAccount'
        },
        {
          label: '微信昵称：',
          prop: 'wechatName'
        },
        {
          label: '来源方式：',
          prop: 'channelName'
        },
        {
          label: '注册时间：',
          prop: 'createTime'
        },
        {
          label: '最近登录时间：',
          prop: 'lastLoginInTime'
        },
        {
          label: '分享人：',
          prop: 'allocationUserName'
        },
        {
          label: '分享人电话：',
          prop: 'allocationUserTelephone',
        },
        {
          label: '实名认证：',
          prop: 'examineState',
          formatter: (data) => {
            return data['examineState'] === 1 ? '是' : '否'
          }
        }
      ],
      userInfo: {},
      specieChangeCol: [
        {
          label: '任务ID',
          prop: 'taskId'
        },
        {
          label: '任务名称',
          prop: 'taskName'
        },
        {
          label: '变更前智能币',
          prop: 'beforeSmartCoins'
        },
        {
          label: '变更智能币',
          prop: 'exchangeSmartCoins'
        },
        {
          label: '变更后智能币',
          prop: 'afterSmartCoins'
        },
        {
          label: '变更时间',
          prop: 'exchangeTime'
        }
      ],
      specieChangeList: [],
      specieChangePage: {
        total: 0,
        current: 1,
        size: 10
      },
      specieConsumeCol: [
        {
          label: '订单ID',
          prop: 'orderNo'
        },
        {
          label: '商品名称',
          prop: 'goodsName'
        },
        {
          label: '兑换数量',
          prop: 'exCount'
        },
        {
          label: '智能币价格',
          prop: 'smartCoinsPrice'
        },
        {
          label: '实付',
          prop: 'paidIn'
        },
        {
          label: '兑换时间',
          prop: 'exchangeTime'
        }
      ],
      specieConsumeList: [],
      specieConsumePage: {
        total: 0,
        current: 1,
        size: 10
      },
      orderInfoCol: [
        {
          label: '交易单号',
          prop: 'orderNo'
        },
        {
          label: '商品名称',
          prop: 'clientType'
        },
        {
          label: '单价（元）',
          prop: 'createTime'
        },
        {
          label: '优惠券',
          prop: 'createTime'
        },
        {
          label: '支付方式',
          prop: 'createTime'
        },
        {
          label: '应付金额（元）',
          prop: 'createTime'
        },
        {
          label: '实付金额（元）',
          prop: 'createTime'
        },
        {
          label: '交易时间',
          prop: 'createTime'
        },
        {
          label: '交易状态',
          prop: 'createTime'
        }
      ],
      orderInfoList: [],
      orderInfoPage: {
        total: 0,
        current: 1,
        size: 10
      },
      couponsCol: [
        {
          label: '优惠券名称',
          prop: 'orderNo'
        },
        {
          label: '面值（元）',
          prop: 'clientType'
        },
        {
          label: '获取方式',
          prop: 'createTime'
        },
        {
          label: '使用状态',
          prop: 'createTime'
        },
        {
          label: '获取时间',
          prop: 'createTime'
        },
        {
          label: '使用时间',
          prop: 'createTime'
        }
      ],
      couponsList: [],
      couponsPage: {
        total: 0,
        current: 1,
        size: 10
      },
      vipCol: [
        {
          label: '交易单号',
          prop: 'orderNumber'
        },
        {
          label: '商品名称',
          prop: 'mealName'
        },
        {
          label: '交易时间',
          prop: 'transactionTime'
        },
        {
          label: '状态',
          prop: 'payTypeName'
        },
        {
          label: '变更前会员到期时间',
          prop: 'beforeExpireTime'
        },
        {
          label: '购买会员天数',
          prop: 'grantDays'
        },
        {
          label: '变更后会员到期时间',
          prop: 'memberExpireTime'
        }
      ],
      vipList: [],
      vipPage: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.id = this.$route.query.id
    this.loadDetail()
    this.specieChangeLoadData()
  },
  methods: {
    specieChangeLoadData() {
      this.specieChangePage.userId = this.id
      specieChangePageList(this.specieChangePage).then(req => {
        this.specieChangeList = req.data.records
        this.specieChangePage.total = +req.data.total
      })
    },
    specieConsumeLoadData() {
      this.specieConsumePage.userId = this.id
      specieConsumePageList(this.specieConsumePage).then(req => {

        this.specieConsumeList = req.data.records
        this.specieConsumePage.total = +req.data.total
      })
    },
    orderInfoLoadData() {

    },
    couponsLoadData() {

    },
    vipLoadData() {
      this.vipPage.userId = this.id

      vipList(this.vipPage).then(res => {
        this.vipList = res.data.records
        this.vipPage.total = +res.data.total
      })
    },
    loadDetail() {
      userCenterDetail({ id: this.id }).then(res => {
        this.userInfo = res.data
      })
    },
    handleBack() {
    },
    tabsclick(tab, event) {
      if (tab.name === 'specieChange') {
        this.specieChangeLoadData()
      } else if (tab.name === 'specieConsume') {
        this.specieConsumeLoadData()
      } else if (tab.name === 'orderInfo') {
        this.orderInfoLoadData()
      } else if (tab.name === 'coupons') {
        this.couponsLoadData()
      } else if (tab.name === 'vipChange') {
        this.vipLoadData()
      }
    },
    specieConsumeView(row) {
      this.$router.push({
        path: '/smartCoinsManagement/order/detail',
        query: {
          orderNo: row.orderNo
        }
      })
    },
    couponsRemove(row) {

    }

  }
}
</script>

<style lang="scss" scoped></style>
