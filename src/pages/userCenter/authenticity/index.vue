<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='用户昵称'>
          <el-input v-model='form.nickName'></el-input>
        </el-form-item>
        <el-form-item label='真实姓名'>
          <el-input v-model='form.realName'></el-input>
        </el-form-item>
        <el-form-item label='联系电话'>
          <el-input v-model='form.telephone'></el-input>
        </el-form-item>
      </el-form>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' icon='el-icon-view' @click='detailView(scope.row)'>查看详情</el-button>
      </div>
    </simple-table>
  </simple-list-page>
</template>

<script>
import {selectAuthenticationPage} from '@/api/modules/authenticity.js'

export default {
  data() {
    return {
      form: {},
      columns: [
        {
          label: '用户昵称',
          prop: 'nickName'
        },
        {
          label: '真实姓名',
          prop: 'realName'
        },
        {
          label: '联系方式',
          prop: 'telephone'
        },
        {
          label: '认证时间',
          prop: 'authenticationTime'
        },
        {
          label: '注册时间',
          prop: 'registerTime'
        },
        {
          label: '证件有效截止日期',
          prop: 'validityTime',
          formatter: (row, col, value, index) => {
            console.log(row)
            console.log(col)
            console.log(value)
            return row.perpetual == 1 ? '长期有效' :  value
          }
        },
        {
          label: '审核状态',
          prop: 'examineState',
          formatter: (row, col, value, index) => {
            return value == 1 ? '通过' : (value == 2 ? '审核中' : '不通过')
          }
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      selectAuthenticationPage(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
    },
    detailView(row) {
      this.$router.push({
        path: '/userCenter/authenticity/detail',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
