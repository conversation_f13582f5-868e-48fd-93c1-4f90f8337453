<template>
  <simple-page title='查看详情' @back='handleBack ' v-loading='loading'>
    <simple-line-card header='用户信息'>
      <simple-detail-panel :columns='columns' :data='userInfo' :labelWidth='100'></simple-detail-panel>
    </simple-line-card>
    <el-row :gutter='3'>
      <el-col :span='12'>
        <div class='label-column-wrapper'>
          <label style='width:80px'>身份证正面</label>
          <p class='content'>
            <el-image style='width: 350px; height: 200px' :src='userInfo.frontIdCardUrl' :preview-src-list='[userInfo.frontIdCardUrl]'>
            </el-image>
          </p>
        </div>
      </el-col>
      <el-col :span='12'>
        <div class='label-column-wrapper'>
          <label style='width:80px'>身份证背面</label>
          <p class='content'>
            <el-image style='width: 350px; height: 200px' :src='userInfo.backIdCardUrl' :preview-src-list='[userInfo.backIdCardUrl]'>
            </el-image>
          </p>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter='3'>
      <el-col :span='24'>
        <div class='label-column-wrapper'>
          <label style='width:80px'>用户照片</label>
          <p class='content'>
            <el-image style='width: 350px; height: 200px' :src='userInfo.userPhotosUrl' :preview-src-list='[userInfo.userPhotosUrl]'>
            </el-image>
          </p>
        </div>
      </el-col>
    </el-row>
    <el-form ref='form' :model='form' label-width='80px' style='padding-top: 25px'>
      <el-form-item label='是否长期有效' label-width='120px'>
        <el-radio-group v-model="form.perpetual" :disabled="!showSelect">
          <el-radio label="1">是</el-radio>
          <el-radio label="2">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label='有效截止日期' label-width='120px'>
        <el-date-picker :disabled="!showSelect || form.perpetual == 1" v-model="form.validityTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item label='审核状态' label-width='120px' v-if="showSelect">
        <el-select v-model='form.examineState'>
          <el-option v-for='item in options' :key='item.value' :label='item.label' :value='item.value'>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label='审核状态' label-width='120px' v-else>
        <div>{{form.examineState == 0?'审核不通过':'审核通过'}}</div>
      </el-form-item>
      <el-form-item label='审核不通过原因' label-width='120px' v-if="form.examineState==0">
        <el-input :disabled="!showSelect" style='width: 300px' type='textarea' :rows="5" placeholder='请输入不通过原因' v-model='form.reason'>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-col v-if="showSelect">
          <span class='dialog-footer'>
            <el-button @click='back'>取 消</el-button>
            <el-button type='primary' @click='examine'>确 定</el-button>
          </span>
        </el-col>
      </el-form-item>
    </el-form>
  </simple-page>
</template>

<script>
import { examine, selectById } from '@/api/modules/authenticity.js'

export default {
  data() {
    return {
      showSelect: false,
      loading: false,
      form: {
        id: 0,
        validityTime: '',
        examineState: '1',
        reason: '',
        perpetual: '2'
      },
      options: [{
        value: 1,
        label: '审核通过'
      }, {
        value: 0,
        label: '审核不通过'
      }],
      id: '',
      columns: [
        {
          label: '用户昵称',
          prop: 'nickName'
        },
        {
          label: '联系方式',
          prop: 'telephone'
        },
        {
          label: '注册时间',
          prop: 'registerTime'
        },
        {
          label: '真实姓名',
          prop: 'realName'
        },
        {
          label: '身份证号',
          prop: 'idCard'
        },
        {
          label: '认证时间',
          prop: 'authenticationTime',
          labelWidth: 200
        }
      ],
      userInfo: {}
    }
  },
  mounted() {
    this.id = this.$route.query.id
    this.form.id = this.id
    this.loadDetail()
  },
  methods: {
    loadDetail() {
      selectById({ id: this.id }).then(res => {

        this.userInfo = res.data
        this.form.id = this.userInfo.id
        this.form.validityTime = this.userInfo.validityTime
        this.form.examineState = this.userInfo.examineState == 2 ? 1 : this.userInfo.examineState
        this.form.reason = this.userInfo.reason
        this.form.perpetual = '' + this.userInfo.perpetual
        this.showSelect = this.userInfo.examineState == 2 ? true : false
      })
    },
    handleBack() {
      // this.$router.back(-1)
    },
    back() {
      this.$router.back(-1)
    },
    examine() {
      if (this.form.examineState == 0 && !this.form.reason) {
        this.$message.error('请填写审核不通过原因')
        return
      }
      this.loading = true

      examine(this.form).then(rel => {
        this.loading = false
        this.$message.success(rel.msg)
        this.back()
      }).catch((e) => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.label-column-wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  color: #4c4c4c;
  padding-right: 10px;
  font-size: 14px;

  label {
    font-weight: normal;
  }

  .content {
    margin: 0;
    color: #121212;
    white-space: pre-wrap;
  }
}
</style>
