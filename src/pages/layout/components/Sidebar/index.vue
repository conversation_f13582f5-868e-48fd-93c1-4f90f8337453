<template>
  <div
    :class="{ 'has-logo': showLogo }"
    style="border-right: solid 1px #e6e6e6"
  >
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="true"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in menus"
          :key="route.id"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import routes from "@/router/router.config.js";
import variables from "@/style/variables.scss";
import Cookies from "js-cookie";
import { mapGetters } from "vuex";
import SidebarItem from "./SidebarItem";
export default {
  data() {
    return {
      userType: "",
    };
  },
  components: { SidebarItem },
  created() {
    this.userType = Cookies.get("userType");
  },
  methods: {},
  computed: {
    ...mapGetters(["sidebar", "menus"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      // return !this.sidebar.opened
      return false;
    },
    routes() {
      const arr = routes.filter(
        (item) =>
          item.children &&
          (item.component.name === "Layout" ||
            item.component.name === "LargeLayout")
      );
      return arr;
    },
  },
};
</script>
