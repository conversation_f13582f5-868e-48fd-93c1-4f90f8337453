<template>
  <div>
    <template v-if="hasOneShowingChild(item.children, item)">
      <app-link v-if="onlyOneChild.menuName" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{ 'submenu-title-noDropdown': !isNest }">
          <item :title="onlyOneChild.menuName" :is-leaf="onlyOneChild.noShowingChildren && hasParent" />
        </el-menu-item>
      </app-link>
    </template>
    <el-submenu v-else ref="subMenu" :index="item.path" popper-append-to-body>
      <template slot="title">
        <item v-if="item.menuName" :icon="item.menuName && item.icon" :title="item.menuName" />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :hasParent="true"
        :base-path="child.path"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true,
    },
    isNest: {
      type: Boolean,
      default: false,
    },
    basePath: {
      type: String,
      default: '',
    },
    hasParent: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.onlyOneChild = null
    return {}
  },
  mounted() {},
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        this.onlyOneChild = item
        return true
       })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1 && !parent.menuName) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      // console.log('path: ', path.resolve(this.basePath, routePath))
      return path.resolve(this.basePath, routePath)
    },
  },
}
</script>
