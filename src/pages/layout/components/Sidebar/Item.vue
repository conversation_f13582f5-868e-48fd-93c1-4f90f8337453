<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    isLeaf: {
      type: Boolean,
      default: false,
    },
  },
  render(h, context) {
    const { icon, title, isShow, isLeaf } = context.props
    const vnodes = []

    // if (!isShow) { return vnodes }
    if (!isLeaf) {
      vnodes.push(<svg-icon icon-class="circle" class="svg" />)
    }

    if (title) {
      vnodes.push(
        <span style={!isLeaf ? 'margin-left:0px;' : 'margin-left:20px;'} slot="title">
          {title}
        </span>
      )
    }

    return vnodes
  },
}
</script>
<style lang="scss" scoped>
.is-active {
  .svg {
    color: $primary;
  }
}
.svg {
  color: #f0eeee;
}
</style>
