<template>
  <div class="navbar">
    <logo v-if="showLogo" :collapse="isCollapse" class="logoDiv" />
    <div class="right-menu">
      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper" style="display: flex; align-items: center">
          <span style="margin-right: 5px; margin-left: 10px">
            {{ userName }}</span
          >
          <i class="el-icon-caret-bottom" style="margin-top: -5px" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <!-- <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link> -->
          <el-dropdown-item @click.native="showChangePasswordDialog">
            <span>修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
      :modal-append-to-body="false"
      custom-class="password-dialog"
    >
      <el-form
        :model="passwordForm"
        :rules="passwordRules"
        ref="passwordForm"
        label-width="80px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            type="password"
            v-model="passwordForm.oldPassword"
            placeholder="请输入原密码"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            type="password"
            v-model="passwordForm.newPassword"
            placeholder="请输入新密码"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            type="password"
            v-model="passwordForm.confirmPassword"
            placeholder="请输入新密码"
            show-password
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="updatePassword" :loading="loading"
          >修改</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { removeToken } from "@/utils/auth";
import Cookies from "js-cookie";
import { mapGetters } from "vuex";
import Logo from "./Sidebar/Logo";
import { updatePassword } from "@/api/modules/sysUser";

export default {
  components: {
    Logo,
  },
  data() {
    // 验证两次密码是否一致
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };

    return {
      userName: Cookies.get("ebs_username") || "超级管理员",
      // 修改密码对话框相关数据
      dialogVisible: false,
      loading: false,
      passwordForm: {
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: "请输入原密码", trigger: "blur" },
        ],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
        ],
        confirmPassword: [
          { required: true, message: "请再次输入新密码", trigger: "blur" },
          { validator: validateConfirmPassword, trigger: "blur" },
        ],
      },
    };
  },
  created() {},
  computed: {
    ...mapGetters(["sidebar", "avatar", "device", "realName", "roleList"]),
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
  },
  methods: {
    // 显示修改密码对话框
    showChangePasswordDialog() {
      this.dialogVisible = true;
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.passwordForm = {
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
      };
      // 如果表单已经创建，则重置验证
      this.$nextTick(() => {
        if (this.$refs.passwordForm) {
          this.$refs.passwordForm.clearValidate();
        }
      });
    },

    // 提交修改密码
    updatePassword() {
      this.$refs.passwordForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 获取当前用户ID
          let userId = "";
          try {
            const userInfo = JSON.parse(localStorage.getItem("stb-orgInfo"));
            if (userInfo && userInfo.id) {
              userId = userInfo.id;
            }
          } catch (error) {
            console.error("获取用户信息失败:", error);
          }

          if (!userId) {
            this.$message.error("获取用户信息失败，无法修改密码");
            this.loading = false;
            return;
          }

          // 调用API修改密码
          updatePassword({
            id: userId,
            password: this.passwordForm.newPassword,
          })
            .then((res) => {
              this.loading = false;
              if (res.code === 200) {
                this.$message.success("密码修改成功，请重新登录");
                this.dialogVisible = false;
                // 退出登录
                // this.logout();
              } else {
                this.$message.error(res.message || res.msg || "密码修改失败");
              }
            })
            .catch((error) => {
              this.loading = false;
              console.error("修改密码失败:", error);
              this.$message.error(
                error.message || "修改密码失败，请检查网络连接"
              );
            });
        }
      });
    },

    changeRole(item) {
      // localStorage.setItem('roleInfo', JSON.stringify(item))
      location.reload();
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    async logout() {
      // console.log(this.roleInfo)
      // console.log(this.roleList)
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        removeToken();
        Cookies.set("ebs_username", "");
        Cookies.set("userType", "");
        this.$router.push({ path: "/login" });
        this.$store.dispatch("app/closeMenus");
        // this.$store.dispatch('LogOut').then(() => {
        // })
        localStorage.removeItem("progressArr"); // 移除进度跟踪查询缓存数据
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  overflow: hidden;
  position: relative;
  background: $primary;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }
  .logoDiv {
    float: left;
    max-width: 400px;
    min-width: #{$sideBarWidth};
    margin-left: 10px;
    margin-top: 5px;
  }
  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    margin-right: 20px;
    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 14px;
      color: #ffffff;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 10px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 20px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>

<style></style>
