<template>
  <div class="org-info-container">
    <div class="content-wrapper">
      <div class="card-container">
        <h1 class="page-title">机构信息维护</h1>

        <el-form
          ref="orgForm"
          :model="orgData"
          :rules="rules"
          label-width="120px"
          :disabled="!isEditing"
          class="org-form"
          v-loading="loading"
        >
          <el-form-item label="机构名称" prop="name">
            <el-input
              v-model="orgData.name"
              placeholder="请输入机构名称"
            ></el-input>
          </el-form-item>

          <el-form-item label="机构代码" prop="code">
            <el-input
              v-model="orgData.code"
              placeholder="请输入机构代码"
            ></el-input>
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="所在省份" prop="province">
                <el-select
                  v-model="orgData.province"
                  placeholder="请选择省份"
                  style="width: 100%"
                  @change="handleProvinceChange"
                >
                  <el-option
                    v-for="item in provinceOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所在城市" prop="city">
                <el-select
                  v-model="orgData.city"
                  placeholder="请选择城市"
                  style="width: 100%"
                  @change="handleCityChange"
                  :disabled="!orgData.province"
                >
                  <el-option
                    v-for="item in cityOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所在区/县" prop="district">
                <el-select
                  v-model="orgData.district"
                  placeholder="请选择区/县"
                  style="width: 100%"
                  :disabled="!orgData.city"
                  @change="handleDistrictChange"
                >
                  <el-option
                    v-for="item in districtOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="详细地址" prop="address">
            <el-input
              v-model="orgData.address"
              placeholder="请输入详细地址"
            ></el-input>
          </el-form-item>

          <el-form-item label="邮政编码" prop="contactPostcode">
            <el-input
              v-model="orgData.contactPostcode"
              placeholder="请输入邮政编码"
            ></el-input>
          </el-form-item>

          <el-form-item label="联系人" prop="contactName">
            <el-input
              v-model="orgData.contactName"
              placeholder="请输入联系人"
            ></el-input>
          </el-form-item>

          <el-form-item label="电话" prop="contactTel">
            <el-input
              v-model="orgData.contactTel"
              placeholder="请输入电话"
            ></el-input>
          </el-form-item>

          <el-form-item label="传真" prop="fax">
            <el-input v-model="orgData.fax" placeholder="请输入传真"></el-input>
          </el-form-item>

          <el-form-item label="电子邮箱" prop="contactEmail">
            <el-input
              v-model="orgData.contactEmail"
              placeholder="请输入电子邮箱"
            ></el-input>
          </el-form-item>
        </el-form>

        <div class="action-bar">
          <el-button
            type="primary"
            @click="isEditing ? saveOrgInfo() : startEditing()"
            :loading="submitLoading"
            class="action-button"
          >
            {{ isEditing ? "保存" : "修改" }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 机构信息维护页面
 */
import {
  getProvinces,
  getCities,
  getDistricts,
  getRegionName,
  getRegionCodeByName,
} from "@/config/cityData";
import { getOrgInfo, updateOrgInfo } from "@/api/modules/organization";

export default {
  name: "OrgInfo",
  data() {
    return {
      // 是否处于编辑状态
      isEditing: false,
      // 表单加载状态
      loading: false,
      // 提交加载状态
      submitLoading: false,
      // 省市区选项
      provinceOptions: [],
      cityOptions: [],
      districtOptions: [],
      // 机构信息数据
      orgData: {
        id: "", // 机构ID
        name: "", // 机构名称
        code: "", // 机构代码
        licenseNo: "", // 机构代码
        province: "", // 省份代码
        provinceId: "", // 省份代码
        provinceName: "", // 省份名称
        city: "", // 城市代码
        cityId: "", // 城市代码
        cityName: "", // 城市名称
        district: "", // 区县代码
        areaId: "", // 区县代码
        districtName: "", // 区县名称
        areaName: "", // 区县名称
        address: "", // 详细地址
        contactPostcode: "", // 邮政编码
        contactName: "", // 联系人
        contactTel: "", // 电话
        fax: "", // 传真
        contactEmail: "", // 电子邮箱
      },
      // 表单验证规则
      rules: {
        name: [{ required: true, message: "请输入机构名称", trigger: "blur" }],
        code: [{ required: true, message: "请输入机构代码", trigger: "blur" }],
        province: [
          { required: false, message: "请选择所在省份", trigger: "change" },
        ],
        city: [
          { required: false, message: "请选择所在城市", trigger: "change" },
        ],
        district: [
          { required: false, message: "请选择所在区/县", trigger: "change" },
        ],
        areaName: [
          { required: false, message: "请选择所在区/县", trigger: "change" },
        ],
        address: [
          { required: false, message: "请输入详细地址", trigger: "blur" },
        ],
        contactPostcode: [
          { required: false, message: "请输入邮政编码", trigger: "blur" },
        ],
        contactName: [
          { required: false, message: "请输入联系人", trigger: "blur" },
        ],
        contactTel: [
          { required: false, message: "请输入电话号码", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        fax: [{ required: false, message: "请输入传真", trigger: "blur" }],
        contactEmail: [
          { required: false, message: "请输入电子邮箱", trigger: "blur" },
          {
            type: "email",
            message: "请输入正确的邮箱格式",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    // 初始化省份数据
    this.provinceOptions = getProvinces();
  },
  mounted() {
    // 页面加载时获取机构信息
    this.getOrgInfo();
  },
  methods: {
    /**
     * 处理区县选择变化
     */
    handleDistrictChange(value) {
      // 更新区县名称为areaName
      this.orgData.areaName = getRegionName(value);
      // 保持兼容性
      this.orgData.districtName = this.orgData.areaName;
    },

    /**
     * 处理城市选择变化
     */
    handleCityChange(value) {
      // 重置区县
      this.orgData.district = "";
      this.orgData.areaName = "";
      this.orgData.districtName = "";

      // 获取区县选项
      this.districtOptions = getDistricts(this.orgData.province, value);

      // 更新城市名称
      this.orgData.cityName = getRegionName(value);
    },

    /**
     * 处理省份选择变化
     */
    handleProvinceChange(value) {
      // 重置城市和区县
      this.orgData.city = "";
      this.orgData.cityName = "";
      this.orgData.district = "";
      this.orgData.areaName = "";
      this.orgData.districtName = "";
      this.districtOptions = [];

      // 获取城市选项
      this.cityOptions = getCities(value);

      // 更新省份名称
      this.orgData.provinceName = getRegionName(value);
    },

    /**
     * 处理后端返回的区域数据
     * 支持名称或代码转换
     * @param {Object} data 后端返回的数据
     */
    processRegionData(data) {
      // 创建副本避免直接修改原始数据
      const processedData = { ...data };

      // 根据截图中的实际返回数据处理字段映射
      // 处理API返回的字段名与前端表单字段名不一致的情况

      // 处理机构代码字段
      if (processedData.code) {
        processedData.licenseNo = processedData.code;
      }

      // 处理省市区字段
      // 省份处理
      if (processedData.provinceId) {
        processedData.province = processedData.provinceId;
      }
      if (processedData.provinceName && !processedData.province) {
        processedData.province = getRegionCodeByName(
          processedData.provinceName
        );
      } else if (processedData.province && !processedData.provinceName) {
        processedData.provinceName = getRegionName(processedData.province);
      }

      // 城市处理 - 优先保留cityName的值
      if (processedData.cityId) {
        processedData.city = processedData.cityId;
      }
      if (processedData.cityName) {
        // 保留原始cityName
        if (!processedData.city) {
          // 如果没有city代码，尝试通过名称获取
          const cityCode = getRegionCodeByName(processedData.cityName);
          if (cityCode) {
            processedData.city = cityCode;
          }
        }
      } else if (processedData.city && !processedData.cityName) {
        // 如果只有city代码，尝试获取名称
        processedData.cityName = getRegionName(processedData.city);
      }

      // 区县处理 - 重点处理areaName字段
      if (processedData.areaId) {
        processedData.district = processedData.areaId;
      }

      // 确保areaName存在，这是前端要绑定的字段
      if (processedData.areaName) {
        // 如果areaName存在，则确保district代码也存在
        if (!processedData.district) {
          const districtCode = getRegionCodeByName(processedData.areaName);
          if (districtCode) {
            processedData.district = districtCode;
          }
        }
        // 同步到districtName以保持兼容性
        processedData.districtName = processedData.areaName;
      } else if (processedData.districtName) {
        // 如果只有districtName存在，则同步到areaName
        processedData.areaName = processedData.districtName;
        if (!processedData.district) {
          const districtCode = getRegionCodeByName(processedData.districtName);
          if (districtCode) {
            processedData.district = districtCode;
          }
        }
      } else if (processedData.district && !processedData.areaName) {
        // 如果只有district代码，尝试获取名称
        processedData.areaName = getRegionName(processedData.district);
        processedData.districtName = processedData.areaName;
      }

      // 处理联系人相关字段
      if (processedData.contactName === null) processedData.contactName = "";
      if (processedData.contactTel === null) processedData.contactTel = "";
      if (processedData.contactEmail === null) processedData.contactEmail = "";
      if (processedData.contactPostcode === null)
        processedData.contactPostcode = "";
      if (processedData.fax === null) processedData.fax = "";

      console.log("API返回的原始数据:", data);
      console.log("处理后的数据:", processedData);

      return processedData;
    },

    /**
     * 获取机构信息
     */
    getOrgInfo() {
      // 从localStorage获取机构ID
      let orgId = "";
      try {
        const orgInfoStr = localStorage.getItem("stb-orgInfo");
        if (orgInfoStr) {
          const orgInfo = JSON.parse(orgInfoStr);
          orgId = orgInfo.orgId || "";
        }
      } catch (error) {
        console.error("获取本地存储的机构信息失败:", error);
      }

      // 检查是否有机构ID
      if (!orgId) {
        this.$message.error("未找到机构ID，无法获取机构信息");
        this.loading = false;
        // 使用本地数据作为备选
        this.useLocalData();
        return;
      }

      this.loading = true;

      // 使用封装的API函数
      getOrgInfo({ id: orgId })
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            // 处理后端返回数据，确保省市区code和名称都有值
            const processedData = this.processRegionData(res.data || {});

            console.log("原始返回数据:", res.data);
            console.log("处理后的数据:", processedData);

            // 特别检查areaName是否存在，如果不存在但有districtName，则使用districtName
            if (!processedData.areaName && processedData.districtName) {
              processedData.areaName = processedData.districtName;
            }
            // 如果有district代码但没有areaName，则通过代码获取名称
            else if (processedData.district && !processedData.areaName) {
              processedData.areaName = getRegionName(processedData.district);
              processedData.districtName = processedData.areaName; // 保持兼容性
            }

            // 赋值到表单
            this.orgData = processedData;

            // 处理省市区三级联动
            if (processedData.province) {
              // 加载省份对应的城市选项
              this.cityOptions = getCities(processedData.province);

              // 如果有城市数据，加载对应的区县选项
              if (processedData.city) {
                this.districtOptions = getDistricts(
                  processedData.province,
                  processedData.city
                );
              }
            }
          } else {
            this.$message.error(res.message || res.msg || "获取机构信息失败");
            // 使用本地数据
            this.useLocalData();
          }
        })
        .catch((error) => {
          this.loading = false;
          console.error("获取机构信息失败:", error);
          this.$message.error("获取机构信息失败，请检查网络连接");
          // 使用本地数据
          this.useLocalData();
        });
    },

    /**
     * 使用本地数据（用于接口尚未实现时调试）
     */
    useLocalData() {
      // 模拟数据 - 使用浙江省杭州市上城区
      const provinceValue = "330000"; // 浙江省
      const cityValue = "330100"; // 杭州市
      const districtValue = "330102"; // 上城区

      this.orgData = {
        id: "",
        name: "浙江公正检验中心有限公司",
        code: "913300007549452X8",
        licenseNo: "913300007549452X8",
        province: provinceValue,
        provinceId: "330000",
        provinceName: "浙江省",
        city: cityValue,
        cityId: "330100",
        cityName: "杭州市",
        district: districtValue,
        areaId: "330102",
        districtName: "上城区",
        areaName: "上城区", // 确保areaName字段存在
        address: "浙江省杭州市城头巷128号",
        contactPostcode: "315012896",
        contactName: "张三",
        contactTel: "13545296203",
        fax: "0574-870022",
        contactEmail: "<EMAIL>",
      };

      // 加载城市和区县数据
      this.cityOptions = getCities(provinceValue);
      this.districtOptions = getDistricts(provinceValue, cityValue);
    },

    /**
     * 开始编辑
     */
    startEditing() {
      this.isEditing = true;
    },

    /**
     * 准备提交数据
     * @returns {Object} 处理后的提交数据
     */
    prepareSubmitData() {
      const data = { ...this.orgData };

      // 从localStorage获取机构ID，确保提交数据包含ID
      // try {
      //   const orgInfoStr = localStorage.getItem("stb-orgInfo");
      //   if (orgInfoStr) {
      //     const orgInfo = JSON.parse(orgInfoStr);
      //     // 使用localStorage中的id，优先级高于表单中可能已有的ID
      //     data.id = orgInfo.id || data.id;
      //   }
      // } catch (error) {
      //   console.error("获取本地存储的机构信息失败:", error);
      // }

      // 确保省市区代码和名称都正确设置
      // 根据截图，后端可能更关注名称字段，所以确保名称字段有值
      if (data.province && (!data.provinceName || data.provinceName === "")) {
        data.provinceName = getRegionName(data.province);
      }

      if (data.city && (!data.cityName || data.cityName === "")) {
        data.cityName = getRegionName(data.city);
      }

      // 确保areaName字段正确赋值
      if (data.district && (!data.areaName || data.areaName === "")) {
        data.areaName = getRegionName(data.district);
        // 保持兼容性
        data.districtName = data.areaName;
      }

      // 根据前后端约定，将code字段值设置为licenseNo的值（机构代码）
      if (data.licenseNo) {
        data.code = data.licenseNo;
      }

      console.log("提交的数据:", data); // 调试用

      return data;
    },

    /**
     * 保存机构信息
     */
    saveOrgInfo() {
      this.$refs.orgForm.validate((valid) => {
        if (valid) {
          // 处理提交数据
          const submitData = this.prepareSubmitData();

          // 检查是否有机构ID
          if (!submitData.id) {
            this.$message.error("未找到机构ID，无法保存机构信息");
            return false;
          }

          this.submitLoading = true;

          // 使用封装的API函数
          updateOrgInfo(submitData)
            .then((res) => {
              this.submitLoading = false;
              if (res.code === 200) {
                this.$message.success("保存成功");
                this.isEditing = false;
                // 触发系统管理-机构信息同步更新
                this.$bus && this.$bus.$emit("org-info-updated", submitData);
              } else {
                this.$message.error(res.message || res.msg || "保存失败");
              }
            })
            .catch((error) => {
              this.submitLoading = false;
              console.error("保存机构信息失败:", error);
              this.$message.error(error.message || "保存失败，请检查网络连接");
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.org-info-container {
  padding: 20px;
  min-height: calc(100vh - 60px);

  .content-wrapper {
    max-width: 1000px;
    margin: 0 auto;
  }

  .card-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

    .page-title {
      margin: 0 0 20px 0;
      font-size: 18px;
      color: #333;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;
    }

    .org-form {
      padding-bottom: 20px;
    }

    .action-bar {
      display: flex;
      justify-content: flex-end;
      border-top: 1px solid #ebeef5;
      padding-top: 20px;

      .action-button {
        min-width: 80px;
      }
    }
  }
}
</style>
