<template>
  <div class="employee-manage">
    <el-tabs
      v-model="activeTab"
      @tab-click="handleTabClick"
      class="custom-tabs"
    >
      <el-tab-pane label="员工管理" name="employee">
        <employee-tab></employee-tab>
      </el-tab-pane>
      <el-tab-pane label="抽样队管理" name="team">
        <team-tab ref="teamTab"></team-tab>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import EmployeeTab from "./components/EmployeeTab.vue";
import TeamTab from "./components/TeamTab.vue";

export default {
  name: "EmployeeManage",
  components: {
    EmployeeTab,
    TeamTab,
  },
  data() {
    return {
      activeTab: "employee",
    };
  },
  methods: {
    handleTabClick(tab) {
      if (tab.name === "team" && this.$refs.teamTab) {
        this.$refs.teamTab.initData();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.employee-manage {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;

  .custom-tabs {
    /deep/ .el-tabs__nav-wrap::after {
      display: none; /* 移除Tab下的深灰色线 */
    }
  }
}
</style>
