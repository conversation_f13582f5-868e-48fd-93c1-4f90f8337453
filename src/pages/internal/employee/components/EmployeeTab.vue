<template>
  <div class="employee-tab">
    <div class="filter-options">
      <el-form ref="searchForm" :inline="true" :model="searchForm" size="small">
        <el-form-item label="账号：">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="用户姓名：">
          <el-input
            v-model="searchForm.realName"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadData()">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="operation-btns">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAdd"
          >添加用户</el-button
        >
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="list"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="username"
          label="账号"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="realName"
          label="员工姓名"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="telephone"
          label="联系方式"
          min-width="150"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="roleName"
          label="角色"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="remark"
          label="备注"
          min-width="200"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" width="220" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-refresh"
              @click="handleResetPass(scope.row)"
              >重置密码</el-button
            >
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="text"
              icon="el-icon-delete"
              class="delete-btn"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
        ></el-pagination>
      </div>
    </div>

    <!-- 添加/编辑员工弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="employeeForm"
        :rules="rules"
        ref="employeeForm"
        label-width="100px"
        autocomplete="off"
      >
        <el-form-item label="员工账号" prop="username">
          <el-input
            v-model="employeeForm.username"
            placeholder="请输入员工账号"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="员工密码" prop="password" v-if="!employeeForm.id">
          <div class="password-input-container">
            <el-input
              v-model="employeeForm.password"
              :type="passwordVisible ? 'text' : 'password'"
              placeholder="请输入员工密码"
              autocomplete="new-password"
            ></el-input>
            <i
              class="el-icon-view password-eye"
              @click="passwordVisible = !passwordVisible"
            ></i>
          </div>
        </el-form-item>
        <el-form-item label="员工姓名" prop="realName">
          <el-input
            v-model="employeeForm.realName"
            placeholder="请输入员工姓名"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系方式" prop="telephone">
          <el-input
            v-model="employeeForm.telephone"
            placeholder="请输入联系方式"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="角色" prop="roleName">
          <el-select v-model="employeeForm.roleName" placeholder="请选择角色">
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="备注">
          <el-input
            v-model="employeeForm.remark"
            type="textarea"
            placeholder="请输入备注信息"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <!-- 删除确认弹窗 -->
    <el-dialog
      title="确定要删除该员工?"
      :visible.sync="deleteDialogVisible"
      width="400px"
    >
      <div>员工删除后，该员工将无法登录平台</div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="confirmDelete"
          :loading="deleteLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getEmployeeList,
  addEmployee,
  updateEmployee,
  deleteEmployee,
  resetPassword,
} from "@/api/modules/employee";

export default {
  name: "EmployeeTab",
  data() {
    // 自定义验证规则：密码在添加员工时是必填的
    const validatePassword = (rule, value, callback) => {
      if (this.dialogTitle === "添加员工" && (!value || value.trim() === "")) {
        callback(new Error("请输入员工密码"));
      } else {
        callback();
      }
    };

    return {
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10,
      },
      dialogVisible: false,
      dialogTitle: "添加员工",
      employeeForm: {
        id: "",
        username: "",
        password: "",
        realName: "",
        telephone: "",
        roleName: "",
        remark: "",
      },
      rules: {
        username: [
          { required: true, message: "请输入员工账号", trigger: "blur" },
        ],
        password: [{ validator: validatePassword, trigger: "blur" }],
        realName: [
          { required: true, message: "请输入员工姓名", trigger: "blur" },
        ],
        telephone: [
          { required: true, message: "请输入联系方式", trigger: "blur" },
        ],
        roleName: [
          { required: true, message: "请选择角色", trigger: "change" },
        ],
      },
      roleOptions: [
        { label: "管理员", value: "管理员" },
        { label: "抽样员", value: "抽样员" },
        { label: "系统管理员", value: "系统管理员" },
        { label: "普通用户", value: "普通用户" },
      ],
      deleteDialogVisible: false,
      deleteEmployeeId: "",
      passwordVisible: false,
      loading: false,
      submitLoading: false,
      deleteLoading: false,
      searchForm: {
        username: "",
        realName: "",
      },
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    loadData() {
      const params = {
        ...this.searchForm,
        current: this.page.current,
        size: this.page.size,
      };
      this.loading = true;
      getEmployeeList(params)
        .then((res) => {
          if (res.code === 200) {
            this.list = (res.data.records || []).map((item) => {
              const newItem = { ...item };
              Object.keys(newItem).forEach((key) => {
                if (
                  newItem[key] === null ||
                  newItem[key] === undefined ||
                  newItem[key] === ""
                ) {
                  newItem[key] = "-";
                }
              });
              return newItem;
            });
            this.page.total = parseInt(res.data.total || 0);
          } else {
            this.$message.error(res.msg || "获取员工列表失败");
          }
        })
        .catch((err) => {
          console.error("获取员工列表失败:", err);
          this.$message.error("获取员工列表失败，请检查网络连接");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSizeChange(val) {
      this.page.size = val;
      this.loadData();
    },
    handleCurrentChange(val) {
      this.page.current = val;
      this.loadData();
    },
    handleAdd() {
      this.dialogTitle = "添加员工";
      this.dialogVisible = true;
      this.employeeForm = {
        id: "",
        username: "",
        password: "123456",
        realName: "",
        telephone: "",
        roleName: "",
        remark: "",
      };
      this.$nextTick(() => {
        this.$refs.employeeForm && this.$refs.employeeForm.clearValidate();
      });
    },
    handleEdit(row) {
      this.dialogTitle = "编辑员工";
      this.employeeForm = {
        id: row.id,
        username: row.username,
        password: "",
        realName: row.realName,
        telephone: row.telephone,
        roleName: row.roleName,
        remark: row.remark || "",
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.employeeForm && this.$refs.employeeForm.clearValidate();
      });
    },
    handleDelete(row) {
      this.deleteEmployeeId = row.id;
      this.deleteDialogVisible = true;
    },
    submitForm() {
      this.$refs.employeeForm.validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          const formData = JSON.parse(JSON.stringify(this.employeeForm));

          if (formData.id) {
            // 编辑模式，删除密码字段
            delete formData.password;

            // 发送请求
            updateEmployee(formData)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success("员工信息更新成功");
                  this.dialogVisible = false;
                  this.loadData();
                } else {
                  this.$message.error(res.msg || "员工信息更新失败");
                }
              })
              .catch((err) => {
                console.error("更新员工信息失败:", err);
                this.$message.error("更新员工信息失败，请检查网络连接");
              })
              .finally(() => {
                this.submitLoading = false;
              });
          } else {
            // 新增，准备API需要的数据格式
            const registerData = {
              username: formData.username,
              password: formData.password,
              realName: formData.realName,
              telephone: formData.telephone,
              roleName: formData.roleName,
              remark: formData.remark,
              roleIds: ["1"], // 员工默认传角色值为1
            };

            addEmployee(registerData)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success("员工添加成功");
                  this.dialogVisible = false;
                  this.loadData();
                } else {
                  this.$message.error(res.msg || "员工添加失败");
                }
              })
              .catch((err) => {
                console.error("添加员工失败:", err);
                this.$message.error("添加失败:" + err.msg);
              })
              .finally(() => {
                this.submitLoading = false;
              });
          }
        }
      });
    },
    confirmDelete() {
      this.deleteLoading = true;
      deleteEmployee(this.deleteEmployeeId)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success("员工删除成功");
            this.deleteDialogVisible = false;
            this.loadData();
          } else {
            this.$message.error(res.msg || "员工删除失败");
          }
        })
        .catch((err) => {
          console.error("删除员工失败:", err);
          this.$message.error("删除员工失败，请检查网络连接");
        })
        .finally(() => {
          this.deleteLoading = false;
        });
    },
    handleReset() {
      this.searchForm = { username: "", realName: "" };
      this.loadData();
    },
    // 重置密码
    handleResetPass(row) {
      this.$confirm("确定要重置该用户密码为123456?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const params = {
          userId: row.id,
        };
        resetPassword(params).then((res) => {
          if (res.code === 200) {
            this.$message.success("重置成功!");
          } else {
            this.$message.error(res.msg || "重置失败");
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.employee-tab {
  width: 100%;

  .filter-options {
    margin-bottom: 20px;

    .operation-btns {
      margin-bottom: 10px;
    }
  }

  .table-container {
    width: 100%;
    overflow-x: auto;

    .delete-btn {
      color: #f56c6c;
      margin-left: 10px;
    }
  }

  .pagination-container {
    margin-bottom: 20px;
    text-align: right;
  }
}

.password-input-container {
  position: relative;
}

.password-eye {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #c0c4cc;
  font-size: 16px;
}

/deep/ .el-table__header {
  th {
    &:last-child {
      transform: translateY(-1px) !important;
    }
  }
}
</style>
