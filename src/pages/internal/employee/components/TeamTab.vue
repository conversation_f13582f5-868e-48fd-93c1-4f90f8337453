<template>
  <div class="team-tab">
    <div class="filter-options">
      <div class="operation-btns">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAdd"
          >添加抽样队</el-button
        >
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="list"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="抽样队名称"
          min-width="150"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="code"
          label="抽样队编号"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="leaderName"
          label="抽样队长"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="teamMember"
          label="队员"
          min-width="180"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="remark"
          label="备注"
          min-width="150"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="text"
              icon="el-icon-delete"
              class="delete-btn"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page.current"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="page.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
        ></el-pagination>
      </div>
    </div>

    <!-- 添加/编辑抽样队弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      @closed="handleDialogClosed"
    >
      <el-form
        :model="teamForm"
        :rules="rules"
        ref="teamForm"
        label-width="100px"
      >
        <el-form-item label="抽样队名称" prop="name">
          <el-input
            v-model="teamForm.name"
            placeholder="请输入抽样队名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="抽样队编号" prop="code">
          <el-input
            v-model="teamForm.code"
            placeholder="请输入抽样队编号"
          ></el-input>
        </el-form-item>
        <el-form-item label="抽样队长" prop="leaderId">
          <el-select
            v-model="teamForm.leaderId"
            placeholder="请选择抽样队长"
            filterable
            value-key="id"
            @change="handleLeaderChange"
          >
            <el-option
              v-for="item in userOptions"
              :key="item.id"
              :label="item.realName"
              :value="String(item.id)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="队员" prop="memberIds">
          <el-select
            v-model="teamForm.memberIds"
            multiple
            placeholder="请选择队员"
            filterable
            @change="handleMembersChange"
          >
            <el-option
              v-for="item in userOptions"
              :key="item.id"
              :label="item.realName"
              :value="String(item.id)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="teamForm.remark"
            type="textarea"
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <!-- 删除确认弹窗 -->
    <el-dialog
      title="确定要删除该抽样队?"
      :visible.sync="deleteDialogVisible"
      width="400px"
    >
      <div>抽样队删除后，该抽样队下的所有成员将被移除</div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="confirmDelete"
          :loading="deleteLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSamplingTeamList,
  addSamplingTeam,
  updateSamplingTeam,
  deleteSamplingTeam,
  getAllUsers,
} from "@/api/modules/employee";

export default {
  name: "TeamTab",
  data() {
    return {
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10,
      },
      dialogVisible: false,
      dialogTitle: "添加抽样队",
      teamForm: {
        id: "",
        name: "",
        code: "",
        leaderId: "",
        memberIds: [],
        remark: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入抽样队名称", trigger: "blur" },
        ],
        code: [
          { required: true, message: "请输入抽样队编号", trigger: "blur" },
        ],
        leaderId: [
          { required: true, message: "请选择抽样队长", trigger: "change" },
        ],
        memberIds: [
          { required: true, message: "请选择队员", trigger: "change" },
          {
            type: "array",
            min: 1,
            message: "至少选择一名队员",
            trigger: "change",
          },
        ],
      },
      userOptions: [],
      deleteDialogVisible: false,
      deleteTeamId: "",
      isDataLoaded: false,
      loading: false,
      submitLoading: false,
      deleteLoading: false,
    };
  },
  methods: {
    // 新增方法：处理对话框关闭事件
    handleDialogClosed() {
      // 重置表单数据
      this.teamForm = {
        id: "",
        name: "",
        code: "",
        leaderId: "",
        memberIds: [],
        remark: "",
      };
      this.$refs.teamForm && this.$refs.teamForm.resetFields();
    },
    // 新增方法：处理抽样队长变更
    handleLeaderChange(val) {
      console.log("队长选择变更：", val, "类型：", typeof val);
      // 确保始终是字符串类型
      this.teamForm.leaderId = String(val);
    },
    // 新增方法：处理队员变更
    handleMembersChange(val) {
      console.log("队员选择变更：", val);
      // 确保所有ID都是字符串类型
      this.teamForm.memberIds = val.map((id) => String(id));
    },
    // 获取所有用户列表
    getUserList() {
      getAllUsers()
        .then((res) => {
          if (res.code === 200) {
            this.userOptions = res.data || [];
          } else {
            this.$message.error(res.msg || "获取用户列表失败");
          }
        })
        .catch((err) => {
          console.error("获取用户列表失败:", err);
          this.$message.error("获取用户列表失败，请检查网络连接");
        });
    },
    // 初始化数据，仅在切换到抽样队Tab时调用
    initData() {
      if (!this.isDataLoaded) {
        this.loadData();
        this.getUserList();
        this.isDataLoaded = true;
      }
    },
    loadData() {
      const params = {
        current: this.page.current,
        size: this.page.size,
      };
      this.loading = true;
      getSamplingTeamList(params)
        .then((res) => {
          if (res.code === 200) {
            // 处理返回的数据，转换队员ID为名称
            this.list = (res.data.records || []).map((item) => {
              return {
                ...item,
                // 如果后端返回的是teamMemberIds，处理成可读的队员名称
                members: item.teamMemberIds
                  ? this.formatMembers(item.teamMemberIds)
                  : item.teamMember || "-",
              };
            });
            // 使用 parseInt 确保 total 是数字类型，并提供默认值 0
            this.page.total = parseInt(res.data.total) || 0;
          } else {
            this.$message.error(res.msg || "获取抽样队列表失败");
          }
        })
        .catch((err) => {
          console.error("获取抽样队列表失败:", err);
          this.$message.error("获取抽样队列表失败，请检查网络连接");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSizeChange(val) {
      this.page.size = val;
      this.loadData();
    },
    handleCurrentChange(val) {
      this.page.current = val;
      this.loadData();
    },
    handleAdd() {
      this.dialogTitle = "添加抽样队";
      this.dialogVisible = true;
      this.teamForm = {
        id: "",
        name: "",
        code: "",
        leaderId: "",
        memberIds: [],
        remark: "",
      };
      // 每次打开添加对话框时都重新获取用户列表
      this.getUserList();
      this.$nextTick(() => {
        this.$refs.teamForm && this.$refs.teamForm.clearValidate();
      });
    },
    handleEdit(row) {
      this.dialogTitle = "编辑抽样队";
      const formData = JSON.parse(JSON.stringify(row));

      // 打开对话框
      this.dialogVisible = true;

      // 获取用户列表
      this.getUserList();

      // 使用$nextTick和setTimeout的组合确保DOM更新且用户列表已完全加载
      this.$nextTick(() => {
        setTimeout(() => {
          // 将后端返回的数据格式转换为表单所需格式
          this.teamForm = {
            id: formData.id,
            name: formData.name,
            code: formData.code,
            // 确保leaderId是字符串类型
            leaderId: formData.leaderId ? String(formData.leaderId) : "",
            // 如果后端返回的是逗号分隔的字符串，转换为数组，并确保每个ID都是字符串类型
            memberIds: formData.teamMemberIds
              ? formData.teamMemberIds.split(",").map((id) => String(id.trim()))
              : [],
            remark: formData.remark || "",
          };

          // 输出日志检查数据类型
          console.log(
            "设置的leaderId:",
            this.teamForm.leaderId,
            "类型:",
            typeof this.teamForm.leaderId
          );
          console.log("用户选项列表:", this.userOptions);

          this.$refs.teamForm && this.$refs.teamForm.clearValidate();
        }, 800); // 增加延迟时间确保数据加载完成
      });
    },
    handleDelete(row) {
      this.deleteTeamId = row.id;
      this.deleteDialogVisible = true;
    },
    submitForm() {
      this.$refs.teamForm.validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          const formData = JSON.parse(JSON.stringify(this.teamForm));

          // 转换为后端需要的数据结构
          const submitData = {
            id: formData.id || undefined,
            code: formData.code,
            name: formData.name,
            leaderId: formData.leaderId,
            // 从用户列表中查找队长名称
            leaderName: this.findUserName(formData.leaderId),
            remark: formData.remark,
            // 将队员ID数组转换为逗号分隔的字符串
            teamMemberIds: formData.memberIds.join(","),
            // 将队员名称数组转换为逗号分隔的字符串
            teamMember: this.getMemberNames(formData.memberIds),
          };

          if (formData.id) {
            // 编辑
            updateSamplingTeam(submitData)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success("抽样队信息更新成功");
                  this.dialogVisible = false;
                  this.loadData();
                } else {
                  this.$message.error(res.msg || "抽样队信息更新失败");
                }
              })
              .catch((err) => {
                console.error("更新抽样队信息失败:", err);
                this.$message.error("更新抽样队信息失败，请检查网络连接");
              })
              .finally(() => {
                this.submitLoading = false;
              });
          } else {
            // 新增
            addSamplingTeam(submitData)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success("抽样队添加成功");
                  this.dialogVisible = false;
                  this.loadData();
                } else {
                  this.$message.error(res.msg || "抽样队添加失败");
                }
              })
              .catch((err) => {
                console.error("添加抽样队失败:", err);
                this.$message.error("添加抽样队失败，请检查网络连接");
              })
              .finally(() => {
                this.submitLoading = false;
              });
          }
        }
      });
    },
    // 根据用户ID查找用户名称
    findUserName(userId) {
      const user = this.userOptions.find((user) => user.id === userId);
      return user ? user.realName : "";
    },
    // 根据用户ID数组获取用户名称，并用逗号连接
    getMemberNames(userIds) {
      if (!userIds || !userIds.length) return "";
      return userIds
        .map((id) => this.findUserName(id))
        .filter((name) => name)
        .join(",");
    },
    // 处理队员显示，将ID转换为名称
    formatMembers(memberIds) {
      if (!memberIds) return "-";
      return this.getMemberNames(memberIds.split(","));
    },
    confirmDelete() {
      this.deleteLoading = true;
      deleteSamplingTeam(this.deleteTeamId)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success("抽样队删除成功");
            this.deleteDialogVisible = false;
            this.loadData();
          } else {
            this.$message.error(res.msg || "抽样队删除失败");
          }
        })
        .catch((err) => {
          console.error("删除抽样队失败:", err);
          this.$message.error("删除抽样队失败，请检查网络连接");
        })
        .finally(() => {
          this.deleteLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.team-tab {
  width: 100%;

  .filter-options {
    margin-bottom: 20px;

    .operation-btns {
      margin-bottom: 10px;
    }
  }

  .table-container {
    width: 100%;
    overflow-x: auto;

    .delete-btn {
      color: #f56c6c;
      margin-left: 10px;
    }
  }

  .pagination-container {
    margin-bottom: 20px;
    text-align: right;
  }
}

/deep/ .el-table__header {
  th {
    &:last-child {
      transform: translateY(-1px) !important;
    }
  }
}
</style>
