<template>
  <div class='wrapper'>
    <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot='search'>
        <el-form :inline='true' :model='form'>
          <el-form-item label='课程名称：' class='ml20'>
            <el-input v-model='form.curriculumName'></el-input>
          </el-form-item>
          <el-form-item label='用户昵称：' class='ml20'>
            <el-input v-model='form.nickName'></el-input>
          </el-form-item>
          <el-form-item label='联系方式：' class='ml20'>
            <el-input v-model='form.phone'></el-input>
          </el-form-item>
        </el-form>
      </div>
      <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
        <div slot='options' slot-scope='scope'>
          <el-button type='text' size='mini' @click='handleView(scope.row)'>订单详情</el-button>
        </div>
      </simple-table>
    </simple-list-page>
    <detail-dialog ref='detailDialog' @confirm='loadData'></detail-dialog>
  </div>
</template>

<script>
import DetailDialog from './components/detail'
import {pageVideoOrder} from '@/api/modules/sampleVideo'

export default {
  components: {DetailDialog},
  data() {
    return {
      form: {
        curriculumName: null,
        nickName: null,
        phone: null
      },
      columns: [
        {
          label: '交易单号',
          width: 180,
          prop: 'orderNumber'
        },
        {
          label: '课程名称',
          prop: 'curriculumName'
        },
        {
          label: '使用范围',
          prop: 'scopeOfApplicationNames'
        },
        {
          label: '课程价格（元）',
          prop: 'curriculumPrice'
        },
        {
          label: '优惠券',
          prop: 'discountPrice'
        },
        {
          label: '支付方式',
          prop: 'payTypeName'
        },
        {
          label: '应付金额（元）',
          prop: 'copeWithPrice'
        },
        {
          label: '实付金额（元）',
          prop: 'paidInPrice'
        },
        {
          label: '交易时间',
          width: 140,
          prop: 'transactionTime'
        },
        {
          label: '交易状态',
          prop: 'statusLabel'
        },
        {
          label: '用户昵称',
          prop: 'nickName'
        },
        {
          label: '会员类型',
          prop: 'userMemberTypeLabel'
        },
        {
          label: '联系方式',
          prop: 'phone'
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {
        curriculumName: null,
        nickName: null,
        phone: null
      }
      this.handleQuery()
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageVideoOrder(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleView(row) {
      this.$refs.detailDialog.open(row.id)
    }
  }
}
</script>

<style lang='scss' scoped></style>
