<template>
  <div class='wrapper'>
    <el-dialog title='编辑' :visible.sync='dialogVisible' width='60%' @close='close'>
      <el-form :model='form' :rules='rules' ref='form' label-width='200px' label-position='left' size='small' class='dialog-form'>
        <el-form-item class='item' label='课程名称：' prop='curriculumName'>
          <el-input v-model='form.curriculumName'></el-input>
        </el-form-item>
        <el-form-item class='item' label='适用范围：' prop='foodId'>
          <el-select ref='select' v-model='foodTypeName' placeholder='请选择' :popper-append-to-body='false' class='select-tree' style='width:100%'>
            <el-option style='height: auto;'>
              <!-- option展开高度太小，把height设置为auto就好啦 -->
              <el-tree ref='tree' :data='treeCate' node-key='id' accordion highlight-current :props='defaultProps' icon-class='el-icon-arrow-down' @node-click='nodeclick' />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class='item' label='适用样品：'>
          <el-input v-model='form.sampleNames'></el-input>
        </el-form-item>
        <el-form-item class='item' label='非会员用户是否免费：'>
          <el-radio-group v-model='form.ordinaryFree' class='rg1'>
            <el-radio :label='1' class='ra'>是</el-radio>
            <el-radio :label='0' class='ra'>否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if='form.ordinaryFree===0' class='item' label='课程价格（元）：' prop='price1'>
          <el-input v-model='form.price1'></el-input>
        </el-form-item>
        <el-form-item class='item' label='会员用户是否免费：'>
          <el-radio-group v-model='form.memberFree' class='rg1'>
            <el-radio :label='1' class='ra'>是</el-radio>
            <el-radio :label='0' class='ra'>否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if='form.memberFree===0' class='item' label='VIP用户课程价格（元）：' prop='price2'>
          <el-input v-model='form.price2'></el-input>
        </el-form-item>
        <el-form-item v-if='form.memberFree===0' class='item' label='SVIP用户课程价格（元）：' prop='price3'>
          <el-input v-model='form.price3'></el-input>
        </el-form-item>
        <el-form-item class='item' label='上传视频：' prop='video'>
          <el-upload class='upload-demo' style='height: 200px' drag :action='uploadUrl' :headers='headers' :file-list='fileList' :on-success='handleVideoSuccess' :on-remove='handleVideoRemove'
            :before-upload='beforeVideoUpload' accept='.mp4,.avi,.flv'>
            <i class='el-icon-upload'></i>
            <div class='el-upload__text'>将文件拖到此处，或<em>点击上传</em></div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { treeFoodCategory } from '@/api/modules/foodCategory'
import { preSampleVideo, updateSampleVideo } from '@/api/modules/sampleVideo'
import { getToken } from '@/utils/auth'

export default {
  data() {
    let validateUpload = (rule, value, callback) => {
      if (!this.form.videoFile) {
        callback(new Error('请上传视频'))
      } else {
        callback()
      }
    }
    return {
      id: null,
      dialogVisible: false,
      loading: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/oss/endpoint/put-public-file',
      headers: {
        Authorization: getToken()
      },
      foodTypeName: '',
      treeCate: [],
      fileList: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      form: {
        curriculumName: undefined,//课程名称
        sampleNames: undefined,//样品名称
        memberFree: 0,//会员是否免费 0 否 1 是
        ordinaryFree: 0,//非会员是否免费 0 否 1 是
        curriculumPrice: undefined,//价格
        videoFile: undefined,//视频文件 json格式{\name\,\\,\url\:\\}
        foodId: undefined,//食品id
        price1: undefined,
        price2: undefined,
        price3: undefined,
        curriculumTypePriceList: [{
          price: undefined,//价格
          memberType: undefined//会员类型（0 VIP会员 1 SVIP会员）
        }]
      },
      rules: {
        curriculumName: [
          { required: true, message: '请输入课程名称', trigger: 'blur' }
        ],
        foodId: [
          { required: true, message: '请选择适用范围', trigger: 'change' }
        ],
        sampleNames: [
          { required: true, message: '请输入适用样品', trigger: 'blur' }
        ],
        price1: [
          { required: true, message: '请输入课程价格', trigger: 'blur' }
        ],
        price2: [
          { required: true, message: '请输入VIP用户课程价格', trigger: 'blur' }
        ],
        price3: [
          { required: true, message: '请输入SVIP用户课程价格', trigger: 'blur' }
        ],
        video: [
          { required: true, validator: validateUpload, trigger: 'change' }
        ]
      },
      optionMemberType: [
        { value: 0, label: 'VIP会员' },
        { value: 1, label: 'SVIP会员' }
      ]
    }
  },
  mounted() {
    this.firstlyCategory()
  },
  methods: {
    nodeclick(data) {
      this.form.foodId = data.id
      this.foodTypeName = data.name
      this.$refs.tree.setCheckedNodes([data.id])
      this.$refs.select.blur()
    },
    firstlyCategory() {
      treeFoodCategory().then(req => {
        this.treeCate = req.data
      })
    },
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {

          let list = []
          // if (this.form.price1)
          //   list.push({'price': this.form.price1, 'memberType': '0'})
          if (this.form.price2)
            list.push({ 'price': this.form.price2, 'memberType': '1' })
          if (this.form.price3)
            list.push({ 'price': this.form.price3, 'memberType': '2' })

          let data = {
            id: this.id,
            curriculumName: this.form.curriculumName,
            sampleNames: this.form.sampleNames,
            memberFree: this.form.memberFree,
            ordinaryFree: this.form.ordinaryFree,
            curriculumPrice: this.form.price1,
            videoFile: this.form.videoFile,
            foodId: this.form.foodId,
            curriculumTypePriceList: list
          }

          this.loading = true
          updateSampleVideo(data).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
              this.$emit('confirm')
            }, 500)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    loadData() {
      preSampleVideo(this.id).then(res => {
        this.form = res.data

        this.$set(this.form, 'price1', this.form.curriculumPrice)
        this.$set(this.form, 'price2', undefined)
        this.$set(this.form, 'price3', undefined)

        if (this.form.curriculumTypePriceList)
          this.form.curriculumTypePriceList.map(item => {
            if (item.memberType == '1')
              this.form.price2 = item.price
            if (item.memberType == '2')
              this.form.price3 = item.price
          })

        this.foodTypeName = res.data.foodName

        let file = JSON.parse(this.form.videoFile)
        this.fileList = []
        this.fileList.push({
          name: file.originalName,
          url: file.link
        })
      })
    },
    open(id) {
      this.id = id
      this.loadData()
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
      this.foodTypeName = null
      this.fileList = []
      this.$refs.form.resetFields()
    },
    handleVideoSuccess(res, file) {
      this.fileList = []
      this.fileList.push({
        name: res.data.originalName,
        url: res.data.link
      })
      this.form.videoFile = JSON.stringify(res.data)
      this.$refs.form.validateField('video')
    },
    handleVideoRemove() {
      this.form.videoFile = null
      this.$refs.form.validateField('video')
    },
    beforeVideoUpload(file) {
      if (['video/mp4', 'video/ogg', 'video/flv', 'video/avi', 'video/wmv', 'video/rmvb'].indexOf(file.type) === -1) {
        this.$message.error('请上传正确的视频格式')
        return false
      }
      return true
    }
  }
}
</script>

<style lang='scss' scoped>
.select-tree {
  .el-select-dropdown__item {
    padding: 0 10px;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background: transparent;
  }

  .el-tree-node__content {
    position: relative;
  }

  .expanded.el-tree-node__expand-icon.el-icon-arrow-down {
    position: absolute;
    top: 0;
    right: 0;
  }

  .el-tree-node__expand-icon {
    position: absolute;
    top: 0;
    right: 0;
  }

  .el-tree-node__expand-icon.expanded {
    transform: rotate(-180deg);
  }
}

.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 20%;

  .item {
    width: 70%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.rg1 {
  width: 118px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}
</style>
