<template>
  <div class='wrapper'>
    <el-dialog
      title='查看'
      :visible.sync='dialogVisible'
      v-loading='loading'
      width='60%'>
      <el-form :model='form' ref='form' label-width='200px' label-position='left' size='small'
               class='dialog-form col-18'>
        <el-form-item label='课程名称：' class='item'><p>{{ form.curriculumName }}</p></el-form-item>
        <el-form-item label='适用范围：' class='item'><p>{{ form.scopeOfApplicationNames }}</p></el-form-item>
        <el-form-item label='适用样品：' class='item'><p>{{ form.sampleNames }}</p></el-form-item>
        <el-form-item label='非会员用户是否免费：' class='item'><p>{{ form.memberFreeLabel }}</p></el-form-item>
        <el-form-item label='课程价格（元）：' v-if="form.memberFreeLabel==='否'" class='item'><p>{{ form.curriculumPrice }}</p>
        </el-form-item>
        <el-form-item label='会员用户是否免费：' class='item'><p>{{ form.ordinaryFreeLabel }}</p></el-form-item>
        <el-form-item label='VIP用户课程价格（元）：' v-if="form.memberFreeLabel==='否'" class='item'><p>{{ price2 }}</p>
        </el-form-item>
        <el-form-item label='SVIP用户课程价格（元）：' v-if="form.memberFreeLabel==='否'" class='item'><p>{{ price3 }}</p>
        </el-form-item>
        <el-form-item label='上传视频：' class='item'><p>{{ videoName }}</p></el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>

import {detailMemberPackage} from '@/api/modules/member'
import {detailSampleVideo} from '@/api/modules/sampleVideo'

export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      price2: null,
      price3: null,
      videoName: null,
      form: {
        id: null,//主键,
        curriculumName: null,//课程名称,
        scopeOfApplicationNames: null,//适用范围,
        sampleNames: null,//样品名称,
        memberFreeLabel: null,//会员是否免费 0 否 1 是,
        ordinaryFreeLabel: null,//非会员是否免费 0 否 1 是,
        curriculumPrice: null,//价格,
        videoFile: null,//视频文件 json格式{\name\,\\,\url\:\\},
        foodId: null,//食品id,
        curriculumTypePriceList: [{
          price: null,//价格,
          memberType: null,//会员类型（0 VIP会员 1 SVIP会员）,
          memberTypeName: null//会员类型（0 VIP会员 1 SVIP会员）
        }]
      }
    }
  },
  methods: {
    loadData() {
      detailSampleVideo(this.id).then(res => {
        this.form = res.data
        if (this.form.curriculumTypePriceList)
          this.form.curriculumTypePriceList.map(item => {
            if (item.memberType == '1')
              this.price2 = item.price
            if (item.memberType == '2')
              this.price3 = item.price
          })

        // this.foodTypeName = data.name

        let file = JSON.parse(this.form.videoFile)
        this.videoName = file.originalName
      })
    },
    open(id) {
      this.id = id
      this.form = {
        statusLabel: null,//状态 0 待上架1 已上架
        memberMealName: null,//套餐名称
        memberTypeName: null,//会员类型（0 VIP会员 1 SVIP会员）
        scribePrice: null,//划线价格
        mealPrice: null,//套餐价格
        memberDays: null,//会员天数
        newUserLabel: null,//新用户是否优惠显示label
        oldUserLabel: null//老用户是否优惠显示label
      }
      this.dialogVisible = true
      this.loadData()
    },
    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 25%;

  .item {
    margin-bottom: 0;
  }

  .sub {
    font-weight: bold;
    color: #333333;
    font-size: 16px;
    margin: 20px 0 10px 0;
  }
}

</style>
