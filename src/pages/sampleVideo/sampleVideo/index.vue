<template>
  <div class='wrapper'>
    <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot='search'>
        <el-form :inline='true' :model='form'>
          <el-form-item label='适用范围' class='ml20'>
            <treeselect
              ref='select'
              v-model='form.foodId'
              :options='treeCate'
              :clearable='false'
              placeholder='请选择'
              :searchable='false'>
            </treeselect>
          </el-form-item>
          <el-form-item label='课程名称' class='ml20'>
            <el-input v-model='form.curriculumName'></el-input>
          </el-form-item>
          <el-form-item label='适用样品' class='ml20'>
            <el-input v-model='form.sampleNames'></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot='bottom-options'>
        <el-button type='primary' size='mini' @click='handleInsert'>新增</el-button>
      </div>
      <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
        <div slot='options' slot-scope='scope'>
          <el-button type='text' size='mini' @click='handleView(scope.row)'>查看</el-button>
          <el-button type='text' size='mini' @click='handleEdit(scope.row)'>编辑</el-button>
          <el-button type='text' size='mini' @click='handleDel(scope.row)'>删除</el-button>
        </div>
      </simple-table>
    </simple-list-page>
    <detail-dialog ref='detail' />
    <insert-dialog ref='insert' @confirm='handleQuery' />
    <edit-dialog ref='edit' @confirm='handleQuery' />
  </div>
</template>

<script>
import DetailDialog from './components/detail'
import InsertDialog from './components/insert'
import EditDialog from './components/edit'
import {treeFoodCategory} from '@/api/modules/foodCategory'
import {delSampleVideo, pageSampleVideo} from '@/api/modules/sampleVideo'

// import the component
import Treeselect from '@riophae/vue-treeselect'
// import the styles
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  components: {DetailDialog, InsertDialog, EditDialog, Treeselect},
  data() {
    return {
      treeCate: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      form: {
        curriculumName: null,//课程名称
        sampleNames: null,//样品名称
        foodId: null//食品id
      },
      columns: [
        {
          label: '课程名称',
          prop: 'curriculumName'
        },
        {
          label: '适用范围',
          prop: 'scopeOfApplicationNames'
        },
        {
          label: '适用样品',
          prop: 'sampleNames'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        }
        // {
        //   label: '是否付费',
        //   prop: 'col4'
        // }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  created() {
    this.firstlyCategory()
  },
  methods: {
    firstlyCategory() {
      treeFoodCategory().then(req => {
        this.treeCate = req.data
        console.log('-----------')
        this.treeCate = this.formatList(this.treeCate)
        console.log(this.treeCate)
      })
    },
    formatList(list) {
      let l = []
      list.map(item => {
        let a = {
          id: item.id,
          label: item.name
        }
        if (item.children && item.children.length > 0) {
          a.children = this.formatList(item.children)
        }
        l.push(a)
      })
      return l
    },
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {
        curriculumName: null,//课程名称
        sampleNames: null,//样品名称
        foodId: null//食品id
      }
      this.handleQuery()
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageSampleVideo(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleInsert() {
      this.$refs.insert.open()
    },
    handleView(row) {
      this.$refs.detail.open(row.id)
    },
    handleEdit(row) {
      this.$refs.edit.open(row.id)
    },
    handleDel(row) {
      this.$confirm('确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        delSampleVideo(row.id).then(res => {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.loadData()
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.select-tree {
  .el-select-dropdown__item {
    padding: 0 10px;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background: transparent;
  }

  .el-tree-node__content {
    position: relative;
  }

  .expanded.el-tree-node__expand-icon.el-icon-arrow-down {
    position: absolute;
    top: 0;
    right: 0;
  }

  .el-tree-node__expand-icon {
    position: absolute;
    top: 0;
    right: 0;
  }

  .el-tree-node__expand-icon.expanded {
    transform: rotate(-180deg);
  }
}

::v-deep {
  .vue-treeselect__control {
    width: 217px;
    height: 40px;
  }
}
</style>
