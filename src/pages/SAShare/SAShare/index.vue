<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form' >
        <el-form-item label='用户昵称'>
          <el-input v-model='form.nickName'></el-input>
        </el-form-item>
        <el-form-item label='真实姓名'>
          <el-input v-model='form.realName'></el-input>
        </el-form-item>
        <el-form-item label='联系方式'>
          <el-input v-model='form.telephone'></el-input>
        </el-form-item>
        <el-form-item label='审核状态'>
          <el-select v-model='form.checkStatus' placeholder='请选择'>
            <el-option
              v-for='item in optionStatus'
              :key='item.value'
              :label='item.label'
              :value='item.value'>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData' :optionsWidth='140'>
      <div slot='checkStatus' slot-scope="scope">
         {{scope.row.checkStatus == 1 ? '审核通过': scope.row.checkStatus == 2 ? '审核不通过' : '待审核'}}
      </div>
      <div slot='upStatus' slot-scope="scope">
         {{scope.row.type == 1 ? '置顶': ''}}
      </div>
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' @click='handleView(scope.row)'>查看详情</el-button>
        <el-button type='text' v-if="scope.row.upStatus!=='1'" size='mini' @click='handleUp(1,scope.row)'>置顶</el-button>
        <el-button type='text' v-if="scope.row.upStatus==='1'" size='mini' @click='handleUp(2,scope.row)'>取消置顶</el-button>
      </div>
    </simple-table>
  </simple-list-page>
</template>

<script>
import {miniList, miniDetail, miniUpdate, buyUserInfo} from '@/api/modules/verify.js'

export default {
  data() {
    return {
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      form: {},
      columns: [
        {label: '标题', prop: 'title'},
        {label: '用户昵称', prop: 'nickName'},
        {label: '真实姓名', prop: 'realName'},
        {label: '联系方式', prop: 'telephone'},
        {label: '发布时间', prop: 'pushTime'},
        {label: '状态', prop: 'checkStatus', slot: 'checkStatus'},
        {label: '排序', prop: 'upStatus', slot: 'upStatus'}
      ],
      optionStatus: [
        {label: '全部', value: null},
        {label: '待审核', value: '1'},
        {label: '审核通过', value: '2'},
        {label: '审核不通过', value: '3'}
      ]
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page={
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    loadData() {
      let data={
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      miniList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleView(scope) {
      this.$router.push({
        path: '/SAShare/SAShare/detail'
      })
    },
    handleUp(type, row){
      let str = ''
      if (type===1){
        str = '确定置顶当前常见问题-问答吗'
      }else{
        str = '确定取消置顶当前常见问题-问答吗'
      }
      this.$confirm(str, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.putTop(type,row)
      }).catch(() =>{
      })
    },
    putTop(type,row){
      QATop({id: row.id,upStatus:type}).then(res => {
          if (+res.code === 200) {
            this.$message.success(res.msg)
            this.loadData()
          } else {
            this.$message.error(res.msg)
          }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
