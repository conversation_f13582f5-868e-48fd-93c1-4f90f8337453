<template>
  <el-card shadow='hover'>
    <div class='page-wrapper'>
      <div class='main'>
        <p class='title'>{{ data.title }}</p>
        <p class='info'>{{ data.time }}<span class='ml10'>{{ data.comment }}个评论</span><span
          class='ml10'>{{ data.collect }}个收藏</span></p>
        <div class='author'>
          <el-avatar class='mr10' :size='20' :src='data.head'></el-avatar>
          {{ data.author }}
        </div>
        <p>{{ data.content }}</p>
        <div class='tags'><label class=' mt10 mr20' v-for='item in listTag'>{{ item }}</label></div>
        <p class='sub'>全部评论（{{ data.comment }}）</p>

        <div class='comment' v-for='item in listCom'>
          <div class='top'>
            <el-avatar class='mr10' :size='40' :src='item.head'></el-avatar>
            <label class='name'>{{ item.nick }}{{ item.isAuthor ? '（作者）' : '' }}</label>
            <label class='time'>{{ item.time }}</label>
          </div>
          <div class='bottom'>
            <p class='content'>{{ item.content }}</p>
            <div class='btns'>
              <el-button class='bt' type='text' size='mini' @click='handleReply'>回复</el-button>
              <el-button class='bt' type='text' size='mini' @click='handleDel'>删除</el-button>
            </div>
          </div>

          <div class='reply' v-if='item.children' v-for='child in item.children'>
            <div class='top'>
              <el-avatar class='mr10' :size='40' :src='child.fromhead'></el-avatar>
              <label class='name'>{{ child.fromnick }}{{ child.fromisAuthor ? '（作者）' : '' }}</label>
              <label>回复</label>
              <el-avatar class='mr10' :size='40' :src='child.tohead'></el-avatar>
              <label class='name'>{{ child.tonick }}{{ child.toisAuthor ? '（作者）' : '' }}</label>
              <label class='time'>{{ child.time }}</label>
            </div>
            <div class='bottom'>
              <p class='content'>{{ child.content }}</p>
              <div class='btns'>
                <el-button class='bt' type='text' size='mini' @click='handleReply'>回复</el-button>
                <el-button class='bt' type='text' size='mini' @click='handleDel'>删除</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-button class='mt20' type='primary' plain round>回复</el-button>
    </div>

    <el-dialog :visible.sync='showReply' width='40%' title='回复'>
      <el-form ref='form' class='form' label-width='70px' label-position='left' size='mini'>
        <el-form-item label='解答' required>
          <el-input type='textarea' v-model='replyContent' :rows='8' />
        </el-form-item>
      </el-form>
      <div slot='footer' class='footer'>
        <el-button size='small' @click='handleClose'>取 消</el-button>
        <el-button class='ml20' type='primary' size='small' @click='handleSubmit'>提 交</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
export default {
  data() {
    return {
      showReply: false,
      curr: null,
      replyContent: '',
      data: {
        title: '谁有平台 蔬菜验收标准和抽样方法',
        time: '发表于2022-03-22 13：25',
        comment: '10',
        collect: '20',
        head: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        author: '七月在夏天',
        content: '最近互联网生鲜平台，比如美团优选、橙心、叮咚买菜，这写平台蔬菜验收标准 和抽样方法是怎么样子，谁有或者谁见过，麻烦分享一下。'
      },
      listTag: ['#食品安全', '#抽样办法'],
      listCom: [
        {
          head: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          nick: '七月在夏天',
          isAuthor: true,
          content: '我也想知道',
          time: '2022-03-21'
        },
        {
          head: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          nick: '七月在夏天',
          isAuthor: false,
          content: '我也想知道',
          time: '2022-03-21',
          children: [
            {
              fromhead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              fromnick: '七月',
              tohead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              tonick: '七月夏天',
              isAuthor: true,
              content: '我也想知道',
              time: '2022-03-21'
            },
            {
              fromhead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              fromnick: '七月',
              tohead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              tonick: '七月夏天',
              isAuthor: false,
              content: '我也想知道',
              time: '2022-03-21'
            },
            {
              fromhead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              fromnick: '七月',
              tohead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              tonick: '七月夏天',
              isAuthor: true,
              content: '我也想知道',
              time: '2022-03-21'
            }]
        },
        {
          head: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          nick: '七月在夏天',
          isAuthor: true,
          content: '我也想知道',
          time: '2022-03-21'
        },
        {
          head: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          nick: '七月在夏天',
          isAuthor: false,
          content: '我也想知道',
          time: '2022-03-21'
        },
        {
          head: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          nick: '七月在夏天',
          isAuthor: true,
          content: '我也想知道',
          time: '2022-03-21',
          children: [
            {
              fromhead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              fromnick: '七月',
              tohead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              tonick: '七月夏天',
              fromisAuthor: true,
              toisAuthor: false,
              content: '我也想知道',
              time: '2022-03-21'
            },
            {
              fromhead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              fromnick: '七月',
              tohead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              tonick: '七月夏天',
              fromisAuthor: false,
              toisAuthor: true,
              content: '我也想知道',
              time: '2022-03-21'
            },
            {
              fromhead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              fromnick: '七月',
              tohead: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              tonick: '七月夏天',
              fromisAuthor: false,
              toisAuthor: false,
              content: '我也想知道',
              time: '2022-03-21'
            }]
        },
        {
          head: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          nick: '七月在夏天',
          isAuthor: false,
          content: '我也想知道',
          time: '2022-03-21'
        },
        {
          head: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          nick: '七月在夏天',
          isAuthor: true,
          content: '我也想知道',
          time: '2022-03-21'
        },
        {
          head: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          nick: '七月在夏天',
          isAuthor: false,
          content: '我也想知道',
          time: '2022-03-21'
        },
        {
          head: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          nick: '七月在夏天',
          isAuthor: true,
          content: '我也想知道',
          time: '2022-03-21'
        },
        {
          head: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          nick: '七月在夏天',
          isAuthor: false,
          content: '我也想知道',
          time: '2022-03-21'
        }
      ]
    }
  },
  methods: {
    handleReply(row) {
      this.curr = row
      this.showReply = true
    },
    handleDel() {
    },
    handleClose() {
      this.showReply = false
      this.curr = null
      this.replyContent = null
    },
    handleSubmit() {
      // changeStatus({id: this.curr.id, checkStatus: 2, rejectReason: this.reason}).then(res => {
      this.handleClose()
      //   this.loadData(this.page)
      // }).finally(() => {
      //   this.loading = false
      // })
    }
  }
}
</script>

<style lang='scss' scoped>
.page-wrapper {

  display: flex;
  align-items: flex-start;

  .main {
    width: 56%;
    margin-left: 24%;
    margin-top: 30px;
    margin-right: auto;
    font-size: 14px;

    .title {
      font-size: 24px;
      line-height: 36px;
    }

    .info {
      color: #666666;
      font-size: 14px;
      line-height: 24px;
      margin-top: 5px;
    }

    .author {
      height: 40px;
      display: flex;
      align-items: center;
      font-size: 12px;
    }

    .tags {
      display: flex;
      flex-wrap: wrap;
    }

    .sub {
      font-size: 18px;
      font-weight: bold;
      margin: 20px 0;
    }
  }
}

.comment {
  .top {
    display: flex;
    align-items: center;
    height: 40px;

    .name {
      margin-right: auto;
    }

    .time {
      width: 15%;
      color: #aaaaaa;
      text-align: right;
    }
  }

  .bottom {
    display: flex;
    align-items: flex-start;
    line-height: 20px;
    margin-top: 10px;
    margin-bottom: 10px;

    .content {
      width: 80%;
      padding-left: 50px;
      margin-right: auto;
    }

    .btns {
      width: 15%;
      display: flex;
      align-items: center;
      height: 40px;
      text-align: right;
      justify-content: flex-end;

      .bt {
        margin-left: 20px;
      }
    }
  }
}

.reply {
  .top {
    display: flex;
    align-items: center;
    padding-left: 50px;
    height: 40px;

    .name {
      margin-right: 10px;
    }

    .time {
      width: 15%;
      margin-left: auto;
      color: #aaaaaa;
      text-align: right;
    }
  }

  .bottom {
    padding-left: 50px;
    display: flex;
    align-items: flex-start;
    line-height: 20px;
    margin-top: 10px;
    margin-bottom: 10px;

    .content {
      width: 80%;
      padding-left: 50px;
      margin-right: auto;
    }

    .btns {
      width: 15%;
      display: flex;
      align-items: center;
      height: 40px;
      text-align: right;
      justify-content: flex-end;

      .bt {
        margin-left: 20px;
      }
    }
  }
}

.form {
   margin: 20px 50px;
 }
</style>
