<template>
  <div class='wrapper'>
    <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot="search">
        <el-form :inline="true" :model="form">
          <el-form-item label="发布用户：">
            <el-input v-model="form.nickName"></el-input>
          </el-form-item>
          <el-form-item label="真实姓名：">
            <el-input v-model="form.userName"></el-input>
          </el-form-item>
          <el-form-item label="联系方式：">
            <el-input v-model="form.phone"></el-input>
          </el-form-item>
          <el-form-item label="话题名称：">
            <el-input v-model="form.topicName"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot='bottom-options'>
        <el-button type='primary' size='mini' @click='handleInsert'>新增话题</el-button>
      </div>
      <simple-table :columns="columns" :data="list" :page="page" @load="loadData" :optionsWidth="240">
        <div slot='topping' slot-scope="scope">
          {{scope.row.topping == 1 ? '已置顶': ''}}
        </div>
        <div slot="options" slot-scope='scope'>
          <el-button type="text" size="mini" icon="el-icon-view" @click="handleView(scope.row)">查看详情</el-button>
          <el-button type="text" size="mini" icon="el-icon-view" @click="deleteClick(scope.row)">删除</el-button>
          <el-button type="text" size="mini" icon="el-icon-view" @click="topClick(scope.row)">{{scope.row.topping == 1 ? '取消置顶' : '置顶'}}</el-button>
        </div>
      </simple-table>
    </simple-list-page>
    <detail-dialog ref='detail' />
    <insert-dialog ref='insert' @confirm='handleQuery'/>
  </div>
</template>

<script>
import DetailDialog from './components/detail'
import InsertDialog from './components/insert'
import {topicList,topping,deleteTopping} from '@/api/modules/verify.js'

export default {
  components: {DetailDialog, InsertDialog},
  data() {
    return {
      form: {
        nickName: null,
        userName: null,
        phone: null,
        topicName: null
      },
      columns: [
        {
          label: '话题名称',
          prop: 'topicName',
        },
        {
          label: '发布用户昵称',
          prop: 'nickName',
        },
        {
          label: '发布用户真实姓名',
          prop: 'userName',
        },
        {
          label: '联系方式',
          prop: 'phone',
        },
        {
          label: '发布时间',
          prop: 'createTime',
        },
        {
          label: '参与人数',
          prop: 'discussCount',
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form= {
        nickName: null,
        userName: null,
        phone: null,
        topicName: null
      },
      this.handleQuery()
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      topicList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    topClick(row){
      topping(row.id, row.topping).then(res => {
        this.$message({
          type: 'success',
          message: '操作成功'
        })
        this.loadData()
      })
    },
    handleInsert() {
      this.$refs.insert.open()
    },
    deleteClick(row) {
      this.$confirm('确定删除当前话题吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteTopping(row.id).then(res => {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.loadData()
        })
      })

    },
    handleView(row) {
      this.$refs.detail.open(row.id)
    },
    handleEdit(row) {
      this.$refs.edit.open(row.id)
    },
    handleDel(row) {
      this.$confirm('确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        delMemberPackage(row.id).then(res => {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.loadData()
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang='scss' scoped></style>
