<template>
  <div class='wrapper'>
    <el-dialog
      title='新增话题'
      :visible.sync='dialogVisible'
      width='60%'
      @close='close'>
      <el-form :model='form' :rules='rules' ref='form' label-width='180px' label-position='left' size='small'
               class='dialog-form'>
        <el-form-item class='item' label='话题名称：' prop='topicName'>
          <el-input v-model='form.topicName'></el-input>
        </el-form-item>

      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {topicAdd} from '@/api/modules/verify'

export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      status: true,
      form: {
        topicName: null,//话题名称
      },
      rules: {
        topicName: [
          {required: true, message: '请输入话题名称', trigger: 'blur'}
        ]
      },
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          topicAdd(this.form).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
              this.$emit('confirm')
            }, 500)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    open() {
      this.form = {
        topicName: null,//套餐名称
      }
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 20%;

  .item {
    width: 70%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.rg1 {
  width: 118px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg2 {
  width: 62px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg-content {
  display: flex;
  align-items: center;
  margin-top: 32px;
}
</style>
