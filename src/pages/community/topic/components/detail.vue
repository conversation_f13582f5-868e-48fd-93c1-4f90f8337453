<template>
  <div class='wrapper'>
    <el-dialog
      title='话题详情'
      :visible.sync='dialogVisible'
      width='60%'
      @close='close'>
      <simple-detail-panel :columns='columns' :data='detail' :labelWidth='100'></simple-detail-panel>
      <simple-line-card header="参与用户">
        <simple-table :columns="listColumns" :data="list" :page="page" @load="loadData" :showOptions="false"></simple-table>
      </simple-line-card>
      <div slot='footer' class='dialog-footer'>
        <el-button type='primary' size='small' @click='close'>确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {topicDetailList, topicDetail} from '@/api/modules/verify'

export default {
  data() {
    return {
      id: '',
      dialogVisible: false,
      loading: false,
      columns: [
        {
          label: '话题名称：',
          prop: 'topicName'
        },
        {
          label: '发布用户昵称：',
          prop: 'nickName'
        },
        {
          label: '发布用户真实姓名：',
          prop: 'realName'
        },
        {
          label: '账号：',
          prop: 'username'
        },
        {
          label: '联系方式：',
          prop: 'telephone'
        },
        {
          label: '发布时间：',
          prop: 'createTime'
        }
      ],
      listColumns:[
        {
          label: '参与用户昵称',
          prop: 'nickName'
        },
        {
          label: '真实姓名',
          prop: 'realName'
        },
        {
          label: '联系方式',
          prop: 'telephone'
        },
        {
          label: '社区发布内容标题',
          prop: 'title'
        },
        {
          label: '标题类型',
          prop: 'typeLabel'
        },
        {
          label: '发布时间',
          prop: 'createTime'
        }
      ],
      list: [],
      detail:{},
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  methods: {
    loadData() {
      this.getList()
      this.getDetail()
    },
    getList(){
      let data = {
        id: this.id,
        current: this.page.current,
        size: this.page.size
      }
      topicDetailList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    getDetail(){
      topicDetail(this.id).then(res => {
        this.detail = res.data
      })
    },
    open(id) {
      this.id = id
      this.dialogVisible = true
      this.loadData()
    },
    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 20%;

  .item {
    width: 70%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.rg1 {
  width: 118px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg2 {
  width: 62px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg-content {
  display: flex;
  align-items: center;
  margin-top: 32px;
}
</style>
