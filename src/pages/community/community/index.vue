<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot="search">
      <el-form :inline="true" :model="form">
        <el-form-item label="发布用户">
          <el-input v-model="form.nickName"></el-input>
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input v-model="form.realName"></el-input>
        </el-form-item>
        <el-form-item label="联系方式">
          <el-input v-model="form.phone"></el-input>
        </el-form-item>
        <el-form-item label="联系方式">
          <el-date-picker v-model="form.phone1" type="week" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择周">
          </el-date-picker>
        </el-form-item>

        <el-form-item label='审核状态'>
          <el-select v-model='form.checkStatus' placeholder='请选择'>
            <el-option v-for='item in optionStatus' :key='item.value' :label='item.label' :value='item.value'>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <simple-table :columns="columns" :data="list" :page="page" @load="loadData">
      <div slot='topping' slot-scope="scope">
        {{scope.row.topping == 1 ? '已置顶': ''}}
      </div>
      <div slot="options" slot-scope='scope'>
        <el-button type="text" size="mini" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
        <el-button type="text" size="mini" icon="el-icon-view" @click="topClick(scope.row)">{{scope.row.topping == 1 ? '取消置顶' : '置顶'}}</el-button>
      </div>
    </simple-table>
  </simple-list-page>
</template>

<script>
import { communityList, comTopping } from '@/api/modules/verify.js'
export default {
  data() {
    return {
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      form: {},
      optionStatus: [
        { label: '全部', value: null },
        { label: '待审核', value: '1' },
        { label: '审核通过', value: '2' },
        { label: '审核不通过', value: '3' }
      ],
      columns: [
        {
          label: '标题',
          prop: 'title',
        },
        {
          label: '话题',
          prop: 'topicNames',
        },
        {
          label: '类型',
          prop: 'typeLabel',
        },
        {
          label: '用户昵称',
          prop: 'nickName',
        },
        {
          label: '真实姓名',
          prop: 'realName',
        },
        {
          label: '联系方式',
          prop: 'phone',
        },
        {
          label: '发布时间',
          prop: 'createTime',
        },
        {
          label: '评论',
          prop: 'commentCount',
        },
        {
          label: '点赞',
          prop: 'likesCount',
        },
        {
          label: '收藏',
          prop: 'collectionCount',
        },
        {
          label: '分享',
          prop: 'shareCount',
        },
        {
          label: '状态',
          prop: 'statusLabel',

        },
        {
          label: '排序',
          prop: 'topping',
          slot: 'topping'
        },
      ],
      list: [],
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      communityList(data).then(res => {

        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleView(val) {
      this.$router.push({
        path: '/community/community/detail',
        query: {
          id: val.id
        }
      })
    },
    topClick(row) {

      comTopping(row.id, row.topping).then(res => {
        this.$message({
          type: 'success',
          message: '操作成功'
        })
        this.loadData()
      })
    }
  },
}
</script>

<style lang="scss" scoped></style>
