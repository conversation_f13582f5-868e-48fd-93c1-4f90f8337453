<template>
  <simple-page title='查看社区详情' @back='handleBack'>
    <div class='page-wrapper'>
      <div class='main'>
        <p class='title'>{{ data.title }}</p>
        <div style="display:flex; justify-content: space-between;">

          <p class='info'>发表于{{ data.createTime }}<span class='ml10'>{{ data.commentCount }}个评论</span><span class='ml10'>{{ data.collectionCount }}个收藏</span></p>

          <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
            <el-tab-pane label="查看点赞用户" name="1"></el-tab-pane>
            <el-tab-pane label="查看收藏用户" name="2"></el-tab-pane>
            <el-tab-pane label="查看分享用户" name="3"></el-tab-pane>
          </el-tabs>
        </div>
        <div class='author'>
          <el-avatar class='mr10' :size='20' :src='data.avatar'></el-avatar>
          <div>{{ data.nickName }}</div>
        </div>
        <div class="content">{{ data.content }}</div>
        <div class="SongList" v-if="imgList">
          <div class="covers">
            <div class="cover" v-for="img in imgList" :key='img'>
              <el-image style="width:80px;height:80px;" :src="img" :preview-src-list="[img]">
              </el-image>
            </div>
          </div>
        </div>
        <div class=' mt10 mr20'>
          <label class=' tags' v-for='item in listTag' :key="item.topicName">#{{ item.topicName }}</label>
        </div>
        <p class='sub'>全部评论（{{ data.commentCount }}）</p>
        <div class="line"></div>

        <div class='comment' v-for='(item ,index) in listCom' :key="index">
          <div style="display:flex;flex-direction: row;">
            <el-avatar class='mr10' :size='40' :src='item.avatar'></el-avatar>
            <div class="right">
              <div class="rightTop">
                <label class='name'>{{ item.nickName }}</label>
                <label class='time'>{{ item.createTime }}</label>
              </div>
              <div class="rightBottom">
                <p class='content'>{{ item.commentContext }}</p>
                <div class='btns' style="widht:100%">
                  <el-button class='bt textRed' type='text' size='mini' @click='handleDel(item)'>删除</el-button>
                  <el-button class='bt btnBlue' type='text' size='mini' @click='handleReply(item)'>查看点赞用户</el-button>
                </div>
              </div>
            </div>
          </div>
          <div class='reply' v-if='item.commentPage.records' v-for='(child,idx) in getCommentArr(item.commentPage.records)' :key="idx">
            <div class='top'>
              <el-avatar class='mr10' :size='20' :src='child.avatar'></el-avatar>
              <label class='name'>{{ child.nickName }}</label>
              <label>回复</label>
              <el-avatar style="margin-left:10px" class='mr10' :size='20' :src='child.receiveAvatar'></el-avatar>
              <label class='name'>{{ child.receiveNickName }}</label>
              <label class='time'>{{ child.createTime }}</label>
            </div>
            <div class='bottom'>
              <p class='content'>{{ child.commentContext }}</p>
              <div class='btns' style="widht:100%">
                <el-button class='bt textRed' type='text' size='mini' @click='handleDel(child)'>删除</el-button>
                <el-button class='bt btnBlue' type='text' size='mini' @click='handleReply(child)'>查看点赞用户</el-button>
              </div>
            </div>
          </div>
          <div class="more" v-if="+item.childComments > 2" @click="checkChildMordClick(item)">
            查看全部{{item.childComments}}条内容 >
          </div>
          <div style="margin-top:3px;" class="line"></div>
        </div>

        <div class="more" v-if="listCom.length>0 && !isHidenMore" style="margin:0 auto; margin-top:10px; " @click="checkMoreClick">
          查看更多评论 >
        </div>
      </div>
    </div>

    <el-dialog :visible.sync='showUsers' v-loading="loading" width='30%' :title='checkTitle'>
      <div class='comment' style="border-bottom: 1px solid #dfe3e8; padding:10px;" v-for='(item,index) in showUserList' :key="index">
        <div style="display:flex;flex-direction: row;">
          <el-avatar class='mr10' :size='40' :src='item.avatar'></el-avatar>
          <div class="right">
            <div class="rightTop" style="flex-direction: row;">
              <label class='name'>{{ item.receiveUserName }} {{  typeTitle  }} {{ item.sendUserName }}的动态</label>
            </div>
            <label style="font-size: 14px;font-weight: 400;color: #C0C4CC;line-height: 20px;">{{ item.createTime }}</label>
          </div>
        </div>
      </div>
      <div slot='footer' class='footer'>
        <el-button class='ml20' type='primary' size='small' @click='showUsers=false'>确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync='childMore' v-loading="loading" width='60%' :title='checkTitle'>
      <div style="padding:10px;">
        <div class='comment' v-for='(child,idx) in childMoreList' :key="idx">
          <div style="display:flex;flex-direction: row;">
            <el-avatar class='mr10' :size='40' :src='child.avatar'></el-avatar>
            <div class="right">
              <div class="rightTop">
                <label class='name'>{{ child.nickName }}</label>
                <label class='time'>{{ child.createTime }}</label>
              </div>
              <div class="rightBottom">
                <p class='content'><span style="color:#BFC3CB;">回复：{{child.receiveNickName}}</span> {{ child.commentContext }}</p>
                <div class='btns' style="widht:100%">
                  <el-button class='bt textRed' type='text' size='mini' @click='handleDel(child)'>删除</el-button>
                  <el-button class='bt btnBlue' type='text' size='mini' @click='handleReply(child)'>查看点赞用户</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </simple-page>
</template>

<script>
import { collectionList, communityDetail, deleteCommunityComment, likesCommentList, likesList, pageAllCommunityCommentPC, shareList } from '@/api/modules/verify.js'

export default {
  data() {
    return {
      imgList: [],
      childMore: false,
      childMoreList: [],
      isHidenMore: false,
      checkTitle: '查看点赞用户',
      typeTitle: '赞了',
      loading: false,
      id: '',
      replyType: 1,
      showUsers: false,
      curr: null,
      replyContent: '',
      activeName: 'first',
      data: {
      },
      listTag: [],
      listCom: [],
      page: {
        current: 0,
        size: 10
      },
      showUserList: []
    }
  },
  mounted() {
    this.id = this.$route.query.id
    this.loadData()
  },
  methods: {
    loadData() {
      this.imgList = []
      communityDetail(this.id).then(res => {

        this.data = res.data
        this.listTag = res.data.topicList
        this.imgList = res.data.fileUrls || []
        this.checkMoreClick()
      })
    },
    getCommentArr(arr = []) {
      if (arr.length <= 2) {
        return arr
      }
      return arr.slice(0, 2)
    },
    handleReply(row) {
      this.checkTitle = '查看点赞用户'
      this.typeTitle = '赞了'
      likesCommentList({ commentId: row.id }).then(res => {
        this.showUserList = res.data
        if (res.data.length > 0) {
          this.showUsers = true
        } else {
          this.$message.error('暂无相关数据')
        }
      })

    },
    checkMoreClick() {
      // 查看更多主评论
      this.page.current++
      let param = this.page
      param.communityId = this.id
      pageAllCommunityCommentPC(param).then(res => {
        this.listCom = this.listCom.concat(res.data.records)
        this.isHidenMore = this.listCom.length >= +res.data.total ? true : false
      })
    },
    checkChildMordClick(val) {
      this.childMore = true
      this.checkTitle = '查看全部评论'
      this.childMoreList = val.commentPage.records
    },
    handleDel(row) {
      this.$confirm('确定删除当前评论吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {

        deleteCommunityComment(row.id).then(res => {
          if (+res.code === 200) {
            this.listCom = []
            this.childMore = false
            this.$message.success(res.msg)
            this.loadData()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {

      })
    },
    handleClose() {
      this.showReply = false
      this.curr = null
      this.replyContent = null
      this.replyType = null
    },
    handleClick(tab, event) {
      if (tab.name == 1) {
        this.checkTitle = '查看点赞用户'
        this.typeTitle = '赞了'
        likesList({ communityId: this.id }).then(res => {
          this.showUserList = res.data
          if (res.data.length > 0) {
            this.showUsers = true
          } else {
            this.$message.error('暂无相关数据')
          }
        })
      } else if (tab.name == 2) {
        this.checkTitle = '查看收藏用户'
        this.typeTitle = '收藏了'
        collectionList({ communityId: this.id }).then(res => {
          this.showUserList = res.data
          if (res.data.length > 0) {
            this.showUsers = true
          } else {
            this.$message.error('暂无相关数据')
          }
        })
      } else {
        this.checkTitle = '查看分享用户'
        this.typeTitle = '分享了'
        shareList({ communityId: this.id }).then(res => {
          this.showUserList = res.data
          if (res.data.length > 0) {
            this.showUsers = true
          } else {
            this.$message.error('暂无相关数据')
          }
        })
      }

    },
    handleBack() {
    },
    handleSubmit() {
      let data = {
        id: this.curr ? this.curr.id : null,
        qaId: this.id,
        commentType: this.replyType,
        reply: this.replyContent
      }
      QAComment(data).then(res => {
        if (res.code == 200) {
          this.childMore = false
          this.$message.success('操作成功')
          this.handleClose()
          this.loadData()
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.page-wrapper {
  .main {
    width: 56%;
    margin-left: 24%;
    margin-top: 30px;
    margin-right: auto;
    font-size: 14px;

    .title {
      height: 28px;
      font-size: 20px;
      font-weight: 500;
      color: #131414;
    }

    .info {
      height: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #c0c4cc;
      line-height: 20px;
      margin-top: 8px;
    }

    .author {
      display: flex;
      flex-direction: row;
      margin-top: 8px;
      height: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #409eff;
      line-height: 20px;
    }

    .tags {
      width: auto;
      padding: 5px;
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
    }
    .content {
      margin: 20px 0;
      font-size: 14px;
      font-weight: 400;
      color: #131414;
      line-height: 20px;
    }
    .sub {
      margin: 20px 0;
      font-size: 16px;
      font-weight: 500;
      color: #131414;
      line-height: 24px;
    }
  }
}

.comment {
  .right {
    width: 100%;
  }
  .rightTop {
    display: flex;
    justify-content: space-between;
    .name {
      font-size: 14px;
      font-weight: 400;
      color: #606266;
      line-height: 20px;
    }
    .time {
      font-size: 14px;
      font-weight: 400;
      color: #c0c4cc;
      line-height: 20px;
    }
  }
  .rightBottom {
    .content {
      font-size: 16px;
      font-weight: 400;
      color: #131414;
      line-height: 22px;
      margin: 4px 0;
    }
    .btns {
      display: flex;
      flex-direction: row-reverse;
      .bt {
        margin: 0 5px;
      }
    }
  }
}

.more {
  padding: 3px;
  cursor: pointer;
  margin-left: 50px;
  text-align: center;
  width: 160px;
  height: 28px;
  background: #f5f5f5;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}
.reply {
  padding-left: 50px;
  .top {
    display: flex;
    align-items: center;
    height: 40px;

    .name {
      margin-right: 10px;
      font-size: 14px;
      font-weight: 400;
      color: #606266;
      line-height: 20px;
    }

    .time {
      width: 15%;
      margin-left: auto;
      color: #c0c4cc;
      text-align: right;
    }
  }

  .bottom {
    padding-left: 50px;
    margin-top: 10px;
    margin-bottom: 10px;

    .content {
      font-size: 16px;
      font-weight: 400;
      color: #131414;
      line-height: 22px;
      margin: 4px 0;
    }
    .btns {
      display: flex;
      flex-direction: row-reverse;
      .bt {
        margin: 0 5px;
      }
    }
  }
}

.form {
  margin: 20px 50px;
}
.line {
  border-bottom: 1px solid #e6ebf5;
  height: 1px;
  width: 100%;
  margin-bottom: 10px;
}
.textRed {
  color: #f56c6c;
}
.btnBlue {
  color: #409eff;
}
::v-deep {
  .el-tabs--card > .el-tabs__header .el-tabs__nav {
    border: none;
  }
  .el-tabs--card > .el-tabs__header {
    border-bottom: none;
  }
  .el-tabs--card > .el-tabs__header .el-tabs__item {
    border: none;
    border-right: 1px solid #dfe3e8;
  }
}
.SongList {
  width: 30%;
}
.covers {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.cover {
  display: flex;
  justify-content: center;
  width: 33%;
  padding: 5px;
}
</style>
