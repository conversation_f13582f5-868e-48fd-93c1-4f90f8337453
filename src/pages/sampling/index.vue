<template>
  <div class="wrapper wrapper-a">
    <simple-list-page @query="handleQuery" @reset="handleReset">
      <div slot="top-options">
        <el-form :inline="true" :model="form" size="small">
          <el-form-item label="区域省份：">
            <el-select
              v-model="form.enterpriseProvince"
              filterable
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in provinceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="检测结论：">
            <el-select v-model="form.testResult" filterable clearable>
              <el-option
                v-for="item in options7"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="食品大类：">
            <el-select v-model="form.firstCategory" filterable clearable>
              <el-option
                v-for="item in options2"
                :key="item.cateName"
                :label="item.cateName"
                :value="item.cateName"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="生产企业名称：">
            <el-input
              v-model="form.productionName"
              placeholder="请输入"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="样品名称：">
            <el-input
              v-model="form.foodName"
              placeholder="请输入"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="检测项目：">
            <el-input
              v-model="form.bhgxm"
              placeholder="请输入"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="数据年份：">
            <el-select v-model="form.year" filterable clearable>
              <el-option
                v-for="item in generateYearOptions(2023)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <simple-table
        :columns="columns"
        :data="list"
        :page="page"
        :optionsWidth="200"
        @load="loadData"
        :showOptions="false"
        :loading="loading"
        layoutPagination="sizes, prev, pager, next"
        lastPageHidden
      >
        <!-- 自定义检测结论展示内容 -->
        <div slot="testResult" slot-scope="{ row }">
          {{
            row.testResult == 0 ? "不合格" : row.testResult === 1 ? "合格" : ""
          }}
        </div>
      </simple-table>
    </simple-list-page>
  </div>
</template>

<script>
import {
  selectXrSamList,
  findTreeListAll,
  queryEnterprisesList,
  querySampleList,
  queryDetectionList,
} from "@/api/modules/entrust.js";
import { provinces, isValidProvince } from "@/config/provinces.js";
import dayjs from "dayjs";
export default {
  data() {
    return {
      form: {
        year: 2024,
        testResult: null,
      },
      columns: [
        {
          label: "省份",
          prop: "enterpriseProvince",
        },
        {
          label: "食品大类",
          prop: "firstCategory",
        },
        {
          label: "样品名称",
          prop: "foodName",
        },
        {
          label: "生产企业名称",
          prop: "productionName",
        },
        {
          label: "被抽样单位名称",
          prop: "enterpriseName",
        },
        {
          label: "检测项目",
          prop: "bhgxm",
        },
        {
          label: "检测结论",
          prop: "testResult",
          slot: "testResult",
        },
        {
          label: "任务来源",
          prop: "taskSource",
        },
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10,
      },
      loading: false,
      provinceOptions: provinces, // 使用公共配置中的省份数据
      options1: [], // 区域省份枚举值，从API获取的备用数据
      options2: [], // 食品大类枚举值
      options3: ["国抽", "省抽", "市抽", "县抽"], // 任务级别枚举值
      options4: [], // 企业名称枚举值
      options5: [], // 样品名称枚举值
      options6: [], // 检测项目枚举值
      options7: [
        { label: "合格", value: 1 },
        { label: "不合格", value: 0 },
      ], // 检测结论枚举值
    };
  },
  mounted() {
    this.loadData();
    this.findTreeListAll();
    // this.findEnterprisesList()
    // this.findSampleList()
    // this.findDetectionList()
  },
  methods: {
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10,
      };
      this.loadData();
    },
    handleReset() {
      this.form = {
        year: 2024,
      };
      this.handleQuery();
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size,
      };
      if (
        !isValidProvince(data.enterpriseProvince) &&
        data.enterpriseProvince
      ) {
        return this.$message.error("请输入正确的省份");
      }
      this.loading = true;
      selectXrSamList(data)
        .then((res) => {
          if (res.code !== 200) {
            return this.$message.error(res.msg);
          }
          this.list = res.data.records;
          this.page.total = res.data.total;
        })
        .catch((err) => {
          this.$message.error(err.msg);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 食品分类树型列表查询
    findTreeListAll() {
      findTreeListAll().then((res) => {
        this.options2 = res.data;
      });
    },
    // 企业名称枚举值查询
    findEnterprisesList() {
      queryEnterprisesList().then((res) => {
        this.options4 = res.data;
      });
    },
    // 样品名称枚举值查询
    findSampleList() {
      querySampleList().then((res) => {
        this.options5 = res.data;
      });
    },
    // 检测项目枚举值查询
    findDetectionList() {
      queryDetectionList().then((res) => {
        this.options6 = res.data;
      });
    },
    // 生成年份枚举值，startYear：开始年份
    generateYearOptions(startYear) {
      const currentYear = new Date().getFullYear();
      const options = [];
      for (let year = startYear; year <= currentYear; year++) {
        options.unshift({ label: `${year}年`, value: year });
      }
      return options;
    },
    handleInput(event) {
      if (!this.form.enterpriseProvince && !this.form.firstCategory) {
        this.form.testResult = null;
      }
    },
    handleChange(event) {
      if (!this.form.firstCategory && !this.form.enterpriseProvince) {
        this.form.testResult = null;
      }
    },
  },
};
</script>

<style lang="scss">
.wrapper-a .el-pager .number:last-child {
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 隐藏超出的内容 */
  text-overflow: ellipsis; /* 用省略号表示被隐藏的部分 */
  max-width: 20px; /* 设置最大宽度以限制文本的显示长度 */
}
</style>
