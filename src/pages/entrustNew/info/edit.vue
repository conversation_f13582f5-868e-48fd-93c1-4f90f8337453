<template>
  <div class="wrapper">
    <el-dialog
      title="编辑"
      :visible.sync="dialogVisible"
      width="500px"
      @close="close"
    >
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        label-width="140px"
        label-position="right"
        size="small"
        class="dialog-form"
      >
        <el-form-item class="item" label="任务来源：" prop="taskSource">
          <el-input
            v-model="form.taskSource"
            placeholder="请输入任务来源"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item class="item" label="报送分类A：" prop="newscbname">
          <el-input
            v-model="form.newscbname"
            placeholder="请输入报送分类A"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item class="item" label="报送分类B：" prop="scbname">
          <el-input
            v-model="form.scbname"
            placeholder="请输入报送分类B"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item class="item" label="任务来源省份：">
          <el-select
            v-model="form.taskSourceProvince"
            filterable
            clearable
            placeholder="请选择"
            disabled
          >
            <el-option
              v-for="item in provinceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="item" label="计划批次数量：" prop="planBatchCount">
          <el-input
            v-model.number="form.planBatchCount"
            type="number"
            placeholder="请输入批次数量"
          ></el-input>
          <div class="form-tip">
            计划批次数量只能增加不能减少，当前原始计划批次：{{
              originalBatchCount
            }}
          </div>
        </el-form-item>
        <el-form-item
          class="item"
          label="抽样单编号前缀："
          prop="numberTemplate"
        >
          <el-input
            v-model="form.numberTemplate"
            placeholder="请输入编号前缀"
          ></el-input>
        </el-form-item>
        <el-form-item class="item" label="">
          <div class="rg-content">
            首字母类型+年份+任务来源编号+抽样单位编号 例：DBJ 25 500000 6232 01
            122 抽样队编号可在人员管理模块配置
          </div>
        </el-form-item>
        <el-form-item class="item" label="计划抽样完成日期：">
          <el-date-picker
            v-model="form.planFinishDate"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="close">取 消</el-button>
        <el-button
          type="primary"
          size="small"
          @click="submit"
          :loading="loading"
          >提 交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { selectXrHnyById, updateXrHny } from "@/api/modules/entrust";
import { provinces } from "@/config/provinces.js";

export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      provinceOptions: provinces,
      originalBatchCount: 0,
      form: {
        newscbname: "",
        scbname: "",
        year: "",
        numberTemplate: "",
        planBatchCount: "",
        planFinishDate: "",
        taskSource: "",
      },
      rules: {
        taskSource: [
          { required: true, message: "请输入任务来源", trigger: "blur" },
        ],
        newscbname: [
          { required: true, message: "请输入报送分类A", trigger: "blur" },
        ],
        scbname: [
          { required: true, message: "请输入报送分类B", trigger: "blur" },
        ],
        planBatchCount: [
          { required: true, message: "请输入计划批次数量", trigger: "blur" },
          {
            type: "number",
            message: "计划批次数量必须为数字",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error("计划批次数量必须大于0"));
              } else if (value < this.originalBatchCount) {
                callback(
                  new Error(
                    `计划批次数量不能小于原计划数量 ${this.originalBatchCount}`
                  )
                );
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.id = this.id;
          if (this.form.planBatchCount) {
            this.form.planBatchCount = Number(this.form.planBatchCount);
          }
          this.loading = true;
          updateXrHny(this.form)
            .then((result) => {
              this.$message({
                type: "success",
                message: result.msg,
              });
              setTimeout(() => {
                this.close();
                this.$emit("confirm");
              }, 500);
            })
            .catch((result) => {
              this.$message({
                type: "warning",
                message: result.msg,
              });
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    loadData() {
      selectXrHnyById({ id: this.id })
        .then((res) => {
          if (res.code !== 200) {
            return this.$message.error(res.msg);
          }
          this.form = res.data;
          if (this.form.planBatchCount) {
            this.form.planBatchCount = Number(this.form.planBatchCount);
          }
          this.originalBatchCount = this.form.planBatchCount || 0;
        })
        .catch((err) => {
          this.$message.error(err.msg);
        });
    },
    open(id) {
      this.id = id;
      this.form = {};
      this.dialogVisible = true;
      this.loadData();
    },
    close() {
      this.dialogVisible = false;
      this.$refs.form.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-form {
  margin: 0 20px;

  .item {
    width: 100%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  margin-top: 5px;
}

.rg1 {
  width: 160px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg2 {
  width: 80px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg-content {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
  line-height: 1.2;
}

.img-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 88px;
  height: 88px;
}

.img-uploader:hover {
  border-color: #409eff;
}

.img-uploader-icon {
  font-size: 14px;
  color: #8c939d;
  width: 88px;
  height: 88px;
  line-height: 88px;
  text-align: center;
}

.img {
  width: 88px;
  height: 88px;
  display: block;
}
</style>
