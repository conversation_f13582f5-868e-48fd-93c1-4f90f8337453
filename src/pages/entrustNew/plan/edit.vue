<template>
  <div class="wrapper" v-loading="loading && isEdit">
    <el-form
      :model="form"
      ref="form"
      :rules="rules"
      label-width="180px"
      label-position="right"
      size="small"
      class="dialog-form"
    >
      <el-row>
        <!-- 使用 el-row 包裹 -->
        <el-col :span="12">
          <el-form-item class="item" label="任务来源: " prop="taskSource">
            <el-select
              v-model="form.taskSource"
              filterable
              placeholder="请选择"
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option
                v-for="(item, index) in taskSourceList"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="item" label="报送分类A: " prop="classA">
            <el-select
              v-model="form.classA"
              filterable
              placeholder="请选择"
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option
                v-for="(item, index) in classAList"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!-- 使用 el-row 包裹 -->
        <el-col :span="12">
          <el-form-item class="item" label="报送分类B: " prop="classB">
            <el-select
              v-model="form.classB"
              filterable
              placeholder="请选择"
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option
                v-for="(item, index) in classBList"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="item" label="任务名称: " prop="taskName">
            <el-input
              placeholder="请输入"
              v-model="form.taskName"
              :disabled="isEdit"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item class="item" label="计划抽样完成日期：">
            <el-date-picker
              v-model="form.plannedCompletionTime"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            class="item"
            label="任务执行区域: "
            prop="executionArea"
          >
            <el-input
              placeholder="请输入"
              v-model="form.executionArea"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 抽样环节选择 -->
      <el-row>
        <el-col :span="24">
          <el-form-item class="item" label="选择抽样环节: " prop="link">
            <el-checkbox-group v-model="form.link">
              <el-checkbox label="生产环节"></el-checkbox>
              <el-checkbox label="流通环节"></el-checkbox>
              <el-checkbox label="餐饮环节"></el-checkbox>
              <el-checkbox label="无环节限制"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item class="item d-flex" label="计划批次数量: " prop="number1">
        <!-- 输入框绑定到 form.linkPlanTotals 对象，使用环节名称作为 key -->
        <el-input
          class="input-w"
          type="number"
          placeholder="生产"
          :min="1"
          step="1"
          onkeypress="return event.charCode >= 48 && event.charCode <= 57"
          :disabled="!form.link.includes('生产环节')"
          v-model.number="form.linkPlanTotals['生产环节']"
          @change="validateBatchInput('生产环节', $event)"
          @input="enforcePositiveInteger('生产环节')"
        ></el-input>
        <el-input
          class="input-w"
          type="number"
          placeholder="流通"
          :min="1"
          step="1"
          onkeypress="return event.charCode >= 48 && event.charCode <= 57"
          :disabled="!form.link.includes('流通环节')"
          v-model.number="form.linkPlanTotals['流通环节']"
          @change="validateBatchInput('流通环节', $event)"
          @input="enforcePositiveInteger('流通环节')"
        ></el-input>
        <el-input
          class="input-w"
          type="number"
          placeholder="餐饮"
          :min="1"
          step="1"
          onkeypress="return event.charCode >= 48 && event.charCode <= 57"
          :disabled="!form.link.includes('餐饮环节')"
          v-model.number="form.linkPlanTotals['餐饮环节']"
          @change="validateBatchInput('餐饮环节', $event)"
          @input="enforcePositiveInteger('餐饮环节')"
        ></el-input>
        <el-input
          class="input-w"
          type="number"
          placeholder="无限制"
          :min="1"
          step="1"
          onkeypress="return event.charCode >= 48 && event.charCode <= 57"
          :disabled="!form.link.includes('无环节限制')"
          v-model.number="form.linkPlanTotals['无环节限制']"
          @change="validateBatchInput('无环节限制', $event)"
          @input="enforcePositiveInteger('无环节限制')"
        ></el-input>
      </el-form-item>
    </el-form>

    <!-- 计划详情区域，仅当选择了环节时显示 -->
    <div v-if="form.link.length > 0" class="title-1">
      计划详情
      <span
        style="
          margin-left: 15px;
          font-weight: normal;
          font-size: 14px;
          color: #606266;
        "
        v-if="totalExistingBatches"
      >
        (总计划批次：{{ totalExistingBatches?.existTotal || 0 }}/{{
          totalExistingBatches?.planTotal || 0
        }})
        <span v-if="isPlanFull" style="color: #f56c6c; margin-left: 5px">
          计划数已满
        </span>
      </span>
    </div>
    <!-- Tabs 组件，仅当选择了环节时显示 -->
    <el-tabs v-if="form.link.length > 0" v-model="activeName">
      <!-- 遍历选中的环节 form.link 来生成 Tab 页 -->
      <el-tab-pane
        v-for="linkName in form.link"
        :label="linkName"
        :name="linkName"
        :key="linkName"
      >
        <!-- 使用 linkName 作为 key，确保 Tab 的稳定性 -->
        <el-form
          label-width="180px"
          label-position="right"
          size="small"
          class="dialog-form"
        >
          <el-form-item class="item" label="食品类别：" prop="newscbname">
            <!-- 级联选择器，数据绑定到 tabFormList[linkName] -->
            <el-cascader
              style="width: 100%"
              v-model="tabFormList[linkName]"
              :show-all-levels="true"
              popper-class="first-no-check-cascader"
              filterable
              clearable
              :options="treeList"
              @expand-change="changeSourceType"
              :props="{
                label: 'cateName',
                value: 'cateName',
                children: 'children',
                multiple: true,
                checkStrictly: true,
                emitPath: true,
              }"
              placeholder="请选择食品类别"
              @change="(val) => handleCascaderChange(val, linkName)"
            >
            </el-cascader>
          </el-form-item>
        </el-form>
        <!-- 表格展示，数据源为 tableData[linkName] -->
        <el-table
          v-if="tableData[linkName] && tableData[linkName].length > 0"
          :data="tableData[linkName]"
          style="width: 100%"
          :span-method="(params) => tableSpanMethod(params, linkName)"
          border
          stripe
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontWeight: 'bold',
            textAlign: 'center',
            borderRight: '1px solid #ebeef5',
          }"
          :cell-style="{
            textAlign: 'center',
            borderRight: '1px solid #ebeef5',
            borderBottom: '1px solid #ebeef5',
          }"
          class="merge-table"
        >
          <el-table-column prop="cate1" label="食品大类"></el-table-column>
          <el-table-column prop="cate2" label="食品亚类"></el-table-column>
          <el-table-column prop="cate3" label="食品次亚类"></el-table-column>
          <el-table-column prop="cate4" label="食品细类"></el-table-column>
          <el-table-column label="细化批次数" width="200">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.total"
                :min="1"
                @change="handleTotalChange($event, scope.row)"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="大类批次数" prop="cate1Total">
            <template #default="scope">
              <!-- 计算并显示该行所属大类的总批次数 -->
              <span>{{ calculateCate1Total(scope.row.cate1, linkName) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="220"
            align="center"
            fixed="right"
          >
            <template slot-scope="scope">
              <!-- 删除按钮，调用 handleDeleteRow 方法，传入 scope 和 linkName -->
              <el-button
                type="text"
                icon="el-icon-delete"
                class="delete-btn"
                @click="handleDeleteRow(scope, linkName)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!-- 当表格无数据时显示提示 -->
        <div v-else style="text-align: center; color: #909399; padding: 20px 0">
          请在级联选择器中选择食品类别
        </div>
      </el-tab-pane>
    </el-tabs>
    <div class="d-flex-btn">
      <el-button type="default" @click="handleCancel">取消</el-button>
      <el-button
        v-if="!isEdit"
        type="primary"
        @click="handleSave"
        :disabled="isPlanFull"
        >新增</el-button
      >
      <el-button
        v-if="isEdit"
        type="primary"
        @click="handleSave"
        :disabled="isPlanFull"
        >修改</el-button
      >
      <el-button
        type="success"
        @click="handleSaveAndDeploy"
        :disabled="isPlanFull"
        >保存并部署</el-button
      >
      <el-button
        type="primary"
        icon="el-icon-upload2"
        @click="handleImport"
        :disabled="!canImport || isPlanFull"
        >导入</el-button
      >
    </div>

    <!-- 文件导入对话框 -->
    <el-dialog
      title="导入Excel（本地处理）"
      :visible.sync="importDialogVisible"
      width="450px"
      :close-on-click-modal="false"
      @close="cancelImport"
      :close-on-press-escape="!importProcessing"
      :show-close="!importProcessing"
    >
      <div class="import-dialog-content">
        <div class="upload-box">
          <div
            class="upload-area"
            @click="triggerFileInput"
            @dragover.prevent="handleDragOver"
            @dragleave.prevent="handleDragLeave"
            @drop.prevent="handleDrop"
            :class="{ 'is-dragover': isDragOver }"
          >
            <input
              type="file"
              ref="fileInput"
              class="file-input"
              accept=".xls,.xlsx"
              @change="handleFileChange"
              @click.stop
            />
            <i class="el-icon-upload"></i>
            <div class="upload-text">
              <p>将文件拖到此处，或点击上传</p>
            </div>
          </div>
          <p class="upload-hint">
            只能上传Excel文件，<a href="javascript:;" @click="downloadTemplate"
              >下载模板</a
            >
          </p>
          <div v-if="importFile" class="selected-file">
            <i class="el-icon-document"></i>
            <span class="file-name">{{ importFile.name }}</span>
            <i class="el-icon-delete" @click="removeSelectedFile"></i>
          </div>
        </div>
        <div class="import-notes">
          <p>注意：</p>
          <p>1.请使用模板导入，确保格式正确</p>
          <p>2.导入数据会覆盖当前表单中已有的数据，以Excel文件中的数据为准</p>
          <p>3.此操作在本地处理，Excel文件不会上传到服务器</p>
          <p>
            4.注意Excel文件中不要包含隐藏字符如空格、特殊符号π、℃、Å等，可能导致解析错误
          </p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelImport">取消</el-button>
        <el-button
          type="primary"
          @click="confirmImport"
          :disabled="importProcessing"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 导入 API 请求方法
import {
  findTreeListAll,
  selectXrHnyList,
  sampleCatesImports,
  downTemplate,
} from "@/api/modules/entrust.js";
// 导入对话框组件（如果用到的话，当前代码没有直接使用）
import editDialog from "./edit";
import insertDialog from "./insert";
// 导入日期处理库
import dayjs from "dayjs";
// 导入新的 API 请求方法
import {
  addPlan,
  getClassA,
  getInfo,
  editPlan,
  getTotalBatchCount,
  deleteDetectionItem,
  deploymentPlan,
} from "@/api/modules/entrustNew.js";
// 导入xlsx库，用于解析Excel文件
import { read, utils } from "xlsx";

export default {
  components: { insertDialog, editDialog },
  data() {
    return {
      treeList: [], // 食品分类树形数据
      // 表格列定义（当前代码未使用）
      columns: [
        // ... (保留原有注释)
      ],
      list: [], // 列表数据（当前代码未使用）
      // 分页信息（当前代码未使用）
      page: {
        total: 0,
        current: 1,
        size: 10,
      },
      echartsDomObject: null, // Echarts 对象（当前代码未使用）
      tableList: [], // 表格列表（当前代码未使用）
      // 表单数据模型
      form: {
        scbname: "", // 报送分类B（旧字段，可能已废弃，但保留）
        newscbname: [], // 报送分类A（旧字段，可能已废弃，但保留）
        plannedCompletionTime: "", // 计划抽样完成日期
        reportTime: "", // 报告出具日期（当前代码未使用）
        year: "", // 年份（当前代码未使用）
        link: [], // 选中的环节列表，例如 ['生产环节', '流通环节']
        classA: "", // 报送分类A
        classB: "", // 报送分类B
        taskName: "", // 任务名称
        // 将 num1, num2, num3 合并到一个对象中，用环节名称作为 key
        linkPlanTotals: {
          生产环节: null, // 生产环节计划批次数
          流通环节: null, // 流通环节计划批次数
          餐饮环节: null, // 餐饮环节计划批次数
          无环节限制: null, // 无环节限制计划批次数
        },
        executionArea: "", // 任务执行区域
        taskSource: "", // 任务来源
      },

      // 表单校验规则
      rules: {
        taskSource: [
          { required: true, message: "请选择任务来源", trigger: "change" },
        ],
        classB: [
          { required: true, message: "请输入报送分类B", trigger: "blur" },
        ],
        classA: [
          { required: true, message: "请输入报送分类A", trigger: "blur" },
        ],
        taskName: [
          { required: true, message: "请选择任务名称", trigger: "change" },
        ],
        link: [{ required: true, message: "请选择环节", trigger: "change" }], // 触发方式改为 change
        // 添加对环节批次数量的校验
        linkPlanTotals: [
          {
            validator: this.validateLinkPlanTotals,
            message: "请为选中的环节填写计划批次数量",
            trigger: "change",
          },
        ],
      },
      classBList: [], // 报送分类B下拉列表
      cascaderTag: [], // 暂存级联选择器点击时已有的tag
      classAList: [], // 报送分类A下拉列表
      classAListAll: [], // 包含层级关系的完整分类A数据
      taskSourceList: [], // 任务来源下拉列表
      activeName: "", // 当前激活的 Tab 页名称 (环节名称)
      // 使用对象替代数组，以环节名称作为 key，确保数据与环节正确对应
      tabFormList: {}, // 存储每个环节Tab页的级联选择器选中值，例如: {'生产环节': [['食品', '肉类'], ...], '流通环节': []}
      tableData: {}, // 存储每个环节Tab页的表格数据，例如: {'生产环节': [{...}, ...], '流通环节': []}
      isEdit: false, // 是否是编辑状态
      totalExistingBatches: null, // 存储已存在的总批次数信息 {existTotal: number, planTotal: number}

      // 新增：定义环节名称到 stepType 的映射关系
      linkMap: {
        生产环节: {
          stepType: 0,
          stepTypeName: "生产环节",
          planTotalKey: "生产环节",
        },
        流通环节: {
          stepType: 1,
          stepTypeName: "流通环节",
          planTotalKey: "流通环节",
        },
        餐饮环节: {
          stepType: 2,
          stepTypeName: "餐饮环节",
          planTotalKey: "餐饮环节",
        },
        无环节限制: {
          stepType: 3,
          stepTypeName: "无环节限制",
          planTotalKey: "无环节限制",
        },
      },
      loading: true,
      // 新增导入对话框相关数据
      importDialogVisible: false,
      importFile: null,
      isDragOver: false,
      importProcessing: false, // 标记是否正在处理导入，防止重复点击
      // 新增：存储每个环节的合并单元格配置
      mergeConfig: {}, // { [linkName]: [ {row, col, rowspan, colspan}, ... ] }
    };
  },
  async mounted() {
    const { taskSource, classA, classB, taskName } = this.$route.query;
    // 先判断是否是编辑模式，添加加载效果
    if (classA && taskName) {
      // 编辑模式需要 classA 和 taskName
      this.isEdit = true;
    }
    // 初始化加载数据
    await this.findTreeListAll(); // 确保先加载食品分类树形数据
    await this.getTaskSourceData(); // 获取任务来源数据（改为await，确保加载完成）

    // 判断是否是编辑模式
    if (classA && taskName) {
      try {
        console.log("开始设置级联数据", {
          taskSource,
          classA,
          classB,
          taskName,
        });

        // 1. 先设置taskSource（如果存在）
        if (taskSource) {
          this.$set(this.form, "taskSource", taskSource);

          // 手动填充classAList
          const matchedTaskSource = this.classAListAll.find(
            (item) => item.name === taskSource
          );

          if (matchedTaskSource && matchedTaskSource.children) {
            this.classAList = matchedTaskSource.children.map((item) => ({
              label: item.name,
              value: item.name,
            }));
            console.log("手动设置classAList:", this.classAList);
          }
        }

        // 等待DOM更新，确保watch执行完毕
        await this.$nextTick();

        // 2. 设置 classA
        if (classA) {
          this.$set(this.form, "classA", classA);

          // 手动填充classBList
          if (taskSource) {
            const matchedTaskSource = this.classAListAll.find(
              (item) => item.name === taskSource
            );

            if (matchedTaskSource && matchedTaskSource.children) {
              const matchedClassA = matchedTaskSource.children.find(
                (item) => item.name === classA
              );

              if (matchedClassA && matchedClassA.children) {
                this.classBList = matchedClassA.children.map((item) => ({
                  label: item.name,
                  value: item.name,
                }));
                console.log("手动设置classBList:", this.classBList);
              }
            }
          }
        }

        // 等待DOM更新，确保watch执行完毕
        await this.$nextTick();

        // 3. 设置 classB
        if (classB) {
          this.$set(this.form, "classB", classB);
        }

        // 4. 设置taskName
        this.$set(this.form, "taskName", taskName);

        // 等待DOM更新，确保所有数据都已设置
        await this.$nextTick();

        // 5. 加载详情数据
        await this.getInfoData({ taskSource, classA, classB, taskName });
      } catch (error) {
        console.error("设置级联数据失败:", error);
        this.$message.error("加载编辑数据失败，请检查网络连接");
      } finally {
        this.loading = false;
      }
    } else {
      // 新增模式
      this.loading = false;
    }
  },

  watch: {
    // 监听任务来源变化
    "form.taskSource": {
      handler(newVal, oldVal) {
        if (!newVal) {
          // 如果清空了任务来源，清空级联的选项
          this.classAList = [];
          this.$set(this.form, "classA", "");
          this.$set(this.form, "classB", "");
          return;
        }

        // 当任务来源变化时，更新报送分类A下拉列表
        const matchedItem = this.classAListAll.find(
          (item) => item.name === newVal
        );

        if (matchedItem && matchedItem.children) {
          // 更新报送分类A列表
          this.classAList = matchedItem.children.map((item) => ({
            label: item.name,
            value: item.name,
          }));

          // 如果任务来源变化了(非初始化)，清空classA和classB的值
          if (newVal !== oldVal && oldVal !== undefined) {
            this.$set(this.form, "classA", "");
            this.$set(this.form, "classB", "");
            this.classBList = [];
            this.totalExistingBatches = null; // 清空批次信息
          }
        } else {
          this.classAList = [];
          // 如果找不到匹配项或没有子项，清空classA和classB
          this.$set(this.form, "classA", "");
          this.$set(this.form, "classB", "");
          this.classBList = [];
        }
      },
    },

    // 监听报送分类A的变化
    "form.classA": {
      async handler(newVal, oldVal) {
        // 首先检查是否有选择任务来源
        if (!this.form.taskSource) {
          this.classBList = [];
          return;
        }

        // 先找到对应的任务来源项
        const taskSourceItem = this.classAListAll.find(
          (item) => item.name === this.form.taskSource
        );

        if (!taskSourceItem || !taskSourceItem.children) {
          this.classBList = [];
          return;
        }

        // 在任务来源的子项中查找匹配的分类A项
        const matchedItem = taskSourceItem.children.find(
          (item) => item.name === newVal
        );

        // 更新分类B下拉列表
        if (matchedItem && matchedItem.children) {
          console.log("找到匹配的分类A项，子项:", matchedItem.children);
          this.classBList = matchedItem.children.map((item) => ({
            label: item.name,
            value: item.name,
          }));
        } else {
          console.log("未找到匹配的分类A项或其没有子项");
          this.classBList = [];
        }

        // 只有当分类A的值真正改变时（非初始化），才清空分类B
        if (newVal !== oldVal && oldVal !== undefined) {
          // 检查 oldVal 是否是 undefined 来区分初始化
          this.$set(this.form, "classB", ""); // 使用 $set 清空
          this.totalExistingBatches = null; // 清空批次信息
        }
      },
    },

    // 监听报送分类B的变化
    "form.classB": {
      handler(newVal) {
        if (this.form.taskSource && this.form.classA && newVal) {
          this.fetchTotalExistingBatches(); // 获取已存在的批次数
        } else {
          this.totalExistingBatches = null; // 如果任务来源、A 或 B 任一为空，清零
        }
      },
      immediate: false, // 不需要立即执行，等待 taskSource、classA 和 classB 都有值
    },

    // 监听环节选择的变化
    "form.link": {
      handler(newVal, oldVal) {
        const oldLinks = oldVal || []; // 处理初始 oldVal 为 undefined 的情况

        // 确定新增和移除的环节
        const addedLinks = newVal.filter((link) => !oldLinks.includes(link));
        const removedLinks = oldLinks.filter((link) => !newVal.includes(link));

        // 为新增的环节初始化数据结构
        addedLinks.forEach((link) => {
          // 使用 $set 确保响应性
          this.$set(this.tableData, link, this.tableData[link] || []); // 保留已有数据（如有）或初始化为空数组
          this.$set(this.tabFormList, link, this.tabFormList[link] || []);
          // 初始化计划批次数为 0 或 null
          if (
            this.form.linkPlanTotals[link] === undefined ||
            this.form.linkPlanTotals[link] === null
          ) {
            this.$set(this.form.linkPlanTotals, link, null); // 或者设置为 0
          }
        });

        // 清理被移除环节的数据结构
        removedLinks.forEach((link) => {
          // 使用 $delete 确保响应性
          this.$delete(this.tableData, link);
          this.$delete(this.tabFormList, link);
          // 清空对应的计划批次数
          this.$set(this.form.linkPlanTotals, link, null);
          // 清理合并单元格配置
          this.$delete(this.mergeConfig, link);
        });

        // 更新 activeName
        if (newVal.length > 0) {
          // 如果当前 activeName 不在新的 link 数组中，或者 activeName 为空，则设置 activeName 为新数组的第一个元素
          if (!newVal.includes(this.activeName) || !this.activeName) {
            this.activeName = newVal[0];
          }
        } else {
          // 如果没有选中的环节了，清空 activeName
          this.activeName = "";
        }

        // 手动触发linkPlanTotals的校验
        if (this.$refs.form) {
          this.$refs.form.validateField("linkPlanTotals");
        }
      },
      deep: true, // 深度监听
      immediate: true, // 组件加载时立即执行一次 handler，处理初始值
    },
    // tabFormList 的 watcher 可能不再需要，因为 handleCascaderChange 直接更新 tableData
    // tabFormList: { ... } // 保留原有注释
    // 监听环节计划批次数的变化
    "form.linkPlanTotals": {
      handler(newVal, oldVal) {
        console.log("环节计划批次数变化:", newVal);

        // 检查已建批数量是否超过总批次数
        const totalPlan = this.totalExistingBatches?.planTotal || 0;
        const existTotal = this.totalExistingBatches?.existTotal || 0;

        // 如果总批次数为0，说明还没有加载数据或新建状态，不需要校验
        if (totalPlan === 0) return;

        // 计算当前变更后的总批次数
        let currentInputTotal = 0;
        Object.values(newVal).forEach((value) => {
          if (value && !isNaN(Number(value))) {
            currentInputTotal += Number(value);
          }
        });

        // 检查是否超过总批次数
        if (existTotal + currentInputTotal > totalPlan) {
          this.$message.warning(
            `设置的批次总数已超过最大限制(${totalPlan})，请重新设置`
          );

          // 找出变更项并恢复
          if (oldVal) {
            // 找出哪个环节的值变化了
            for (const link in newVal) {
              if (newVal[link] !== oldVal[link]) {
                // 恢复到旧值
                this.$set(this.form.linkPlanTotals, link, oldVal[link]);
                break;
              }
            }
          }
        }

        // 手动触发linkPlanTotals的校验
        if (this.$refs.form) {
          this.$refs.form.validateField("linkPlanTotals");
        }
      },
      deep: true,
    },
  },

  computed: {
    /**
     * 计算当前表单中输入的总计划批次数（所有环节之和）
     * 用于实时更新总计划批次的显示
     */
    currentPlanTotal() {
      let total = 0;
      if (this.form && this.form.linkPlanTotals) {
        // 遍历所有环节，计算批次数总和
        Object.values(this.form.linkPlanTotals).forEach((value) => {
          if (value && !isNaN(Number(value))) {
            total += Number(value);
          }
        });
      }
      return total;
    },

    /**
     * 判断是否可以导入（任务来源、报送分类A、报送分类B和任务名称都已填写）
     */
    canImport() {
      return !!(
        this.form.taskSource &&
        this.form.classA &&
        this.form.classB &&
        this.form.taskName
      );
    },

    /**
     * 判断计划是否已满，用于禁用按钮
     */
    isPlanFull() {
      if (!this.totalExistingBatches) return false;

      // 如果已存在的批次数等于计划总数，则计划已满
      return (
        this.totalExistingBatches.existTotal >=
        this.totalExistingBatches.planTotal
      );
    },
  },

  methods: {
    // 自定义校验方法：验证环节计划批次数量是否已填写
    validateLinkPlanTotals(rule, value, callback) {
      // 检查是否选择了环节
      if (!this.form.link || this.form.link.length === 0) {
        // 如果未选择环节，则不需要校验批次数量
        callback();
        return;
      }

      // 检查选中的环节对应的批次数量是否已填写
      let isValid = true;
      let errorMessage = "";

      // 遍历选中的环节
      for (const linkName of this.form.link) {
        const currentPlanTotal = this.form.linkPlanTotals[linkName];

        // 检查批次数量是否有效
        if (
          currentPlanTotal === null ||
          currentPlanTotal === "" ||
          Number(currentPlanTotal) <= 0 ||
          !Number.isInteger(Number(currentPlanTotal))
        ) {
          isValid = false;
          errorMessage = `环节 "${linkName}" 的计划批次数量必须是大于0的整数`;
          break;
        }
      }

      if (isValid) {
        callback(); // 校验通过
      } else {
        callback(new Error(errorMessage)); // 校验失败，返回错误信息
      }
    },

    // 获取计划详情数据
    async getInfoData(params) {
      try {
        const res = await getInfo(params); // 调用 API 获取数据
        if (res.code === 200 && res.data) {
          const data = res.data;
          // 使用 $set 确保响应式更新表单顶层数据
          this.$set(this.form, "taskName", data.taskName);
          this.$set(
            this.form,
            "plannedCompletionTime",
            data.plannedCompletionTime
          );
          this.$set(this.form, "executionArea", data.executionArea);

          // --- 数据回填逻辑重构 ---
          const links = []; // 存储从后端获取的环节
          const newTableData = {}; // 临时存储新的表格数据
          const newTabFormList = {}; // 临时存储新的级联选择器数据
          const newLinkPlanTotals = {
            // 临时存储环节计划批次数
            生产环节: null,
            流通环节: null,
            餐饮环节: null,
          };

          // 按 stepType 分组处理 sampleCateDetailList
          const groupedData = {};
          data.sampleCateDetailList?.forEach((item) => {
            const stepType = item.stepType;
            if (!groupedData[stepType]) {
              groupedData[stepType] = [];
            }
            groupedData[stepType].push(item);
          });

          // 遍历 linkMap 来确定环节并填充数据
          for (const linkName in this.linkMap) {
            const linkInfo = this.linkMap[linkName];
            const stepType = linkInfo.stepType;
            const items = groupedData[stepType];

            if (items && items.length > 0) {
              // 如果该环节有数据，则添加到 links 数组
              links.push(linkName);
              // 设置该环节的计划总数 (取第一个元素的 stepPlanTotal 即可，因为同一环节总数相同)
              newLinkPlanTotals[linkName] = items[0].stepPlanTotal;
              // 填充表格数据
              newTableData[linkName] = items.map((item) => ({ ...item })); // 创建副本以避免引用问题
              // 填充级联选择器数据
              newTabFormList[linkName] = items.map((item) =>
                [item.cate1, item.cate2, item.cate3, item.cate4].filter(Boolean)
              ); // 过滤掉可能为 null 或 undefined 的层级
            }
          }

          // 更新表单的 link 数组
          this.$set(this.form, "link", links);

          // 使用 $nextTick 确保 link 更新后 DOM 渲染完成，再更新 tab 和 table 数据
          await this.$nextTick();

          // 更新 linkPlanTotals, tableData, tabFormList
          this.$set(this.form, "linkPlanTotals", newLinkPlanTotals);
          this.$set(this, "tableData", newTableData);
          this.$set(this, "tabFormList", newTabFormList);

          // 如果 links 不为空，设置 activeName
          if (links.length > 0) {
            this.activeName = links[0];
          }

          // 在数据回填后，如果需要，手动触发一次批次数获取 (虽然 classB 的 watch 应该会触发，但为保险起见)
          if (this.form.taskSource && this.form.classA && this.form.classB) {
            this.fetchTotalExistingBatches();
          }
        } else {
          this.$message.error(res.msg || "获取计划详情失败");
        }
      } catch (error) {
        console.error("获取计划详情失败:", error);
        this.$message.error("获取计划详情异常");
      } finally {
        this.loading = false;
      }
    },

    // 获取报送分类A数据
    async getClassAData() {
      try {
        const res = await getClassA();
        if (res.code === 200) {
          console.log("获取报送分类A数据:", res.data);

          // 只更新顶级类别，不修改已有的层级结构
          if (!this.classAListAll || this.classAListAll.length === 0) {
            this.classAListAll = res.data;
          }

          // 只有在没有选择任务来源时，才直接使用顶级数据作为分类A列表
          // 否则应该由taskSource的选择来决定classAList的内容
          if (!this.form.taskSource) {
            // 这里只用于展示所有分类A，不应该被选择
            this.classAList = res.data.flatMap((item) =>
              item.children
                ? item.children.map((child) => ({
                    label: child.name,
                    value: child.name,
                  }))
                : []
            );
          }
        } else {
          this.$message.error(res.msg || "获取报送分类A失败");
        }
      } catch (error) {
        console.error("获取报送分类A失败:", error);
        this.$message.error("获取报送分类A异常");
      }
    },

    // 级联选择器展开时触发（保留原有逻辑）
    changeSourceType(selectItem) {
      // 一级分类只能选一个，如果一级分类修改了，清空已有值
      // 注意：此逻辑可能需要调整，取决于具体需求。当前实现是如果展开了不同的一级分类，会清空之前的选择。
      // if (this.cascaderTag.length > 0 && !this.cascaderTag.includes(selectItem[0])) {
      //   // this.newscbname = []; // 需要更新对应 tabFormList 的值
      //   const currentTabList = this.tabFormList[this.activeName];
      //   if (currentTabList && currentTabList.length > 0 && currentTabList[0][0] !== selectItem[0]) {
      //       this.$set(this.tabFormList, this.activeName, []); // 清空当前 tab 的选择
      //       this.$set(this.tableData, this.activeName, []);   // 同时清空 table 数据
      //   }
      // }
      // this.cascaderTag = selectItem; // 更新当前展开的路径
    },

    // 查询操作（当前代码未使用）
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10,
      };
      this.loadData();
    },
    // 重置操作（当前代码未使用）
    handleReset() {
      this.form = {
        // 重置为初始状态
        scbname: "",
        newscbname: [],
        plannedCompletionTime: "",
        reportTime: "",
        year: "",
        link: [],
        classA: "",
        classB: "",
        taskName: "",
        linkPlanTotals: {
          生产环节: null,
          流通环节: null,
          餐饮环节: null,
          无环节限制: null,
        },
        executionArea: "",
        taskSource: "",
      };
      // 清空 tab 相关数据
      this.tableData = {};
      this.tabFormList = {};
      this.activeName = "";
      this.totalExistingBatches = null;
      // 清空合并单元格配置
      this.mergeConfig = {};
      this.$refs.form.resetFields(); // 重置 Element UI 表单校验状态
      // this.handleQuery(); // 如果需要重新查询列表，取消注释
    },
    // 加载数据（当前代码未使用）
    loadData() {
      // ... (保留原有注释和逻辑)
      // let data = { ... };
      // selectXrHnyList(data).then(res => { ... });
    },
    // 获取食品分类树形数据
    async findTreeListAll() {
      try {
        const res = await findTreeListAll();
        if (res.code === 200) {
          this.treeList = this.getTypeList(res.data);
        } else {
          this.$message.error(res.msg || "获取食品分类失败");
        }
      } catch (error) {
        console.error("获取食品分类失败:", error);
        this.$message.error("获取食品分类异常");
      }
    },

    // 递归处理食品分类数据，移除没有子节点的 children 属性
    getTypeList(listData) {
      listData.forEach((items) => {
        if (items.children && items.children.length > 0) {
          this.getTypeList(items.children); // 递归处理子节点
        } else {
          // delete items.children; // 直接删除 children 属性，使叶子节点 selectable
          items.children = undefined; // Element UI Cascader 推荐设置为 undefined
        }
      });
      return listData;
    },

    // 计算指定环节中，某个食品大类的总批次数
    calculateCate1Total(cate1, linkName) {
      // 计算同一大类下所有细类批次数的总和
      // const currentTab = this.form.link.indexOf(this.activeName); // 不再使用 index
      // if (currentTab === -1) return 0; // 不再需要此检查

      // 直接使用 linkName 访问数据
      const currentTable = this.tableData[linkName];
      if (!currentTable) return 0; // 如果当前环节没有数据，返回 0

      return currentTable
        .filter((row) => row.cate1 === cate1) // 过滤出相同大类的行
        .reduce((sum, row) => sum + (Number(row.total) || 0), 0); // 累加 total，确保转为数字且处理 NaN/undefined
    },

    // 校验所有选定环节的细类批次数是否都大于0
    validateBatchNumbers() {
      let isValid = true;
      let errorMessages = [];

      // 检查是否选择了环节
      if (!this.form.link || this.form.link.length === 0) {
        this.$message.error("请至少选择一个抽样环节");
        return false;
      }

      // 遍历选中的环节
      this.form.link.forEach((linkName) => {
        const currentTable = this.tableData[linkName];
        const currentPlanTotal = this.form.linkPlanTotals[linkName];

        // 检查环节计划总数是否有效
        if (
          currentPlanTotal === null ||
          currentPlanTotal === "" ||
          Number(currentPlanTotal) <= 0 ||
          !Number.isInteger(Number(currentPlanTotal))
        ) {
          isValid = false;
          errorMessages.push(
            `环节 "${linkName}" 的计划批次数量必须是大于0的整数`
          );
        }

        // 检查该环节是否有表格数据
        if (currentTable && currentTable.length > 0) {
          // 检查是否有细类批次数未设置或小于等于0
          const hasInvalidBatch = currentTable.some(
            (row) =>
              row.total === null ||
              row.total === "" ||
              Number(row.total) <= 0 ||
              !Number.isInteger(Number(row.total))
          );
          if (hasInvalidBatch) {
            isValid = false;
            errorMessages.push(
              `环节 "${linkName}" 中存在未设置或无效的细化批次数，必须是大于0的整数`
            );
          }

          // 检查细类批次数之和是否等于环节计划总数
          const detailTotal = currentTable.reduce(
            (sum, row) => sum + (Number(row.total) || 0),
            0
          );
          if (Number(currentPlanTotal) !== detailTotal) {
            isValid = false;
            errorMessages.push(
              `环节 "${linkName}" 的细化批次数总和 (${detailTotal}) 与计划批次数量 (${currentPlanTotal}) 不一致`
            );
          }
        } else if (currentPlanTotal !== null && Number(currentPlanTotal) > 0) {
          // 如果环节计划数 > 0 但没有选择食品细类
          isValid = false;
          errorMessages.push(
            `环节 "${linkName}" 已设置计划批次数量，但未选择食品细类`
          );
        }
      });

      // 如果有错误，显示第一条错误信息
      if (!isValid && errorMessages.length > 0) {
        this.$message.error(errorMessages[0]); // 显示第一条错误信息
      }
      return isValid;
    },

    // 准备提交的数据（提取公共逻辑）
    prepareSubmitData() {
      // 将各环节的 tableData 打平成一层列表 sampleCateDetailList
      const sampleCateDetailList = [];

      // 遍历选中的环节
      this.form.link.forEach((linkName) => {
        const currentTable = this.tableData[linkName];
        const linkInfo = this.linkMap[linkName]; // 获取环节的 stepType 等信息
        const stepPlanTotal = this.form.linkPlanTotals[linkName]; // 获取环节的计划总数

        if (currentTable && currentTable.length > 0 && linkInfo) {
          // 计算当前环节中每个大类的总批次数
          const cate1TotalMap = {};
          currentTable.forEach((item) => {
            if (!cate1TotalMap[item.cate1]) {
              cate1TotalMap[item.cate1] = 0;
            }
            cate1TotalMap[item.cate1] += Number(item.total) || 0;
          });

          // 构建提交的细类数据
          currentTable.forEach((item) => {
            sampleCateDetailList.push({
              ...item, // 包含 cate1, cate2, cate3, cate4, total 等从表格行获取的数据
              id: this.isEdit ? item.id : undefined, // 编辑时保留 id，新增时移除
              stepType: linkInfo.stepType,
              stepTypeName: linkInfo.stepTypeName,
              stepPlanTotal: Number(stepPlanTotal) || 0, // 确保是数字
              planFinishDate: this.form.plannedCompletionTime, // 计划完成日期（冗余字段，兼容旧后端？）
              plannedCompletionTime: this.form.plannedCompletionTime, // 计划完成日期
              cate1Total: cate1TotalMap[item.cate1] || 0, // 使用计算好的大类总数
              executionArea: this.form.executionArea, // 添加执行区域
            });
          });
        }
      });

      // 构建最终提交的参数对象
      const params = {
        taskSource: this.form.taskSource,
        classA: this.form.classA,
        classB: this.form.classB,
        taskName: this.form.taskName,
        plannedCompletionTime: this.form.plannedCompletionTime,
        executionArea: this.form.executionArea, // 包含执行区域
        // link: this.form.link, // link 字段可能不再需要，信息已在 sampleCateDetailList 中
        // num1/num2/num3 字段可能也不再需要，用 linkPlanTotals 替代
        // linkPlanTotals: this.form.linkPlanTotals, // 根据后端接口决定是否传递这个对象
        sampleCateDetailList: sampleCateDetailList, // 包含所有环节的详细数据
      };
      return params;
    },

    // 保存操作（新增或修改）
    handleSave() {
      // type 参数现在主要用于区分调用 addPlan还是 editPlan
      this.$refs.form.validate((valid) => {
        // 先校验表单，再校验批次数
        if (valid) {
          // 添加批次数校验
          if (!this.validateBatchNumbers()) {
            return false; // 如果批次数校验不通过，阻止继续执行
          }

          const params = this.prepareSubmitData(); // 获取准备好的提交数据

          // 根据是否为编辑状态调用不同的 API
          const saveAction = this.isEdit ? editPlan : addPlan;
          const successMessage = this.isEdit ? "修改成功" : "新增成功";

          saveAction(params)
            .then((res) => {
              if (res.code === 200) {
                this.$message.success(successMessage);
                // 操作成功后跳转回列表页或详情页
                this.$router.push({
                  path: "/samplePlan/entrustNew", // 跳转路径
                  query: {
                    activeTab: "1", // 指示应该激活哪个标签页
                    // 可以传递参数用于列表页高亮或其他操作
                    taskSource: this.form.taskSource,
                    classA: this.form.classA,
                    classB: this.form.classB,
                    taskName: this.form.taskName,
                  },
                });
              } else {
                this.$message.error(
                  res.msg || (this.isEdit ? "修改失败" : "新增失败")
                );
              }
            })
            .catch((error) => {
              console.error("保存失败:", error);
              this.$message.error("保存操作异常");
            });
        } else {
          console.log("表单校验失败");
          return false; // 阻止继续执行
        }
      });
    },

    // 保存并部署操作
    handleSaveAndDeploy() {
      this.$refs.form.validate((valid) => {
        // 先校验表单，再校验批次数
        if (valid) {
          // 添加批次数校验
          if (!this.validateBatchNumbers()) {
            return false; // 如果批次数校验不通过，阻止继续执行
          }

          const params = this.prepareSubmitData(); // 获取准备好的提交数据

          // 根据是否为编辑状态调用不同的保存 API
          const saveAction = this.isEdit ? editPlan : addPlan;
          const successMessage = this.isEdit
            ? "修改并部署成功"
            : "新增并部署成功";
          const failureMessage = this.isEdit ? "修改失败" : "新增失败";

          // 第一步：保存计划
          saveAction(params)
            .then((saveRes) => {
              if (saveRes.code === 200) {
                // 第二步：部署计划
                const deployParams = {
                  taskSource: this.form.taskSource,
                  classA: this.form.classA,
                  classB: this.form.classB,
                  taskName: this.form.taskName,
                };
                deploymentPlan(deployParams)
                  .then((deployRes) => {
                    if (deployRes.code === 200) {
                      this.$message.success(successMessage);
                      // 操作成功后跳转回列表页或详情页
                      this.$router.push({
                        path: "/samplePlan/entrustNew",
                        query: {
                          activeTab: "1",
                          taskSource: this.form.taskSource,
                          classA: this.form.classA,
                          classB: this.form.classB,
                          taskName: this.form.taskName,
                        },
                      });
                    } else {
                      // 部署失败，可能需要提示用户计划已保存但未部署
                      this.$message.error(
                        `计划已${this.isEdit ? "修改" : "保存"}，但部署失败：${
                          deployRes.msg
                        }`
                      );
                    }
                  })
                  .catch((deployError) => {
                    console.error("部署失败:", deployError);
                    this.$message.error(
                      `计划已${this.isEdit ? "修改" : "保存"}，但部署操作异常`
                    );
                  });
              } else {
                this.$message.error(saveRes.msg || failureMessage);
              }
            })
            .catch((saveError) => {
              console.error("保存失败:", saveError);
              this.$message.error("保存操作异常");
            });
        } else {
          console.log("表单校验失败");
          return false;
        }
      });
    },

    // 处理细化批次数输入框变化事件
    handleTotalChange(value, row) {
      // 可以在这里添加额外的逻辑，例如实时更新大类总数（虽然模板里已经自动计算了）
      console.log("细化批次数变化:", value, row);
      // 注意：直接修改 scope.row.total 已经是响应式的了
    },

    // 取消操作，返回上一页或列表页
    handleCancel() {
      // 最好是跳转回列表页，并可能传递参数以保持状态
      this.$router.push({
        path: "/samplePlan/entrustNew", // 返回列表页
        query: {
          activeTab: "1", // 保持在计划管理 Tab
          // 可以保留查询条件等，如果需要的话
          taskSource: this.$route.query.taskSource, // 从路由获取原始查询参数
          classA: this.$route.query.classA,
          classB: this.$route.query.classB,
          taskName: this.$route.query.taskName,
        },
      });
      // 或者简单地返回上一页
      // this.$router.go(-1);
    },

    // 获取指定分类A和B下已存在的总批次数
    fetchTotalExistingBatches() {
      // 确保 taskSource、classA 和 classB 都有值
      if (!this.form.taskSource || !this.form.classA || !this.form.classB) {
        this.totalExistingBatches = null; // 清空或设为默认值
        return;
      }
      const params = {
        taskSource: this.form.taskSource,
        classA: this.form.classA,
        classB: this.form.classB,
      };
      getTotalBatchCount(params)
        .then((res) => {
          if (res.code === 200) {
            // 后端应返回一个包含 existTotal 和 planTotal 的对象
            this.totalExistingBatches = res.data || {
              existTotal: 0,
              planTotal: 0,
            };
          } else {
            this.$message.error("获取已存在批次数失败：" + res.msg);
            this.totalExistingBatches = { existTotal: 0, planTotal: 0 }; // 出错时设为默认值
          }
        })
        .catch((err) => {
          console.error("获取已存在批次数失败:", err);
          this.$message.error("获取已存在批次数失败，请检查网络连接");
          this.totalExistingBatches = { existTotal: 0, planTotal: 0 }; // 出错时设为默认值
        });
    },

    // 计算当前表单中设置的总计划批次（所有环节之和）
    calculateTotalPlanBatches() {
      let total = 0;
      // 遍历选中的环节，累加其计划批次数
      this.form.link.forEach((linkName) => {
        total += Number(this.form.linkPlanTotals[linkName]) || 0;
      });
      return total;
    },

    // 删除表格中的行
    handleDeleteRow(scope, linkName) {
      // scope 包含 $index 和 row
      const rowIndex = scope.$index;
      const rowData = scope.row;

      // 如果是编辑状态且该行有 id，则调用后端接口删除
      if (this.isEdit && rowData.id) {
        // 判断是否只有一条数据，只有一条数据不可删除，否者会导致整个计划被删除
        let tableLength = 0;
        this.form.link.forEach((linkName) => {
          tableLength += this.tableData[linkName].length;
        });
        if (tableLength === 1) {
          this.$message.error("该计划下至少需要保留一条数据");
          return;
        }
        // 添加确认提示
        this.$confirm("确定删除该条细类计划吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            deleteDetectionItem({ ids: rowData.id })
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success("删除成功");
                  // 从前端数据中移除 (接口成功后再移除)
                  this.removeTableRow(linkName, rowIndex, rowData);
                } else {
                  this.$message.error(res.msg || "删除失败");
                }
              })
              .catch((err) => {
                console.error("删除失败:", err);
                this.$message.error("删除操作异常");
              });
          })
          .catch(() => {
            // 用户点击取消
            this.$message.info("已取消删除");
          });
      } else {
        // 如果是新增状态或者该行没有 id (例如新增后未保存的行)，直接从前端数据移除
        this.removeTableRow(linkName, rowIndex, rowData);
        this.$message.success("移除成功"); // 或者不提示，因为只是本地移除
      }
    },

    // 封装从前端数据移除行的逻辑
    removeTableRow(linkName, rowIndex, rowData) {
      // 1. 从 tableData 中移除行
      if (this.tableData[linkName]) {
        this.tableData[linkName].splice(rowIndex, 1);
      }

      // 2. 从 tabFormList 中移除对应的级联选择器值
      if (this.tabFormList[linkName]) {
        // 找到与删除行匹配的级联选择器值并移除
        // 构建一个唯一标识符用于匹配，例如拼接所有层级
        const deletedRowIdentifier = [
          rowData.cate1,
          rowData.cate2,
          rowData.cate3,
          rowData.cate4,
        ]
          .filter(Boolean)
          .join("-");
        this.tabFormList[linkName] = this.tabFormList[linkName].filter(
          (cascaderValue) => {
            const cascaderIdentifier = cascaderValue.filter(Boolean).join("-");
            return cascaderIdentifier !== deletedRowIdentifier;
          }
        );
        // 注意：如果允许多次选择同一个细类，此逻辑需要调整
      }
    },

    // 食品分类级联选择器变化处理
    handleCascaderChange(val, linkName) {
      console.log(`级联选择器值变化，环节: ${linkName}，值:`, val);

      // 调试信息：检查级联选择器原始值
      console.log("级联选择器原始值类型:", Object.prototype.toString.call(val));
      console.log("级联选择器原始值内容:", JSON.stringify(val, null, 2));

      // 确保初始化tableData[linkName]
      if (!this.tableData[linkName]) {
        this.$set(this.tableData, linkName, []);
      }

      // 处理空值的情况
      if (!val || (Array.isArray(val) && val.length === 0)) {
        console.log("级联选择器值为空，清空表格数据");
        this.$set(this.tableData, linkName, []);
        return;
      }

      // 当前环节表格中已存在的数据
      const currentTableData = this.tableData[linkName] || [];
      console.log("当前表格已有数据:", currentTableData);

      // 即将构建的新表格数据
      const newTableData = [];

      try {
        // 处理emitPath=true的情况
        // 如果val是二维数组，表示多选+emitPath=true的情况
        if (Array.isArray(val) && val.length > 0 && Array.isArray(val[0])) {
          console.log("检测到二维数组格式，处理emitPath=true的情况");

          // 处理每个选中的路径数组
          val.forEach((path, pathIndex) => {
            console.log(`处理第${pathIndex + 1}个路径:`, path);

            if (!Array.isArray(path) || path.length === 0) return;

            // 提取节点中的cateName
            const categories = path
              .map((node) => {
                if (node && typeof node === "object") {
                  return node.cateName || null;
                }
                return node;
              })
              .filter(Boolean);

            console.log("提取的类别名称:", categories);

            if (categories.length === 0) return;

            // 构建表格行
            const newRow = {
              cate1: categories[0] || null,
              cate2: categories[1] || null,
              cate3: categories[2] || null,
              cate4: categories[3] || null,
              total: 1,
            };

            console.log("构建的表格行:", newRow);

            // 检查是否已存在
            const cateKey = categories.join("-");
            const existingRow = currentTableData.find((row) => {
              const rowKey = [row.cate1, row.cate2, row.cate3, row.cate4]
                .filter(Boolean)
                .join("-");
              return rowKey === cateKey;
            });

            if (existingRow) {
              console.log("找到已存在的行，保留原数据:", existingRow);
              newTableData.push(existingRow);
            } else {
              console.log("添加新行数据");
              newTableData.push(newRow);
            }
          });

          // 更新表格数据
          console.log(`为环节 ${linkName} 设置新的表格数据:`, newTableData);
          this.$set(this.tableData, linkName, newTableData);

          // 强制刷新
          this.$forceUpdate();

          return; // 处理完毕，直接返回
        }

        // 确保val是数组
        const valueArray = Array.isArray(val) ? val : [val];
        console.log("处理的值数组:", valueArray);

        // 遍历级联选择器选中的值
        valueArray.forEach((item, index) => {
          console.log(`处理第${index + 1}个选中项:`, item);

          // 对于级联选择器中选中的每个节点，构建表格行数据
          let cate1 = null,
            cate2 = null,
            cate3 = null,
            cate4 = null;

          if (item && typeof item === "object") {
            console.log("处理对象类型选中项");

            // 检查是否是节点对象（有cateName属性）
            if (item.cateName) {
              console.log(`找到cateName: ${item.cateName}`);

              // 尝试查找完整路径
              const path = this.findNodePath(item);
              console.log(
                "找到的路径:",
                path.map((n) => n.cateName)
              );

              if (path.length > 0) {
                // 从路径提取分类名称
                const pathNames = path.map((node) => node.cateName);
                cate1 = pathNames[0] || null;
                cate2 = pathNames[1] || null;
                cate3 = pathNames[2] || null;
                cate4 = pathNames[3] || null;

                console.log("从路径提取的分类:", {
                  cate1,
                  cate2,
                  cate3,
                  cate4,
                });
              } else {
                // 如果找不到路径，使用当前节点作为大类
                console.log("未找到完整路径，使用当前节点作为大类");
                cate1 = item.cateName;
              }
            } else {
              console.log("对象中没有cateName属性，尝试查找其他属性");
              // 尝试其他可能的属性名
              const possibleName = item.label || item.value || item.name;
              if (possibleName) {
                cate1 = possibleName;
                console.log(`使用其他属性作为大类: ${cate1}`);
              }
            }
          } else if (Array.isArray(item)) {
            console.log("处理数组类型选中项");
            // 处理数组形式的路径
            const values = item
              .map((i) => {
                if (i && typeof i === "object") {
                  return i.cateName || i.label || i.value || i.name;
                }
                return i;
              })
              .filter(Boolean);

            console.log("从数组中提取的值:", values);

            [cate1, cate2, cate3, cate4] = values;
          } else if (typeof item === "string") {
            console.log("处理字符串类型选中项");
            // 直接使用字符串作为大类
            cate1 = item;
          }

          // 检查是否至少有大类
          if (!cate1) {
            console.log("没有有效的大类，跳过此项");
            return; // 如果没有大类，跳过
          }

          // 构建表格行数据
          const newRow = {
            cate1,
            cate2,
            cate3,
            cate4,
            total: 1, // 默认批次数
          };

          console.log("构建的新行数据:", newRow);

          // 检查是否已存在相同条目
          const cateKey = [cate1, cate2, cate3, cate4]
            .filter(Boolean)
            .join("-");
          console.log("生成的唯一键:", cateKey);

          const existingRow = currentTableData.find((row) => {
            const rowKey = [row.cate1, row.cate2, row.cate3, row.cate4]
              .filter(Boolean)
              .join("-");
            return rowKey === cateKey;
          });

          if (existingRow) {
            console.log("找到已存在的行，保留原数据:", existingRow);
            newTableData.push(existingRow);
          } else {
            console.log("添加新行数据");
            newTableData.push(newRow);
          }
        });

        // 更新表格数据
        console.log(`为环节 ${linkName} 设置新的表格数据:`, newTableData);
        this.$set(this.tableData, linkName, newTableData);

        // 强制更新视图
        this.$forceUpdate();

        // 添加延迟执行，确保DOM更新
        this.$nextTick(() => {
          console.log(
            `环节 ${linkName} 表格数据更新后:`,
            this.tableData[linkName]
          );
          if (this.tableData[linkName] && this.tableData[linkName].length > 0) {
            console.log("表格应该显示了");
          } else {
            console.log("表格数据为空或未正确设置");
          }
        });
      } catch (error) {
        console.error("处理级联选择器值时出错:", error);
        this.$message.error("处理食品类别选择时出错");
      }
    },

    // 在树中查找节点的完整路径 - 增强版
    findNodePath(targetNode, currentNode = null, path = []) {
      // 如果没有指定当前节点，从根节点开始
      const nodes = currentNode ? currentNode.children || [] : this.treeList;

      // 如果目标节点没有cateName，无法匹配
      if (!targetNode || !targetNode.cateName) {
        return [];
      }

      for (const node of nodes) {
        // 检查当前节点是否是目标节点
        if (
          node === targetNode ||
          (node.cateName && node.cateName === targetNode.cateName)
        ) {
          return [...path, node];
        }

        // 如果当前节点有子节点，递归搜索
        if (node.children && node.children.length > 0) {
          const result = this.findNodePath(targetNode, node, [...path, node]);
          if (result.length > 0) {
            return result;
          }
        }
      }

      // 如果在当前分支没有找到目标节点，返回空路径
      return [];
    },

    // 添加新的方法，用于验证批次输入是否超过限制
    validateBatchInput(linkName, newValue) {
      // 获取必要的数据
      const totalPlan = this.totalExistingBatches?.planTotal || 0;
      const existTotal = this.totalExistingBatches?.existTotal || 0;

      // 如果总批次数为0，说明还没有加载数据或新建状态，不需要校验
      if (totalPlan === 0) return true;

      // 保存当前修改值
      const currentValue = Number(newValue);

      // 验证输入值是否为正整数
      if (currentValue <= 0 || !Number.isInteger(currentValue)) {
        this.$message.warning("批次数量必须是大于0的整数");
        this.$nextTick(() => {
          // 如果输入无效，重置为1或之前有效的值
          this.form.linkPlanTotals[linkName] = Math.max(
            1,
            Math.floor(Math.abs(currentValue)) || 1
          );
        });
        return false;
      }

      // 计算其他环节的批次总和
      let otherInputTotal = 0;
      for (const link in this.form.linkPlanTotals) {
        if (link !== linkName && this.form.linkPlanTotals[link] !== null) {
          otherInputTotal += Number(this.form.linkPlanTotals[link]) || 0;
        }
      }

      // 计算总批次数
      const totalBatchCount = existTotal + otherInputTotal + currentValue;

      // 检查是否超过限制
      if (totalBatchCount > totalPlan) {
        // 计算最大可设置值
        const maxAllowed = Math.max(
          1,
          totalPlan - existTotal - otherInputTotal
        );

        this.$message.warning(
          `设置的批次总数已超过最大限制(${totalPlan})，自动调整为最大可设置值(${maxAllowed})`
        );

        // 立即恢复到最大允许值或1
        this.$nextTick(() => {
          this.form.linkPlanTotals[linkName] = maxAllowed;
        });

        return false;
      }

      return true;
    },

    // 确保输入的是正整数
    enforcePositiveInteger(linkName) {
      // 获取当前值
      let value = this.form.linkPlanTotals[linkName];

      // 如果为空或非法值，不处理（change事件中会进行处理）
      if (value === null || value === undefined || value === "") {
        return;
      }

      // 转换为数字
      value = Number(value);

      // 验证并修正
      if (value <= 0 || !Number.isInteger(value)) {
        // 修正为最接近的正整数，至少为1
        this.form.linkPlanTotals[linkName] = Math.max(
          1,
          Math.floor(Math.abs(value)) || 1
        );
      }

      // 手动触发表单校验
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.validateField("linkPlanTotals");
        }
      });
    },

    // 获取任务来源数据
    async getTaskSourceData() {
      try {
        // 调用API获取任务来源数据
        const res = await getClassA();
        if (res.code === 200) {
          console.log("获取到任务来源数据:", res.data);
          this.classAListAll = res.data;
          // 转换为任务来源下拉选项
          this.taskSourceList = res.data.map((item) => ({
            label: item.name,
            value: item.name,
          }));

          // 如果是编辑模式，并且已有任务来源值，初始化级联数据
          if (this.isEdit && this.form.taskSource) {
            console.log(
              "编辑模式，初始化级联数据，当前任务来源:",
              this.form.taskSource
            );

            // 先找到对应的任务来源项
            const matchedTaskSource = this.classAListAll.find(
              (item) => item.name === this.form.taskSource
            );

            if (matchedTaskSource && matchedTaskSource.children) {
              console.log(
                "找到匹配的任务来源，子项:",
                matchedTaskSource.children
              );

              // 更新报送分类A下拉列表
              this.classAList = matchedTaskSource.children.map((item) => ({
                label: item.name,
                value: item.name,
              }));

              // 如果已有classA值，继续初始化classBList
              if (this.form.classA) {
                console.log("已有classA值:", this.form.classA);

                // 找到对应的classA项
                const matchedClassA = matchedTaskSource.children.find(
                  (item) => item.name === this.form.classA
                );

                if (matchedClassA && matchedClassA.children) {
                  console.log("找到匹配的classA子项:", matchedClassA.children);

                  // 更新分类B下拉列表
                  this.classBList = matchedClassA.children.map((item) => ({
                    label: item.name,
                    value: item.name,
                  }));

                  console.log("更新后的classBList:", this.classBList);
                } else {
                  console.log("未找到匹配的classA或其没有子项");
                }
              }
            } else {
              console.log("未找到匹配的任务来源或其没有子项");
            }
          }
        } else {
          this.$message.error(res.msg || "获取任务来源失败");
        }
      } catch (error) {
        console.error("获取任务来源失败:", error);
        this.$message.error("获取任务来源异常");
      }
    },

    /**
     * 处理导入按钮点击
     */
    handleImport() {
      if (!this.canImport) return;

      // 重置导入相关状态
      this.importProcessing = false;
      this.importFile = null;
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = null;
      }

      // 打开导入对话框
      this.importDialogVisible = true;
    },

    /**
     * 触发文件选择
     */
    triggerFileInput() {
      // 确保只有在没有正在处理导入的情况下才触发文件选择
      if (this.importProcessing) return;

      // 先清空当前input的值，确保同一文件可以再次触发change事件
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = null;
      }

      // 使用setTimeout延迟执行，避免事件冲突
      setTimeout(() => {
        if (this.$refs.fileInput) {
          this.$refs.fileInput.click();
        }
      }, 100);
    },

    /**
     * 处理文件选择变化
     */
    handleFileChange(event) {
      // 阻止事件冒泡
      event.stopPropagation();

      // 如果正在处理，则忽略此次选择
      if (this.importProcessing) {
        return;
      }

      const file = event.target.files[0];
      if (!file) return;

      // 检查文件类型
      const fileType = file.name.split(".").pop().toLowerCase();
      if (!["xlsx", "xls"].includes(fileType)) {
        this.$message.error("只能上传Excel文件");
        if (this.$refs.fileInput) {
          this.$refs.fileInput.value = null;
        }
        return;
      }

      // 存储选择的文件
      this.importFile = file;

      console.log("文件已选择:", file.name);
    },

    /**
     * 下载Excel模板
     */
    downloadTemplate(event) {
      this.$message.info("正在下载模板...");

      // 从public目录获取模板文件
      const templatePath = process.env.BASE_URL + "计划详情模版.xlsx";

      // 创建一个隐藏的a标签用于下载
      const link = document.createElement("a");
      link.href = templatePath;
      link.download = "计划详情模版.xlsx";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    /**
     * 确认导入 - 纯客户端操作，解析Excel文件并填充表单
     * 注意：此功能完全在客户端执行，不会将文件上传到服务器
     */
    confirmImport() {
      // 防止重复触发，先禁用确认按钮
      if (!this.importFile || this.importProcessing) {
        this.$message.warning("请先选择要导入的Excel文件");
        return;
      }

      // 标记正在处理导入，防止重复触发
      this.importProcessing = true;

      // 立即关闭弹窗，防止重复点击
      this.importDialogVisible = false;

      // 使用FileReader在客户端读取文件内容，不会发送到服务器
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          // 解析Excel文件
          const data = new Uint8Array(e.target.result);
          const workbook = read(data, { type: "array" });

          // 创建用于存储处理后数据的对象
          const newTableData = {};
          const newTabFormList = {};
          const selectedLinks = [];

          // 获取所有sheet页名称
          const sheetNames = workbook.SheetNames;

          // 打印Excel中的全部Sheet
          console.log("Excel文件中的sheet页:", sheetNames);

          // 遍历所有sheet页
          sheetNames.forEach((sheetName) => {
            // 检查sheet名称是否是我们支持的环节类型
            if (this.linkMap[sheetName]) {
              // 获取当前sheet页的数据
              const worksheet = workbook.Sheets[sheetName];

              // 解析合并单元格信息
              const merges = worksheet["!merges"] || [];
              console.log(`Sheet ${sheetName} 的合并单元格信息:`, merges);

              // 将合并信息转换为适合表格使用的格式
              const mergeConfig = merges.map((merge) => ({
                row: merge.s.r - 1, // 减1是因为第一行通常是表头，实际数据从第二行开始
                col: merge.s.c,
                rowspan: merge.e.r - merge.s.r + 1,
                colspan: merge.e.c - merge.s.c + 1,
              }));

              // 存储该环节的合并配置
              this.$set(this.mergeConfig, sheetName, mergeConfig);

              // 使用header: 1获取原始数据，保持行列结构
              const rawData = utils.sheet_to_json(worksheet, { header: 1 });
              console.log(`Sheet ${sheetName} 的原始数据:`, rawData);

              // 转换为标准的JSON格式，但保持合并单元格的结构
              const jsonData = this.convertRawDataToJson(rawData, mergeConfig);
              console.log(`Sheet ${sheetName} 的转换后数据:`, jsonData);

              // 只有当sheet页含有除表头外的数据行时才处理该环节
              if (jsonData && jsonData.length > 0) {
                // 添加到已选择的环节中
                if (!selectedLinks.includes(sheetName)) {
                  selectedLinks.push(sheetName);
                }

                // 使用新方法处理导入的类别数据
                const processed = this.processImportedCategories(
                  jsonData,
                  sheetName
                );

                // 保存处理后的数据
                newTableData[sheetName] = processed.tableData;
                newTabFormList[sheetName] = processed.formValues;

                // 计算该环节的总批次数
                let totalBatches = 0;
                processed.tableData.forEach((row) => {
                  totalBatches += Number(row.total) || 0;
                });

                // 设置环节的计划批次数
                this.$set(this.form.linkPlanTotals, sheetName, totalBatches);
              }
            }
          });

          // 延迟更新数据，确保弹窗已完全关闭
          setTimeout(() => {
            // 只有当selectedLinks有内容时才更新环节选择
            if (selectedLinks.length > 0) {
              this.$set(this.form, "link", selectedLinks);
              this.activeName = selectedLinks[0]; // 设置默认激活的Tab
            }

            // 先更新表格数据
            selectedLinks.forEach((linkName) => {
              // 直接使用$set更新每个环节的表格数据
              if (newTableData[linkName] && newTableData[linkName].length > 0) {
                this.$set(this.tableData, linkName, [
                  ...newTableData[linkName],
                ]);
              } else {
                this.$set(this.tableData, linkName, []);
              }
            });

            // 等待DOM更新完成后处理级联选择器
            this.$nextTick(() => {
              // 清空并重新设置级联选择器值
              selectedLinks.forEach((linkName) => {
                // 先确保该环节的级联选择器值是一个空数组
                this.$set(this.tabFormList, linkName, []);

                // 打印级联选择器值设置情况
                console.log(`环节 ${linkName} 级联选择器值初始化为空数组`);

                // 强制刷新视图
                this.$forceUpdate();
              });

              // 再次等待DOM更新，确保清空操作已完成
              this.$nextTick(() => {
                // 然后设置新的级联选择器值
                selectedLinks.forEach((linkName) => {
                  if (
                    newTabFormList[linkName] &&
                    newTabFormList[linkName].length > 0
                  ) {
                    // 设置新值之前先打印
                    console.log(
                      `准备为环节 ${linkName} 设置级联选择器值:`,
                      newTabFormList[linkName]
                    );

                    // 使用新值
                    this.$set(this.tabFormList, linkName, [
                      ...newTabFormList[linkName],
                    ]);

                    // 设置后再打印
                    console.log(
                      `已为环节 ${linkName} 设置级联选择器值:`,
                      this.tabFormList[linkName]
                    );
                  }
                });

                // 再次强制更新视图
                this.$forceUpdate();
              });
            });

            // 重置文件选择
            this.importFile = null;
            if (this.$refs.fileInput) {
              this.$refs.fileInput.value = null;
            }

            this.$message.success("Excel文件解析成功，数据已导入到表单");

            // 处理完成，重置标记
            this.importProcessing = false;
          }, 300);
        } catch (error) {
          console.error("解析Excel文件失败:", error);
          this.$message.error("解析Excel文件失败，请检查文件格式是否正确");
          this.importProcessing = false; // 出错时也要重置处理状态
        }
      };

      reader.onerror = () => {
        this.$message.error("读取文件失败，请重试");
        this.importProcessing = false; // 出错时也要重置处理状态
      };

      // 读取文件
      reader.readAsArrayBuffer(this.importFile);
    },

    /**
     * 处理文件拖拽经过
     */
    handleDragOver(event) {
      this.isDragOver = true;
    },

    /**
     * 处理文件拖拽离开
     */
    handleDragLeave(event) {
      this.isDragOver = false;
    },

    /**
     * 处理文件拖拽放下
     */
    handleDrop(event) {
      this.isDragOver = false;

      const files = event.dataTransfer.files;
      if (files.length === 0) return;

      const file = files[0];
      // 检查文件类型
      const fileType = file.name.split(".").pop().toLowerCase();
      if (!["xlsx", "xls"].includes(fileType)) {
        this.$message.error("只能上传Excel文件");
        return;
      }

      this.importFile = file;
    },

    /**
     * 移除已选择的文件
     */
    removeSelectedFile() {
      this.importFile = null;
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = null;
      }
    },

    /**
     * 取消导入操作
     */
    cancelImport() {
      // 关闭弹窗
      this.importDialogVisible = false;

      // 清空已选择的文件
      this.importFile = null;
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = null;
      }

      // 重置导入处理状态
      this.importProcessing = false;

      // 清空合并单元格配置（如果需要的话）
      // this.mergeConfig = {};
    },

    // 新增一个辅助方法，确保级联选择器的值能正确匹配树形结构
    findMatchingPaths(categories) {
      const result = [];

      // 对于每一个类别组合
      categories.forEach((catePath) => {
        if (!catePath || !catePath.length) return;

        // 尝试在树形结构中找到匹配的路径
        // 从第一层开始查找
        let matchFound = false;
        let currentLevel = this.treeList || [];
        let matchedPath = [];

        // 逐层匹配
        for (let i = 0; i < catePath.length; i++) {
          const cateName = catePath[i];
          if (!cateName) continue;

          // 在当前层级查找匹配项
          const match = currentLevel.find((item) => item.cateName === cateName);
          if (match) {
            matchedPath.push(match.cateName);
            // 如果有子级且还有下一层需要匹配，继续往下查找
            if (match.children && i < catePath.length - 1) {
              currentLevel = match.children;
            } else if (i < catePath.length - 1) {
              // 如果没有子级但还有需要匹配的层级，匹配失败
              matchFound = false;
              break;
            } else {
              // 成功匹配到最后一层
              matchFound = true;
            }
          } else {
            // 未找到匹配项
            matchFound = false;
            break;
          }
        }

        // 如果成功匹配，添加到结果中
        if (matchFound && matchedPath.length > 0) {
          result.push(matchedPath);
        } else if (matchedPath.length > 0) {
          // 部分匹配，添加已匹配的路径
          result.push(matchedPath);
          console.log("部分匹配:", matchedPath, "原路径:", catePath);
        }
      });

      return result;
    },

    // 在级联选择器选项中查找匹配的类别路径
    findCategoryPathInOptions(categories) {
      if (!categories || !categories.length) return null;

      // 处理输入的类别数组
      const catePath = categories.filter(Boolean);
      if (!catePath.length) return null;

      // 打印详细日志
      console.log("尝试为类别路径找到匹配值:", catePath);

      // 递归搜索函数获取节点
      const findNode = (currentLevel, targetName, path = []) => {
        for (const node of currentLevel) {
          if (node.cateName === targetName) {
            // 找到匹配的节点
            return { node, path: [...path, node] };
          }

          // 如果有子节点，递归搜索
          if (node.children && node.children.length > 0) {
            const result = findNode(node.children, targetName, [...path, node]);
            if (result) {
              return result;
            }
          }
        }

        return null;
      };

      // 存储找到的节点，Element UI级联选择器格式
      const result = [];

      // 对每个类别名称查找匹配的节点
      catePath.forEach((name) => {
        const nodeInfo = findNode(this.treeList, name);
        if (nodeInfo) {
          // Element UI的级联选择器需要节点对象
          result.push(nodeInfo.node);
        }
      });

      if (result.length > 0) {
        console.log("找到的节点:", result);
        return result;
      }

      return null;
    },

    // 导入Excel时处理级联选择器数据的方法
    processImportedCategories(jsonData, linkName) {
      // 存储处理后的表格数据和级联选择器值
      const tableData = [];
      const formValues = [];

      // 打印原始数据
      console.log(`处理环节 ${linkName} 的导入数据:`, jsonData);

      // 创建一个映射，存储每种类别组合对应的节点集合
      const categoryNodeMap = new Map();

      // 遍历所有行数据
      jsonData.forEach((row) => {
        if (!row["食品大类"]) return;

        // 构建类别路径
        const categories = [
          row["食品大类"] || null,
          row["食品亚类"] || null,
          row["食品次亚类"] || null,
          row["食品细类"] || null,
        ].filter(Boolean);

        // 如果没有有效的类别，跳过
        if (!categories.length) return;

        // 创建表格行数据
        const tableRow = {
          cate1: row["食品大类"] || null,
          cate2: row["食品亚类"] || null,
          cate3: row["食品次亚类"] || null,
          cate4: row["食品细类"] || null,
          total: parseInt(row["细化批次数"]) || 1,
        };

        // 添加到表格数据
        tableData.push(tableRow);

        // 类别组合的唯一标识
        const categoryKey = categories.join("-");

        // 如果没有处理过这个类别组合，尝试查找匹配的节点路径
        if (!categoryNodeMap.has(categoryKey)) {
          // 递归查找完整路径 - 为了适应emitPath=true的情况
          // 我们需要找到从根到叶的完整节点对象路径
          const findPath = (currentLevel, categoryIndex, path = []) => {
            if (categoryIndex >= categories.length) {
              return path; // 完成匹配
            }

            const currentCategory = categories[categoryIndex];
            const matchingNode = currentLevel.find(
              (node) => node.cateName === currentCategory
            );

            if (!matchingNode) {
              return null; // 找不到匹配节点
            }

            // 将当前节点添加到路径
            const newPath = [...path, matchingNode];

            // 如果是最后一级或者没有子节点，返回当前路径
            if (
              categoryIndex === categories.length - 1 ||
              !matchingNode.children
            ) {
              return newPath;
            }

            // 继续查找下一级
            return findPath(matchingNode.children, categoryIndex + 1, newPath);
          };

          // 从树的根开始查找
          const nodePath = findPath(this.treeList, 0);

          if (nodePath && nodePath.length > 0) {
            categoryNodeMap.set(categoryKey, nodePath);
          }
        }
      });

      // 将找到的节点路径添加到formValues
      categoryNodeMap.forEach((nodePath, key) => {
        if (nodePath && nodePath.length > 0) {
          // 由于emitPath=true，我们需要存储完整的节点路径
          formValues.push(nodePath);
          console.log(
            `为环节 ${linkName} 添加级联选择器路径:`,
            nodePath.map((n) => n.cateName)
          );
        }
      });

      return { tableData, formValues };
    },

    /**
     * 表格合并单元格方法
     * @param {Object} params - 包含 row, column, rowIndex, columnIndex 的参数对象
     * @param {string} linkName - 环节名称
     * @returns {Array} [rowspan, colspan] 或 undefined
     */
    tableSpanMethod({ row, column, rowIndex, columnIndex }, linkName) {
      const mergeList = this.mergeConfig[linkName] || [];

      // 查找当前位置是否是合并单元格的起点
      const merge = mergeList.find(
        (m) => m.row === rowIndex && m.col === columnIndex
      );

      if (merge) {
        // 如果是合并单元格的起点，返回合并的行数和列数
        return [merge.rowspan, merge.colspan];
      }

      // 检查当前位置是否在某个合并单元格的范围内（但不是起点）
      const inMergeArea = mergeList.find((m) => {
        return (
          rowIndex >= m.row &&
          rowIndex < m.row + m.rowspan &&
          columnIndex >= m.col &&
          columnIndex < m.col + m.colspan &&
          !(rowIndex === m.row && columnIndex === m.col) // 排除起点
        );
      });

      if (inMergeArea) {
        // 如果在合并区域内但不是起点，返回[0,0]隐藏该单元格
        return [0, 0];
      }

      // 默认情况，不合并
      return [1, 1];
    },

    /**
     * 将原始Excel数据转换为JSON格式，处理合并单元格
     * @param {Array} rawData - 原始数据数组
     * @param {Array} mergeConfig - 合并单元格配置
     * @returns {Array} 转换后的JSON数据
     */
    convertRawDataToJson(rawData, mergeConfig) {
      if (!rawData || rawData.length < 2) return []; // 至少需要表头和一行数据

      const headers = rawData[0]; // 第一行作为表头
      const dataRows = rawData.slice(1); // 从第二行开始是数据

      console.log("表头:", headers);
      console.log("数据行:", dataRows);

      // 处理合并单元格，填充空白单元格
      const processedRows = dataRows.map((row, rowIndex) => {
        const processedRow = [...row]; // 复制原行数据

        // 检查当前行是否有合并单元格需要处理
        mergeConfig.forEach((merge) => {
          // 如果当前行在合并范围内
          if (rowIndex >= merge.row && rowIndex < merge.row + merge.rowspan) {
            // 找到合并单元格的源值（通常在合并区域的第一行第一列）
            const sourceRowIndex = merge.row;
            const sourceColIndex = merge.col;

            // 获取源单元格的值
            let sourceValue = "";
            if (
              dataRows[sourceRowIndex] &&
              dataRows[sourceRowIndex][sourceColIndex] !== undefined
            ) {
              sourceValue = dataRows[sourceRowIndex][sourceColIndex];
            }

            // 将源值填充到合并区域内的所有单元格
            for (let col = merge.col; col < merge.col + merge.colspan; col++) {
              if (processedRow[col] === undefined || processedRow[col] === "") {
                processedRow[col] = sourceValue;
              }
            }
          }
        });

        return processedRow;
      });

      // 转换为对象格式
      const jsonData = processedRows.map((row) => {
        const obj = {};
        headers.forEach((header, index) => {
          if (header) {
            // 确保表头不为空
            obj[header] = row[index] || "";
          }
        });
        return obj;
      });

      console.log("转换后的JSON数据:", jsonData);
      return jsonData;
    },
  },
};
</script>

<style lang="scss">
/* 保留原有样式 */
.el-date-editor .el-range-separator {
  width: 24px !important;
}

.select-waraper {
  width: 200px;
  margin-right: 10px;
}

.d-flex {
  display: flex;
  flex-wrap: wrap;
  align-items: center; // 垂直居中对齐输入框和标签
  .el-form-item__content {
    margin-left: 0 !important; // 修正 d-flex 布局下的 content 偏移
    display: flex; // 让内部元素也 flex 排列
    align-items: center; // 内部元素垂直居中
  }
}

.input-w {
  width: 90px !important; // 设置固定宽度并提高优先级
  margin-right: 10px;
  &:last-child {
    margin-right: 0; // 最后一个输入框无右边距
  }
}

/* 为 placeholder 添加一些内边距或调整样式使其可见 */
.input-w ::v-deep .el-input__inner::placeholder {
  color: #c0c4cc; /* 或者其他你想要的颜色 */
  font-size: 12px; /* 调整字体大小 */
  // padding-left: 5px; /* 添加一些左内边距 */
}

.d-flex-btn {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.title-1 {
  font-weight: bold;
  margin: 20px 0;
  font-size: 18px;
  span {
    font-size: 14px;
    font-weight: normal;
  }
}

.wrapper {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin: 15px; // 添加外边距，避免紧贴边缘
}

/* 全局级联选择器样式修复 */
.el-cascader {
  line-height: normal; /* 保持默认行高 */
  height: auto; /* 自适应高度 */
  display: block; /* 确保独占一行 */
  width: 100%; /* 宽度100% */
}

.el-cascader__dropdown {
  min-width: 200px !important;
}

.el-cascader-menus {
  min-width: 200px !important;
}

.el-cascader-menu {
  min-width: 200px !important;
  max-height: 300px !important;
  overflow-y: auto; /* 确保内容过多时可以滚动 */
}

.delete-btn {
  color: #f56c6c; /* 使用 Element UI 的危险颜色 */
  &:hover {
    color: darken(#f56c6c, 10%); /* 鼠标悬停时颜色加深 */
  }
}

/* 确保 Form Item 垂直对齐 */
.el-form-item {
  margin-bottom: 18px; /* 调整表单项间距 */
}

.el-form-item__label {
  text-align: right; /* 确保标签右对齐 */
  padding-right: 12px; /* 标签和控件之间的间距 */
}

/* 对齐日期选择器 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100% !important; /* 确保日期选择器宽度正确 */
}

/* 修复 Checkbox Group 的对齐 */
.el-checkbox-group {
  line-height: 32px; /* 与输入框等控件高度对齐 */
}
.el-checkbox {
  margin-right: 20px; /* Checkbox 之间的间距 */
}

/* 修复输入数字组件宽度 */
.el-input-number {
  width: 150px; /* 调整数字输入框宽度 */
}

/* 文件导入对话框样式 */
.import-dialog-content {
  padding: 0 10px;
  .upload-box {
    margin-bottom: 15px;

    .upload-area {
      width: 100%;
      height: 150px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      transition: border-color 0.3s;

      &:hover,
      &.is-dragover {
        border-color: #409eff;
        background-color: rgba(64, 158, 255, 0.05);
      }

      .file-input {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
      }

      i {
        font-size: 28px;
        color: #8c939d;
        margin-bottom: 10px;
      }

      .upload-text {
        text-align: center;
        color: #606266;

        p {
          margin: 5px 0;
        }
      }
    }

    .upload-hint {
      font-size: 12px;
      color: #909399;
      margin: 8px 0;
      text-align: center;

      a {
        color: #409eff;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .selected-file {
      display: flex;
      align-items: center;
      margin-top: 10px;
      padding: 8px 12px;
      background-color: #f5f7fa;
      border-radius: 4px;

      i.el-icon-document {
        color: #909399;
        margin-right: 5px;
      }

      .file-name {
        flex: 1;
        font-size: 13px;
        color: #606266;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      i.el-icon-delete {
        color: #f56c6c;
        cursor: pointer;

        &:hover {
          color: #f78989;
        }
      }
    }
  }

  .import-notes {
    font-size: 12px;
    color: #606266;
    line-height: 1.6;

    p {
      margin: 5px 0;
    }
  }
}

/* 合并单元格表格样式美化 */
.merge-table {
  margin-top: 15px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* 表格整体边框 */
  ::v-deep .el-table {
    border: 1px solid #ebeef5;
  }

  /* 表头样式 */
  ::v-deep .el-table__header-wrapper {
    .el-table__header {
      th {
        background: linear-gradient(135deg, #f5f7fa 0%, #e8ecf0 100%);
        border-right: 1px solid #dcdfe6;
        border-bottom: 2px solid #dcdfe6;
        font-weight: 600;
        font-size: 14px;
        color: #303133;

        &:last-child {
          border-right: none;
        }
      }
    }
  }

  /* 表体样式 */
  ::v-deep .el-table__body-wrapper {
    .el-table__body {
      td {
        border-right: 1px solid #ebeef5;
        border-bottom: 1px solid #ebeef5;
        padding: 12px 8px;
        font-size: 13px;
        color: #606266;

        &:last-child {
          border-right: none;
        }
      }
    }
  }

  /* 斑马纹样式 */
  ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
    background: #fafbfc;
  }

  /* 悬停效果 */
  ::v-deep .el-table__body tr:hover > td {
    background-color: #f5f7fa !important;
  }

  /* 合并单元格特殊样式 */
  ::v-deep .el-table__body td {
    position: relative;

    /* 为合并的单元格添加特殊标识 */
    &[rowspan]:not([rowspan="1"]),
    &[colspan]:not([colspan="1"]) {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      font-weight: 500;
      border: 2px solid #bae6fd;
    }
  }

  /* 输入框样式优化 */
  ::v-deep .el-input-number {
    width: 100%;

    .el-input__inner {
      text-align: center;
      border: 1px solid #dcdfe6;
      border-radius: 4px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }

    .el-input-number__decrease,
    .el-input-number__increase {
      border: none;
      background: #f5f7fa;

      &:hover {
        background: #409eff;
        color: #fff;
      }
    }
  }

  /* 操作按钮样式 */
  ::v-deep .el-button--text {
    padding: 6px 12px;
    border-radius: 4px;
    transition: all 0.3s;

    &.delete-btn {
      color: #f56c6c;
      background: rgba(245, 108, 108, 0.1);

      &:hover {
        background: #f56c6c;
        color: #fff;
      }
    }
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .merge-table {
    ::v-deep .el-table__body td,
    ::v-deep .el-table__header th {
      padding: 8px 4px;
      font-size: 12px;
    }
  }
}
</style>
