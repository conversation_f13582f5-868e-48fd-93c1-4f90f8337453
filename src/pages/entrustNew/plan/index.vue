<template>
  <div class="">
    <div class="items-center d-flex">
      <!-- 任务来源筛选 -->
      <el-select
        v-model="selectedTaskSource"
        placeholder="请选择任务来源"
        class="select-waraper"
      >
        <el-option
          v-for="(item, index) in taskSourceList"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <!-- 报送分类A筛选 -->
      <el-select
        v-model="selected1"
        placeholder="请选择报送分类A"
        class="select-waraper"
        :disabled="!selectedTaskSource"
      >
        <el-option
          v-for="(item, index) in classAList"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <!-- 报送分类B筛选 -->
      <el-select
        v-model="selected2"
        placeholder="请选择报送分类B"
        class="select-waraper"
        :disabled="!selected1"
      >
        <el-option
          v-for="(item, index) in classBList"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <!-- 任务名称筛选 -->
      <el-select
        v-model="selected3"
        placeholder="请选择任务名称"
        class="select-waraper"
        :disabled="!selected2"
      >
        <el-option
          v-for="(item, index) in taskNameList"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <div v-show="isDeployment" class="red">已部署</div>
    </div>
    <div class="msg-box">
      <div class="msg-left">
        <div class="msg-card">
          <div class="title">计划制定批次</div>
          <div class="num">{{ boxMsg.planBatch?.batchCount }}</div>
          <div class="txt-1">
            计划制定完成率<span class="num-1">{{
              boxMsg.planBatch?.completionRate
            }}</span>
          </div>
          <div class="icon el-icon-s-order"></div>
        </div>

        <div class="msg-card">
          <div class="title">计划大类覆盖率</div>
          <div class="num red">
            {{
              (Number(boxMsg.planLargeCategory?.coverageRate) * 100).toFixed(2)
            }}%
          </div>
          <div class="txt-1">
            大类计划覆盖数量<span class="num-1">{{
              boxMsg.planLargeCategory?.coverageCount
            }}</span>
          </div>
          <div class="icon el-icon-upload"></div>
        </div>

        <div class="msg-card">
          <div class="title">计划细类覆盖率</div>
          <div class="num">
            {{ (Number(boxMsg.planSubclass?.coverageRate) * 100).toFixed(2) }}%
          </div>
          <div class="txt-1">
            细类计划覆盖数量<span class="num-1">{{
              boxMsg.planSubclass?.coverageCount
            }}</span>
          </div>
          <div class="icon el-icon-s-data"></div>
        </div>

        <div class="msg-card">
          <div class="title">计划抽样完成日期</div>
          <div class="num">{{ boxMsg.planSamplingCompletionDate?.date }}</div>
          <div class="txt-1">
            剩余完成时间<span class="num-1"
              >{{ boxMsg.planSamplingCompletionDate?.daysRemaining }}天</span
            >
          </div>
          <div class="icon el-icon-date"></div>
        </div>
      </div>
      <div class="msg-right">
        <div id="echartsBar" class="w-full h-full"></div>
      </div>
    </div>
    <div class="btn-group">
      <el-button type="primary" @click="addView">新增计划</el-button>
      <template v-if="selected1 && selected2 && selected3">
        <el-button :disabled="isDeployment" type="success" @click="editPlan"
          >编辑计划</el-button
        >
        <el-button
          :disabled="isDeployment"
          type="primary"
          @click="handleDeploymentPlan"
          :loading="loading2"
          >部署计划</el-button
        >
        <el-button
          :disabled="isDeployment"
          type="danger"
          @click="handleDelPlan"
          :loading="loading3"
          >删除计划</el-button
        >
      </template>
      <el-button @click="exportData">导出</el-button>
      <!-- <el-button @click="importData">导入</el-button> -->
      <div class="settings-button">
        <el-popover
          placement="bottom-start"
          title="字段显示选择"
          width="300"
          trigger="click"
          v-model="columnsPopoverVisible"
        >
          <div class="column-selector">
            <div class="column-selector-buttons">
              <el-button type="text" @click="selectAllColumns">全选</el-button>
              <el-button type="text" @click="unselectAllColumns"
                >全不选</el-button
              >
            </div>
            <div
              v-for="column in allTableColumns"
              :key="column.prop"
              class="column-item"
            >
              <el-checkbox
                v-model="column.visible"
                @change="handleColumnVisibilityChange"
                >{{ column.label }}</el-checkbox
              >
            </div>
          </div>
          <el-button
            slot="reference"
            size="small"
            class="settings-icon"
            icon="el-icon-setting"
          ></el-button>
        </el-popover>
      </div>
    </div>

    <!-- 新增 el-table -->
    <div class="table-container">
      <el-table
        :data="tableList"
        style="width: 100%; margin-top: 20px"
        class="custom-table"
        :header-row-style="{ background: '#ffffff' }"
        :header-cell-style="
          (column) => {
            if (column.label === '序号') {
              return {
                background: '#F8F8F9',
                fontWeight: 'bold',
                borderBottom: '1px solid #ebeef5',
              };
            }
          }
        "
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
          fixed="left"
          class="index-column"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('cate1')"
          prop="cate1"
          label="食品大类"
          width="120"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('cate2')"
          prop="cate2"
          label="食品亚类"
          width="120"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('cate3')"
          prop="cate3"
          label="食品品种"
          width="120"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('cate4')"
          prop="cate4"
          label="食品细类"
          width="120"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('specialCategory')"
          prop="specialCategory"
          label="特殊类别"
          align="center"
          width="120"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('stepTypeName')"
          prop="stepTypeName"
          label="抽样环节"
          width="120"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('cate1Total')"
          prop="cate1Total"
          label="大类批次数"
          width="120"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('total')"
          prop="total"
          label="细类批次数"
          width="120"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('specification')"
          prop="specification"
          label="型号规格"
          align="center"
          width="120"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('minSampleQuantity')"
          prop="minSampleQuantity"
          label="最少抽样数量≥"
          width="140"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('minSampleWeight')"
          prop="minSampleWeight"
          label="最少抽样重量>"
          width="140"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('minBackupWeight')"
          prop="minBackupWeight"
          label="最少备样重量>"
          width="140"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('samplingMethod')"
          prop="samplingMethod"
          label="抽样方法及数量"
          align="center"
          width="200"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('riskLevel')"
          prop="riskLevel"
          label="风险等级"
          align="center"
          width="100"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('storageCondition')"
          prop="storageCondition"
          label="存储条件"
          align="center"
          width="120"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('executionStandards')"
          prop="executionStandards"
          label="产品执行标准"
          align="center"
          width="150"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('project')"
          prop="project"
          label="抽检项目"
          align="center"
          width="150"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('remarks')"
          prop="remarks"
          label="备注信息"
          align="center"
          width="120"
        ></el-table-column>
        <el-table-column
          v-if="getColumnVisible('commonConfusion')"
          prop="commonConfusion"
          label="常见易混"
          align="center"
          width="200"
        ></el-table-column>
      </el-table>
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="page.current"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="page.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="page.total"
      style="margin: 20px 0; text-align: right"
    >
    </el-pagination>
  </div>
</template>

<script>
import {
  planCount,
  delPlan,
  deploymentPlan,
} from "@/api/modules/entrustNew.js";
import { selectSampleCateData } from "@/api/modules/entrust.js";

import editDialog from "./edit";
import insertDialog from "./insert";
import dayjs from "dayjs";
import echarts from "@/components/Echart/echarts";
import axios from "axios";
import { getToken } from "@/utils/auth.js";

import { getClassA } from "@/api/modules/entrustNew.js";

export default {
  components: { insertDialog, editDialog },
  data() {
    return {
      // 新增URL参数缓存
      urlParams: {
        taskSource: null,
        classA: null,
        classB: null,
        taskName: null,
      },
      // 新增标志位，控制watch行为
      isSettingFromUrl: false,
      // 新增绑定值
      selectedTaskSource: null,
      // 新增三个绑定值
      selected1: null,
      selected2: null,
      selected3: null,
      form: {},
      columns: [
        {
          label: "报送分类A",
          prop: "newscbname",
        },
        {
          label: "报送分类B",
          prop: "scbname",
        },
        {
          label: "年份",
          prop: "year",
        },
        {
          label: "计划抽样完成日期",
          prop: "planFinishDate",
        },
        {
          label: "报告出具日期",
          prop: "testDate",
        },
      ],
      list: [],
      page: {
        current: 1,
        size: 10,
        total: 0,
      },
      chartData: [], //当前选择用户的echarts对象
      echartsDomObject: null,
      tableList: [],
      boxMsg: {},
      chartList: [],
      classBList: [],
      classAList: [],
      taskNameList: [],
      classAListAll: [],
      taskSourceList: [], // 任务来源列表
      isDeployment: false, // 是否部署标识
      loading2: false,
      loading3: false,
      columnsPopoverVisible: false,
      allTableColumns: [
        {
          label: "食品大类",
          prop: "cate1",
          visible: true,
        },
        {
          label: "食品亚类",
          prop: "cate2",
          visible: true,
        },
        {
          label: "食品品种",
          prop: "cate3",
          visible: true,
        },
        {
          label: "食品细类",
          prop: "cate4",
          visible: true,
        },
        {
          label: "特殊类别",
          prop: "specialCategory",
          visible: true,
        },
        {
          label: "抽样环节",
          prop: "stepTypeName",
          visible: true,
        },
        {
          label: "大类批次数",
          prop: "cate1Total",
          visible: true,
        },
        {
          label: "细类批次数",
          prop: "total",
          visible: true,
        },
        {
          label: "型号规格",
          prop: "specification",
          visible: true,
        },
        {
          label: "最少抽样数量≥",
          prop: "minSampleQuantity",
          visible: true,
        },
        {
          label: "最少抽样重量>",
          prop: "minSampleWeight",
          visible: true,
        },
        {
          label: "最少备样重量>",
          prop: "minBackupWeight",
          visible: true,
        },
        {
          label: "抽样方法及数量",
          prop: "samplingMethod",
          visible: true,
        },
        {
          label: "风险等级",
          prop: "riskLevel",
          visible: true,
        },
        {
          label: "存储条件",
          prop: "storageCondition",
          visible: true,
        },
        {
          label: "产品执行标准",
          prop: "executionStandards",
          visible: true,
        },
        {
          label: "抽检项目",
          prop: "project",
          visible: true,
        },
        {
          label: "备注信息",
          prop: "remarks",
          visible: true,
        },
        {
          label: "常见易混",
          prop: "commonConfusion",
          visible: true,
        },
      ],
    };
  },
  mounted() {
    // 先获取URL参数，准备稍后使用
    this.urlParams = {
      taskSource: this.$route.query.taskSource,
      classA: this.$route.query.classA,
      classB: this.$route.query.classB,
      taskName: this.$route.query.taskName,
    };
    console.log("保存URL参数:", this.urlParams);

    // 从localStorage加载列显示状态
    this.loadColumnVisibilityFromStorage();

    // 获取数据
    this.getTaskSourceData();
  },
  methods: {
    // 获取任务来源数据
    getTaskSourceData() {
      // 调用API获取任务来源数据
      getClassA().then((res) => {
        console.log("获取到数据:", res.data);
        this.classAListAll = res.data;
        // 转换为任务来源下拉选项
        this.taskSourceList = res.data.map((item) => ({
          label: item.name,
          value: item.name,
        }));

        // 使用保存的URL参数
        this.setFilterValuesFromUrlParams();
      });
    },

    // 表头统一样式
    tableHeaderStyle() {
      return {
        background: "#F8F8F9",
        color: "#ffffff",
        fontWeight: "bold",
        borderBottom: "1px solid #ebeef5",
        padding: "12px 0",
        boxSizing: "border-box",
        position: "relative", // 确保边框显示正常
        zIndex: 1, // 提高层级，防止被其他元素覆盖
      };
    },

    // 从localStorage加载列显示状态
    loadColumnVisibilityFromStorage() {
      try {
        const savedColumnsVisibility = localStorage.getItem(
          "entrustPlanTableColumns"
        );
        if (savedColumnsVisibility) {
          const parsedColumns = JSON.parse(savedColumnsVisibility);
          // 更新当前列显示状态
          this.allTableColumns.forEach((column) => {
            const savedColumn = parsedColumns.find(
              (c) => c.prop === column.prop
            );
            if (savedColumn) {
              column.visible = savedColumn.visible;
            }
          });
          console.log("已从localStorage加载表格列显示状态");
        }
      } catch (error) {
        console.error("加载表格列显示状态出错:", error);
        // 加载出错时使用默认值
      }
    },

    // 保存列显示状态到localStorage
    saveColumnVisibilityToStorage() {
      try {
        const columnsToSave = this.allTableColumns.map((column) => ({
          prop: column.prop,
          visible: column.visible,
        }));
        localStorage.setItem(
          "entrustPlanTableColumns",
          JSON.stringify(columnsToSave)
        );
        console.log("已保存表格列显示状态到localStorage");
      } catch (error) {
        console.error("保存表格列显示状态出错:", error);
      }
    },

    // 检查列是否显示
    getColumnVisible(prop) {
      const column = this.allTableColumns.find(
        (column) => column.prop === prop
      );
      return column ? column.visible : true;
    },

    // 处理列显示状态变化
    handleColumnVisibilityChange() {
      // 保存到localStorage
      this.saveColumnVisibilityToStorage();
    },

    getClassAData() {
      // 只有选择了任务来源时，才获取分类A数据
      if (!this.selectedTaskSource) {
        this.classAList = [];
        return;
      }

      // 不再调用API，直接从已加载的数据中筛选
      const matchedItem = this.classAListAll.find(
        (item) => item.name === this.selectedTaskSource
      );

      if (matchedItem && matchedItem.children) {
        // 转换为标准格式 {label, value}
        this.classAList = matchedItem.children.map((item) => ({
          label: item.name,
          value: item.name,
        }));

        // 如果是通过URL参数设置，继续设置剩余的筛选值
        if (this.isSettingFromUrl && this.urlParams.classA) {
          this.continueSettingFiltersFromUrl();
        } else if (this.isSettingFromUrl) {
          // 如果没有URL参数classA，但是正在从URL设置，使用continueSettingFiltersFromUrl
          // 它会处理默认选择
          this.continueSettingFiltersFromUrl();
        }
      } else {
        this.classAList = [];
        console.log("未找到匹配的任务来源数据或数据结构不正确");
      }
    },

    // 新增方法：从URL参数设置筛选值
    setFilterValuesFromUrlParams() {
      this.isSettingFromUrl = true; // 开始设置，禁用watch清空
      console.log("开始设置筛选值，URL参数:", this.urlParams);

      if (!this.urlParams.taskSource) {
        // 没有任务来源参数时，设置默认值
        this.selectedTaskSource =
          this.taskSourceList.length > 0 ? this.taskSourceList[0].value : null;

        // 获取classA数据
        this.getClassAData();

        // 确保获取到classA数据后才能设置默认的classA
        this.$nextTick(() => {
          // 如果classA数据存在且没有从URL中获取到classA参数，则设置默认值
          if (this.classAList.length > 0 && !this.urlParams.classA) {
            this.selected1 = this.classAList[0].value;
            console.log("默认设置classA:", this.selected1);

            // 触发一次selected1的watch，以便加载classB数据
            this.$nextTick(() => {
              // 模拟手动触发watch
              const selectedHandler = this.$options.watch.selected1.handler;
              if (selectedHandler) {
                selectedHandler.call(this, this.selected1);
              }

              // 加载数据
              this.loadData();
              this.getSelectSampleCateData();

              this.isSettingFromUrl = false; // 结束设置
            });
          } else {
            this.isSettingFromUrl = false; // 结束设置
          }
        });
        return;
      }

      // 设置任务来源
      this.selectedTaskSource = this.urlParams.taskSource;
      console.log("设置任务来源:", this.selectedTaskSource);

      // 获取classA数据，在回调中继续设置其他筛选项
      this.getClassAData();
    },

    // 继续设置其他筛选项
    continueSettingFiltersFromUrl() {
      // 查找匹配的任务来源项
      const taskSourceItem = this.classAListAll.find(
        (item) => item.name === this.selectedTaskSource
      );

      if (!taskSourceItem || !taskSourceItem.children) {
        console.log("找不到匹配的任务来源项或没有子项");
        this.loadData();
        this.getSelectSampleCateData();
        this.isSettingFromUrl = false; // 结束设置
        return;
      }

      // 如果没有classA参数但有子项数据，设置默认值
      if (!this.urlParams.classA && taskSourceItem.children.length > 0) {
        this.selected1 = taskSourceItem.children[0].name;
        console.log("默认设置classA:", this.selected1);
      } else {
        // 设置第一个下拉框值（报送分类A）
        this.selected1 = this.urlParams.classA;
        console.log("设置classA:", this.selected1);
      }

      // 使用$nextTick确保watch执行（如果有）且classBList更新
      this.$nextTick(() => {
        // 查找匹配的classA项
        const classAItem = taskSourceItem.children.find(
          (item) => item.name === this.selected1
        );

        if (!classAItem || !classAItem.children) {
          console.log("找不到匹配的classA项或没有子项");
          this.loadData();
          this.getSelectSampleCateData();
          this.isSettingFromUrl = false; // 结束设置
          return;
        }

        // 设置第二级下拉选项（转换为标准格式）
        this.classBList = classAItem.children.map((item) => ({
          label: item.name,
          value: item.name,
        }));
        console.log("设置classBList:", this.classBList);

        // 如果没有classB参数但有子项数据，设置默认值
        if (!this.urlParams.classB && this.classBList.length > 0) {
          this.selected2 = this.classBList[0].value;
          console.log("默认设置classB:", this.selected2);
        } else if (this.urlParams.classB) {
          // 如果有classB参数，设置第二个下拉框值
          this.selected2 = this.urlParams.classB;
          console.log("设置classB:", this.selected2);
        }

        // 使用$nextTick确保watch执行（如果有）且taskNameList更新
        this.$nextTick(() => {
          // 查找匹配的classB项
          const classBItem = classAItem.children.find(
            (item) => item.name === this.selected2
          );
          if (classBItem && classBItem.children) {
            // 设置第三级下拉选项（转换为标准格式）
            this.taskNameList = classBItem.children.map((item) => ({
              label: item.name,
              value: item.name,
            }));
            console.log("设置taskNameList:", this.taskNameList);

            // 如果有taskName参数，设置第三个下拉框值
            if (this.urlParams.taskName) {
              this.selected3 = this.urlParams.taskName;
              console.log("设置taskName:", this.selected3);
            } else if (this.taskNameList.length > 0) {
              // 如果没有taskName参数但有子项数据，设置默认值
              this.selected3 = this.taskNameList[0].value;
              console.log("默认设置taskName:", this.selected3);
            }
          }

          // 打印最终设置的值并加载数据
          this.finalizeSettingAndLoadData();
        });
      });
    },

    // 新增辅助方法：结束设置并加载数据
    finalizeSettingAndLoadData() {
      console.log("最终设置的筛选值:", {
        selected1: this.selected1,
        selected2: this.selected2,
        selected3: this.selected3,
      });

      // 加载数据
      this.loadData();
      this.getSelectSampleCateData();

      // 确保所有操作完成后再恢复watch
      this.$nextTick(() => {
        this.isSettingFromUrl = false; // 结束设置，恢复watch清空
      });
    },

    getChartData() {},

    initChart() {
      if (this.chartData) {
        this.echartsDomObject = echarts.init(
          document.getElementById("echartsBar")
        );
        const list = [];
        this.chartData.map((item) => list.push(item.samplingNumber));
        this.echartsDomObject.setOption({
          title: {
            text: "抽样环节占比",
            left: "center",
          },
          tooltip: {
            trigger: "item",
          },
          legend: {
            orient: "vertical",
            left: "left",
          },
          series: [
            {
              name: "抽样环节占比",
              type: "pie", // 设置图表类型为饼图
              radius: "55%", // 饼图的半径，也可以是百分比，相对于容器高宽的最小值
              data: this.chartList,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
            },
          ],
        });
      }
    },

    handleQuery() {
      this.page = {
        current: 1,
        size: 10,
        total: 0,
      };
      this.loadData();
    },
    handleReset() {
      this.form = {};
      this.handleQuery();
    },

    getSelectSampleCateData() {
      console.log("getSelectSampleCateData调用，参数:", {
        taskSource: this.selectedTaskSource,
        classA: this.selected1,
        classB: this.selected2,
        taskName: this.selected3,
      });

      // 如果未选择任务来源，则不发起请求
      if (!this.selectedTaskSource) {
        this.tableList = [];
        this.page.total = 0;
        return;
      }

      const params = {
        current: this.page.current,
        size: this.page.size,
        taskSource: this.selectedTaskSource,
        classA: this.selected1,
        classB: this.selected2,
        taskName: this.selected3,
      };
      selectSampleCateData(params).then((res) => {
        this.tableList = res.data.records;
        this.isDeployment =
          (!res.data.records.length || !!res.data.records[0].deployStatus) &&
          this.selected3;
        this.page.total = parseInt(res.data.total) || 0;
      });
    },
    loadData() {
      // 如果未选择任务来源，则不发起请求
      if (!this.selectedTaskSource) {
        this.chartList = [];
        this.initChart();
        return;
      }

      if (!this.selected1) return;

      console.log("loadData调用，参数:", {
        taskSource: this.selectedTaskSource,
        scbNameA: this.selected1,
        scbNameB: this.selected2,
        taskName: this.selected3,
      });

      const params = {
        taskSource: this.selectedTaskSource,
        scbNameA: this.selected1,
        scbNameB: this.selected2,
        taskName: this.selected3,
      };
      planCount(params).then((res) => {
        this.boxMsg = res.data;
        let proportionOfSamplingList = [];
        res.data.proportionOfSamplingList.map((item) => {
          if (item.stepType == 0) {
            proportionOfSamplingList.push({
              value: item.account,
              name: "生产",
            });
          }
          if (item.stepType == 1) {
            proportionOfSamplingList.push({
              value: item.account,
              name: "流通",
            });
          }
          if (item.stepType == 2) {
            proportionOfSamplingList.push({
              value: item.account,
              name: "餐饮",
            });
          }
        });
        this.chartList = proportionOfSamplingList;
        this.initChart();
      });
    },
    addView() {
      this.$router.push({ path: "/samplePlan/entrustAdd" });
    },
    // 导出方法
    exportData() {
      // 显示加载提示
      this.$message.info("导出数据处理中，请稍候...");

      // 构建参数（与获取表格数据参数类似，但不需要分页参数）
      const params = {
        taskSource: this.selectedTaskSource,
        classA: this.selected1,
        classB: this.selected2,
        taskName: this.selected3,
      };

      // 过滤掉空值
      const queryParams = {};
      Object.keys(params).forEach((key) => {
        if (
          params[key] !== null &&
          params[key] !== undefined &&
          params[key] !== ""
        ) {
          queryParams[key] = params[key];
        }
      });

      // 从环境变量获取API基础URL
      const baseURL = process.env.VUE_APP_BASE_API;

      // 使用axios直接请求二进制数据
      axios({
        method: "get",
        url: `${baseURL}/sampleTask/export/excel`,
        params: queryParams,
        responseType: "blob", // 指定响应类型为blob
        headers: {
          Authorization: "Bearer " + getToken(),
        },
      })
        .then((response) => {
          // 获取文件名
          let filename = "抽样计划数据.xlsx";
          const contentDisposition = response.headers["content-disposition"];
          if (contentDisposition) {
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(contentDisposition);
            if (matches != null && matches[1]) {
              filename = matches[1].replace(/['"]/g, "");
            }
          }

          // 创建Blob对象
          const blob = new Blob([response.data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });

          // 创建下载链接
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = decodeURIComponent(filename);
          link.style.display = "none";
          document.body.appendChild(link);

          // 触发下载
          link.click();

          // 清理
          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);

          this.$message.success("导出成功");
        })
        .catch((error) => {
          console.error("导出失败", error);
          this.$message.error("导出失败，请稍后重试");
        });
    },
    // 导入方法
    importData() {
      console.log("导入方法");
    },
    // 编辑计划方法占位，需根据实际需求实现
    editPlan() {
      // TODO: 编辑计划
      this.$router.push({
        path: "/samplePlan/entrustEdit",
        query: {
          taskSource: this.selectedTaskSource,
          classA: this.selected1,
          classB: this.selected2,
          taskName: this.selected3,
        },
      });
    },
    // 第四个按钮方法占位，可按需修改
    otherFunction() {
      console.log("执行其他功能操作");
    },
    handleSizeChange(newSize) {
      this.page.size = newSize;
      this.getSelectSampleCateData();
    },
    handleCurrentChange(newPage) {
      this.page.current = newPage;
      this.getSelectSampleCateData();
    },
    // 部署计划
    handleDeploymentPlan() {
      this.loading2 = true;
      const params = {
        taskSource: this.selectedTaskSource,
        classA: this.selected1,
        classB: this.selected2,
        taskName: this.selected3,
      };
      deploymentPlan(params)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success("部署成功");
            this.isDeployment = true; // 设置部署标识为true
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading2 = false;
        });
    },
    // 删除计划
    handleDelPlan() {
      this.$confirm("确定要删除该计划吗？", "删除计划", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.loading3 = true;
        // 删除逻辑
        const params = {
          taskSource: this.selectedTaskSource,
          classA: this.selected1,
          classB: this.selected2,
          taskName: this.selected3,
        };
        delPlan(params)
          .then((res) => {
            if (res.code === 200) {
              this.$message.success("删除成功");
              // 修改参数，防止刷新页面还请求到已经删除的数据
              this.$router.replace({
                path: "/samplePlan/entrustNew",
                query: {
                  activeTab: "1", // 传递参数指示应该激活第二个标签页
                  taskSource: this.selectedTaskSource,
                  classA: this.selected1,
                  classB: this.selected2,
                },
              });
              this.taskNameList = this.taskNameList.filter(
                (item) => item.value !== this.selected3
              );
              this.selected3 = "";
              this.loadData();
              this.getSelectSampleCateData();
            }
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            this.loading3 = false;
          });
      });
    },
    selectAllColumns() {
      this.allTableColumns.forEach((column) => (column.visible = true));
      this.handleColumnVisibilityChange();
    },
    unselectAllColumns() {
      this.allTableColumns.forEach((column) => (column.visible = false));
      this.handleColumnVisibilityChange();
    },
  },

  watch: {
    selectedTaskSource: {
      handler(newVal) {
        console.log(
          "watch selectedTaskSource:",
          newVal,
          "isSettingFromUrl:",
          this.isSettingFromUrl
        );

        // 只有在非URL设置时才清空下级选择和获取数据
        if (!this.isSettingFromUrl) {
          console.log("watch selectedTaskSource 清空所有下级选择");
          this.selected1 = "";
          this.selected2 = "";
          this.selected3 = "";
          this.classAList = [];
          this.classBList = [];
          this.taskNameList = [];

          // 当选择了任务来源时，获取classA数据
          if (newVal) {
            this.getClassAData();
          } else {
            // 未选择任务来源，清空数据
            this.loadData();
            this.getSelectSampleCateData();
          }
        }
      },
      immediate: false,
    },

    selected1: {
      handler(newVal) {
        console.log(
          "watch selected1:",
          newVal,
          "isSettingFromUrl:",
          this.isSettingFromUrl
        );

        if (!this.selectedTaskSource) return;

        // 先找到对应的任务来源项
        const taskSourceItem = this.classAListAll.find(
          (item) => item.name === this.selectedTaskSource
        );

        if (!taskSourceItem || !taskSourceItem.children) return;

        // 在任务来源的子项中查找匹配的classA项
        const matchedItem = taskSourceItem.children.find(
          (item) => item.name === newVal
        );

        if (matchedItem && matchedItem.children) {
          // 若找到匹配项且存在 children 属性，则将其赋值给 classBList（转换为标准格式）
          this.classBList = matchedItem.children.map((item) => ({
            label: item.name,
            value: item.name,
          }));
          // 只有在非URL设置时才清空
          if (!this.isSettingFromUrl) {
            console.log("watch selected1 清空 selected2 和 selected3");
            this.selected2 = "";
            this.selected3 = "";
            this.loadData();
            this.getSelectSampleCateData();
          }
        } else {
          // 未找到则清空 classBList
          this.classBList = [];
          // 只有在非URL设置时才清空
          if (!this.isSettingFromUrl) {
            console.log("watch selected1 (未找到) 清空 selected2 和 selected3");
            this.selected2 = "";
            this.selected3 = "";
          }
        }
      },
      immediate: false, // 组件加载时立即执行一次 handler
    },
    selected2: {
      handler(newVal) {
        console.log(
          "watch selected2:",
          newVal,
          "isSettingFromUrl:",
          this.isSettingFromUrl
        );

        if (!this.selectedTaskSource || !this.selected1) return;

        // 先找到对应的任务来源项
        const taskSourceItem = this.classAListAll.find(
          (item) => item.name === this.selectedTaskSource
        );

        if (!taskSourceItem || !taskSourceItem.children) return;

        // 在任务来源的子项中查找匹配的classA项
        const classAItem = taskSourceItem.children.find(
          (item) => item.name === this.selected1
        );

        if (!classAItem || !classAItem.children) return;

        // 查找匹配的classB项
        const matchedChild = classAItem.children.find(
          (child) => child.name === newVal
        );

        if (matchedChild && matchedChild.children) {
          // 转换为标准格式
          this.taskNameList = matchedChild.children.map((item) => ({
            label: item.name,
            value: item.name,
          }));

          // 只有在非URL设置时才清空
          if (!this.isSettingFromUrl) {
            console.log("watch selected2 清空 selected3");
            this.selected3 = ""; // 清空第三个下拉选择
          }
        } else {
          this.taskNameList = [];
          // 只有在非URL设置时才清空
          if (!this.isSettingFromUrl) {
            console.log("watch selected2 (未找到) 清空 selected3");
            this.selected3 = ""; // 当没有找到匹配的子项时也要清空第三个下拉选择
          }
        }

        // 只有在非URL设置时才加载数据
        if (!this.isSettingFromUrl) {
          this.loadData();
          this.getSelectSampleCateData();
        }
      },
      immediate: false, // 组件加载时立即执行一次 handler
    },
    selected3: {
      handler(newVal) {
        console.log("watch selected3:", newVal);
        this.loadData();
        this.getSelectSampleCateData();
      },
      immediate: false, // 组件加载时立即执行一次 handler
    },
  },
};
</script>

<style lang="scss" scoped>
.el-date-editor .el-range-separator {
  width: 24px !important;
}

.select-waraper {
  width: 200px;
  margin-right: 10px;
}

.msg-box {
  display: flex;
  margin-top: 20px;
}

.msg-left {
  width: 60%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.msg-card {
  position: relative;
  width: 40%;
  height: 120px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  margin-right: 10%;
  justify-content: space-between;
  flex-wrap: wrap;
  .num {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
  .txt-1 {
    font-size: 14px;
    color: #666;
    margin-top: 10px;
    .num-1 {
      color: #67c23a;
    }
  }
  .icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 40px;
    color: #409eff;
  }
  .red {
    color: #f8f8f9;
  }
}

.msg-right {
  height: 300px;
  width: 30%;
  background-color: #fff;
  border-radius: 5px;
}
#echartsBar {
  width: 100%;
  height: 100%;
}
.items-center {
  display: flex;
  align-items: center;
}

.table-container {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 20px;
}

.column-selector {
  padding: 10px;
}

.column-item {
  margin-bottom: 10px;
}

.column-selector-buttons {
  margin-bottom: 10px;
}

.btn-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.settings-button {
  margin-left: auto;
}

.settings-icon {
  font-size: 18px;
}

/* 修复固定列样式问题 - 增强版 */
/deep/ .el-table__fixed {
  th.is-leaf {
    background-color: #f8f8f9 !important;
    color: #515a6e !important;
    position: relative;
    top: -1px;
  }
}

/deep/ .el-table__header .is-center {
  text-align: center !important;
}

/deep/ .el-table__fixed-left::before,
/deep/ .el-table__fixed-right::before {
  background-color: transparent !important;
}

/* 确保序号列样式正确显示 */
/deep/ .custom-table {
  .el-table__header-wrapper .el-table__header .el-table__cell.is-leaf {
    &.index-column {
      background-color: #f8f8f9 !important;
    }
  }

  .el-table__fixed-header-wrapper
    .el-table__header
    th.el-table__cell:first-child {
    background-color: #f8f8f9 !important;
    // color: #ffffff !important;
  }
}

/* 移除Element UI默认的fixed列分隔线 */
/deep/ .el-table::before,
/deep/ .el-table__fixed::before,
/deep/ .el-table__fixed-right::before {
  height: 0 !important;
  display: none !important;
}

/* 保留之前添加的样式... */
</style>
