<template>
  <div class="wrapper">
    <el-form
      :model="form"
      ref="form"
      :rules="rules"
      label-width="180px"
      label-position="right"
      size="small"
      class="dialog-form"
    >
      <el-row>
        <!-- 使用 el-row 包裹，设置列间距为 20 -->
        <el-col :span="12">
          <el-form-item class="item" label="报送分类A: " prop="classA">
            <el-select
              v-model="form.classA"
              filterable
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in classAList"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="item" label="报送分类B: " prop="classB">
            <el-select
              :disabled="!form.classA"
              v-model="form.classB"
              filterable
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in classBList"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!-- 使用 el-row 包裹，设置列间距为 20 -->
        <el-col :span="12">
          <el-form-item class="item" label="任务名称: " prop="taskName">
            <el-input v-model="form.taskName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="item" label="计划抽样完成日期：">
            <el-date-picker
              v-model="form.plannedCompletionTime"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item class="item" label="环节选择: " prop="link">
            <el-checkbox-group v-model="form.link">
              <el-checkbox label="生产环节"></el-checkbox>
              <el-checkbox label="流通环节"></el-checkbox>
              <el-checkbox label="餐饮环节"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            class="item"
            label="任务执行区域: "
            prop="executionArea"
          >
            <el-input v-model="form.executionArea"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item class="item d-flex" label="计划批次数量: " prop="number1">
        <el-input
          class="input-w"
          type="number"
          :disabled="!form.link.includes('生产环节')"
          v-model="form.num1"
        ></el-input>
        <el-input
          class="input-w"
          type="number"
          :disabled="!form.link.includes('流通环节')"
          v-model="form.num2"
        ></el-input>
        <el-input
          class="input-w"
          type="number"
          :disabled="!form.link.includes('餐饮环节')"
          v-model="form.num3"
        ></el-input>
      </el-form-item>
    </el-form>

    <div v-if="form.link.length > 0" class="title-1">
      计划详情
      <span
        style="
          margin-left: 15px;
          font-weight: normal;
          font-size: 14px;
          color: #606266;
        "
      >
        (总计划批次：{{ totalExistingBatches?.existTotal || 0 }}/{{
          totalExistingBatches?.planTotal || 0
        }})
        <span v-if="isPlanFull" style="color: #f56c6c; margin-left: 5px">
          计划数已满
        </span>
      </span>
    </div>
    <el-tabs
      v-if="form.link.length > 0"
      v-model="activeName"
      @tab-click="handleClick"
    >
      <el-tab-pane
        v-for="(item, key) in form.link"
        :label="item"
        :name="item"
        :key="key"
      >
        <el-form
          :model="'form' + key"
          ref='"form"+key'
          label-width="180px"
          label-position="right"
          size="small"
          class="dialog-form"
        >
          <el-form-item class="item" label="食品大类：" prop="newscbname">
            <el-cascader
              style="width: 100%"
              v-model="tabFormList[key]"
              :show-all-levels="true"
              popper-class="first-no-check-cascader"
              filterable
              :options="treeList"
              @expand-change="changeSourceType"
              :props="{
                label: 'cateName',
                value: 'cateName',
                children: 'children',
                multiple: true,
              }"
            >
            </el-cascader>
          </el-form-item>
        </el-form>
        <el-table
          v-if="tableData[key].length > 0"
          :data="tableData[key]"
          style="width: 100%"
        >
          <el-table-column prop="cate1" label="食品大类"></el-table-column>
          <el-table-column prop="cate2" label="食品亚类"></el-table-column>
          <el-table-column prop="cate3" label="食品次亚类"></el-table-column>
          <el-table-column prop="cate4" label="食品细类"></el-table-column>
          <el-table-column label="细化批次数" width="200">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.total"
                :min="1"
                @change="handleTotalChange($event, scope.row)"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="大类批次数" prop="cate1Total">
            <template #default="scope">
              <span>{{ calculateCate1Total(scope.row.cate1) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <div class="d-flex-btn">
      <el-button type="default" @click="handleCancel">取消</el-button>
      <el-button
        v-if="!isEdit"
        type="primary"
        @click="handleSave"
        :disabled="isPlanFull"
        >新增</el-button
      >
      <el-button
        v-if="isEdit"
        type="primary"
        @click="handleSave"
        :disabled="isPlanFull"
        >修改</el-button
      >
      <el-button
        type="success"
        @click="handleSaveAndDeploy"
        :disabled="isPlanFull"
        >保存并部署</el-button
      >
    </div>
  </div>
</template>

<script>
import {
  deleteXrHny,
  findTreeListAll,
  selectXrHnyList,
} from "@/api/modules/entrust.js";
import editDialog from "./edit";
import insertDialog from "./insert";
import dayjs from "dayjs";
import {
  addPlan,
  getClassA,
  getInfo,
  editPlan,
  getTotalBatchCount,
  deploymentPlan,
} from "@/api/modules/entrustNew.js";

export default {
  components: { insertDialog, editDialog },
  data() {
    return {
      treeList: [],
      columns: [
        {
          label: "报送分类A",
          prop: "newscbname",
        },
        {
          label: "报送分类B",
          prop: "scbname",
        },
        {
          label: "年份",
          prop: "year",
        },
        {
          label: "计划抽样完成日期",
          prop: "planFinishDate",
        },
        {
          label: "报告出具日期",
          prop: "testDate",
        },
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10,
      },
      echartsDomObject: null,
      tableList: [],
      form: {
        scbname: "",
        newscbname: [],
        plannedCompletionTime: "",
        reportTime: "",
        year: "",
        link: [],
        classA: "",
        classB: "",
        taskName: "",
        num1: "",
        num2: "",
        num3: "",
        executionArea: "",
      },

      rules: {
        classB: [
          { required: true, message: "请输入报送分类B", trigger: "blur" },
        ],
        classA: [
          { required: true, message: "请输入报送分类A", trigger: "blur" },
        ],
        taskName: [
          { required: true, message: "请选择任务名称", trigger: "change" },
        ],
        link: [{ required: true, message: "请选择环节", trigger: "blur" }],
        // number1: [
        // { required: true, message: '请输入计划批次数量', trigger: 'blur' }
        // ]
      },
      classBList: [],
      cascaderTag: [], // 暂存点击时已有的tag
      classAList: [],
      classAListAll: [],
      activeName: "",
      tabFormList: [[], [], []],
      tableData: [[], [], []],
      isEdit: false, // 是否是编辑状态
      totalExistingBatches: 0, // 新增：存储已存在的总批次数
    };
  },
  computed: {
    /**
     * 判断计划是否已满，用于禁用按钮
     */
    isPlanFull() {
      if (!this.totalExistingBatches) return false;

      // 如果已存在的批次数等于计划总数，则计划已满
      return (
        this.totalExistingBatches.existTotal >=
        this.totalExistingBatches.planTotal
      );
    },
  },
  mounted() {
    this.findTreeListAll();
    this.getClassAData();

    // 判断路由 URL 是否有 classA 的值
    const { classA, classB, taskName } = this.$route.query;
    if (classA) {
      const params = {
        classA: classA, // 传递 classA 的值给后端
        classB: classB, // 传递 classA 的值给后端
        taskName: taskName, // 传递 classA 的值给后端
      };
      this.getInfoData(params);
      this.isEdit = true; // 设置为编辑状态
    }
  },

  watch: {
    "form.classA": {
      handler(newVal) {
        // 遍历 classAListAll 查找匹配项
        const matchedItem = this.classAListAll.find(
          (item) => item.name === newVal
        );
        if (matchedItem && matchedItem.children) {
          // 若找到匹配项且存在 children 属性，则将其赋值给 classBList（转换为对象数组）
          this.classBList = matchedItem.children.map((item) => ({
            label: item.name,
            value: item.name,
          }));
          // 清空 classB 和 totalExistingBatches
          this.form.classB = "";
          this.totalExistingBatches = 0;
        } else {
          // 未找到则清空 classBList
          this.classBList = [];
          this.form.classB = "";
          this.totalExistingBatches = 0;
        }
      },
    },

    "form.classB": {
      handler(newVal) {
        if (this.form.taskSource && this.form.classA && newVal) {
          this.fetchTotalExistingBatches();
        } else {
          this.totalExistingBatches = 0; // 如果任务来源、A 或 B 任一为空，清零
        }
      },
      immediate: false,
    },

    "form.link": {
      handler(newVal) {
        if (newVal.length > 0) {
          this.activeName = newVal[0];
        }
      },
      deep: true, // 深度监听数组变化
      immediate: true, // 组件加载时立即执行一次 handler
    },
    tabFormList: {
      handler(newVal) {
        console.log(this.tabFormList, 33333);

        if (this.tabFormList[0].length > 0) {
          this.tableData[0] = [];

          this.tabFormList[0].forEach((e) => {
            this.tableData[0].push({
              cate1: e[0],
              cate2: e[1],
              cate3: e[2],
              cate4: e[3],
              total: 0,
              cate1Total: 0,
            });
          });
        }

        if (this.tabFormList[1].length > 0) {
          this.tableData[1] = [];

          this.tabFormList[1].forEach((e) => {
            this.tableData[1].push({
              cate1: e[0],
              cate2: e[1],
              cate3: e[2],
              cate4: e[3],
              total: 0,
              cate1Total: 0,
            });
          });
        }
        if (this.tabFormList[2].length > 0) {
          this.tableData[2] = [];
          this.tabFormList[2].forEach((e) => {
            this.tableData[2].push({
              cate1: e[0],
              cate2: e[1],
              cate3: e[2],
              cate4: e[3],
              total: 0,
              cate1Total: 0,
            });
          });
        }
      },
      deep: true, // 深度监听数组变化
      immediate: false, // 组件加载时立即执行一次 handler
    },
  },

  methods: {
    getInfoData(params) {
      getInfo(params).then((res) => {
        console.log(res.data, 33333);
        this.form = {
          ...this.form,
          classA: res.data.classA,
          classB: res.data.classB,
          taskName: res.data.taskName,
          plannedCompletionTime: res.data.plannedCompletionTime,
        };
        const arr1 = [];
        const arr2 = [];
        const arr3 = [];

        const arr4 = [];
        const arr5 = [];
        const arr6 = [];
        res.data.sampleCateDetailList?.forEach((item, index) => {
          if (item.stepType == 0 && !this.form.link.includes("生产环节")) {
            this.form.num1 = item.stepPlanTotal;
            this.form.link.push("生产环节");
          }
          console.log(
            item.stepType == 1 && !this.form.link.includes("流通环节")
          );
          if (item.stepType == 1 && !this.form.link.includes("流通环节")) {
            this.form.num2 = item.stepPlanTotal;
            this.form.link.push("流通环节");
          }
          if (item.stepType == 2 && !this.form.link.includes("餐饮环节")) {
            this.form.num3 = item.stepPlanTotal;
            this.form.link.push("餐饮环节");
          }

          if (item.stepType == 0) {
            arr1.push(item);
            arr4.push([item.cate1, item.cate2, item.cate3, item.cate4]);
          }
          if (item.stepType == 1) {
            arr2.push(item);
            arr5.push([item.cate1, item.cate2, item.cate3, item.cate4]);
          }
          if (item.stepType == 2) {
            arr3.push(item);
            arr6.push([item.cate1, item.cate2, item.cate3, item.cate4]);
          }
        });
        this.tabFormList = [arr4, arr5, arr6];
        setInterval(() => {
          this.tableData = [arr1, arr2, arr3];
        });

        // 在数据回填后，如果需要，手动触发一次批次数获取
        if (this.form.taskSource && this.form.classA && this.form.classB) {
          this.fetchTotalExistingBatches();
        }
      });
    },
    getClassAData() {
      getClassA().then((res) => {
        this.classAListAll = res.data;
        // 转换为对象数组
        this.classAList = res.data.map((item) => ({
          label: item.name,
          value: item.name,
        }));

        // 处理编辑状态下的初始值
        if (this.isEdit && this.form.classA) {
          // 编辑模式，如果 form.classA 已有值（来自 getInfoData），需要手动触发一次 B 列表更新
          const matchedItem = this.classAListAll.find(
            (item) => item.name === this.form.classA
          );
          if (matchedItem && matchedItem.children) {
            this.classBList = matchedItem.children.map((item) => ({
              label: item.name,
              value: item.name,
            }));
            // 如果 B 也有值，触发获取批次数
            if (this.form.classB) {
              this.fetchTotalExistingBatches();
            }
          }
        }
      });
    },

    changeSourceType(selectItem) {
      // 一级分类只能选一个，如果一级分类修改了，清空已有值
      if (!this.cascaderTag.includes(selectItem[0])) {
        this.newscbname = [];
      }
      this.cascaderTag = selectItem;
    },

    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10,
      };
      this.loadData();
    },
    handleReset() {
      this.form = {};
      this.handleQuery();
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size,
      };
      if (this.form.planFinishDate && this.form.planFinishDate.length) {
        data.startDate = dayjs(this.form.planFinishDate[0]).format(
          "YYYY-MM-DD"
        );
        data.endDate = dayjs(this.form.planFinishDate[1]).format("YYYY-MM-DD");
        delete data.planFinishDate;
      }
      selectXrHnyList(data).then((res) => {
        this.list = res.data.records;
        this.page.total = res.data.total;
      });
    },
    findTreeListAll() {
      findTreeListAll().then((res) => {
        this.treeList = this.getTypeList(res.data);
      });
    },

    getTypeList(listData) {
      listData.forEach((items) => {
        if (items.children && items.children.length > 0) {
          this.getTypeList(items.children);
        } else {
          items.children = undefined;
        }
      });
      return listData;
    },

    calculateCate1Total(cate1) {
      // 计算同一大类下所有细类批次数的总和
      const currentTab = this.form.link.indexOf(this.activeName);
      if (currentTab === -1) return 0;

      return this.tableData[currentTab]
        .filter((row) => row.cate1 === cate1)
        .reduce((sum, row) => sum + (row.total || 0), 0);
    },

    // 校验所有细类批次数是否都大于0
    validateBatchNumbers() {
      let isValid = true;
      let errorMessage = "";

      this.form.link.forEach((link, index) => {
        if (this.tableData[index]) {
          const hasZeroBatch = this.tableData[index].some(
            (row) => !row.total || row.total <= 0
          );
          if (hasZeroBatch) {
            isValid = false;
            errorMessage = `${link}环节中存在未设置批次数的细类，请检查`;
          }
        }
      });

      if (!isValid) {
        this.$message.error(errorMessage);
      }
      return isValid;
    },

    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid && this.validateBatchNumbers()) {
          // 将嵌套的 tableData 打平成一层列表
          const sampleCateDetailList = [];

          // 环节类型映射
          const stepTypeMap = {
            生产环节: {
              stepType: 0,
              stepTypeName: "生产环节",
              stepPlanTotal: this.form.num1,
            },
            流通环节: {
              stepType: 1,
              stepTypeName: "流通环节",
              stepPlanTotal: this.form.num2,
            },
            餐饮环节: {
              stepType: 2,
              stepTypeName: "餐饮环节",
              stepPlanTotal: this.form.num3,
            },
          };

          // 遍历所有环节的数据并合并到一个列表
          this.form.link.forEach((link, index) => {
            if (this.tableData[index] && this.tableData[index].length > 0) {
              // 计算每个环节中各食品大类的批次数总和
              const cate1TotalMap = {};

              // 第一遍遍历，统计每个大类下细类批次数的总和
              this.tableData[index].forEach((item) => {
                if (!cate1TotalMap[item.cate1]) {
                  cate1TotalMap[item.cate1] = 0;
                }
                cate1TotalMap[item.cate1] += Number(item.total) || 0;
              });

              // 第二遍遍历，构建最终提交的数据，带上正确的 cate1Total
              this.tableData[index].forEach((item) => {
                // 为每一项添加环节类型信息
                sampleCateDetailList.push({
                  ...item,
                  stepType: stepTypeMap[link].stepType,
                  stepTypeName: stepTypeMap[link].stepTypeName,
                  stepPlanTotal: stepTypeMap[link].stepPlanTotal,
                  planFinishDate: this.form.plannedCompletionTime,
                  plannedCompletionTime: this.form.plannedCompletionTime,
                  cate1Total: cate1TotalMap[item.cate1] || 0, // 使用计算好的大类总数
                  executionArea: this.form.executionArea,
                });
              });
            }
          });

          // 构建提交参数
          const params = {
            classA: this.form.classA,
            classB: this.form.classB,
            taskName: this.form.taskName,
            plannedCompletionTime: this.form.plannedCompletionTime,
            link: this.form.link,
            num1: this.form.num1,
            num2: this.form.num2,
            num3: this.form.num3,
            sampleCateDetailList: sampleCateDetailList,
            executionArea: this.form.executionArea,
          };

          if (this.isEdit) {
            // 编辑模式下的保存逻辑
            editPlan(params).then(() => {
              this.$message.success("修改成功");
              // 修改跳转到计划详情页面
              this.$router.push({
                path: "/samplePlan/entrustNew",
                query: {
                  activeTab: "1", // 传递参数指示应该激活第二个标签页
                  classA: this.form.classA,
                  classB: this.form.classB,
                  taskName: this.form.taskName,
                },
              });
            });
          } else {
            // 新增模式下的保存逻辑
            addPlan(params).then(() => {
              this.$message.success("新增成功");
              // 修改跳转到计划详情页面
              this.$router.push({
                path: "/samplePlan/entrustNew",
                query: {
                  activeTab: "1", // 传递参数指示应该激活第二个标签页
                  classA: this.form.classA,
                  classB: this.form.classB,
                  taskName: this.form.taskName,
                },
              });
            });
          }
        }
      });
    },

    handleSaveAndDeploy() {
      this.$refs.form.validate((valid) => {
        if (valid && this.validateBatchNumbers()) {
          // 将嵌套的 tableData 打平成一层列表
          const sampleCateDetailList = [];

          // 环节类型映射
          const stepTypeMap = {
            生产环节: {
              stepType: 0,
              stepTypeName: "生产环节",
              stepPlanTotal: this.form.num1,
            },
            流通环节: {
              stepType: 1,
              stepTypeName: "流通环节",
              stepPlanTotal: this.form.num2,
            },
            餐饮环节: {
              stepType: 2,
              stepTypeName: "餐饮环节",
              stepPlanTotal: this.form.num3,
            },
          };

          // 遍历所有环节的数据并合并到一个列表
          this.form.link.forEach((link, index) => {
            if (this.tableData[index] && this.tableData[index].length > 0) {
              // 计算每个环节中各食品大类的批次数总和
              const cate1TotalMap = {};

              // 第一遍遍历，统计每个大类下细类批次数的总和
              this.tableData[index].forEach((item) => {
                if (!cate1TotalMap[item.cate1]) {
                  cate1TotalMap[item.cate1] = 0;
                }
                cate1TotalMap[item.cate1] += Number(item.total) || 0;
              });

              // 第二遍遍历，构建最终提交的数据，带上正确的 cate1Total
              this.tableData[index].forEach((item) => {
                // 为每一项添加环节类型信息
                sampleCateDetailList.push({
                  ...item,
                  stepType: stepTypeMap[link].stepType,
                  stepTypeName: stepTypeMap[link].stepTypeName,
                  stepPlanTotal: stepTypeMap[link].stepPlanTotal,
                  planFinishDate: this.form.plannedCompletionTime,
                  plannedCompletionTime: this.form.plannedCompletionTime,
                  cate1Total: cate1TotalMap[item.cate1] || 0, // 使用计算好的大类总数
                });
              });
            }
          });

          // 构建提交参数
          const params = {
            classA: this.form.classA,
            classB: this.form.classB,
            taskName: this.form.taskName,
            plannedCompletionTime: this.form.plannedCompletionTime,
            link: this.form.link,
            num1: this.form.num1,
            num2: this.form.num2,
            num3: this.form.num3,
            sampleCateDetailList: sampleCateDetailList,
            // 添加部署标志
            isDeploy: true,
          };

          if (this.isEdit) {
            // 编辑模式下的保存并部署逻辑
            editPlan(params).then(() => {
              this.$message.success("修改并部署成功");
              // 修改跳转到计划详情页面
              this.$router.push({
                path: "/samplePlan/entrustNew",
                query: {
                  activeTab: "1", // 传递参数指示应该激活第二个标签页
                  classA: this.form.classA,
                  classB: this.form.classB,
                  taskName: this.form.taskName,
                },
              });
            });
          } else {
            // 新增模式下的保存并部署逻辑
            addPlan(params).then(() => {
              const params2 = {
                classA: this.form.classA,
                classB: this.form.classB,
                taskName: this.form.taskName,
              };
              deploymentPlan(params2).then((res2) => {
                if (res2.code === 200) {
                  this.$message.success("新增并部署成功");
                  // 修改跳转到计划详情页面
                  this.$router.push({
                    path: "/samplePlan/entrustNew",
                    query: {
                      activeTab: "1", // 传递参数指示应该激活第二个标签页
                      classA: this.form.classA,
                      classB: this.form.classB,
                      taskName: this.form.taskName,
                    },
                  });
                } else {
                  this.$message.error(res2.msg);
                }
              });
            });
          }
        }
      });
    },

    handleTotalChange(value, row) {
      // 处理细化批次数变化时的逻辑
      console.log("细化批次数变化:", value, row);
    },

    handleCancel() {
      // 跳转到计划列表页面，并选择第二个标签页
      this.$router.push({
        path: "/samplePlan/entrustNew",
        query: {
          activeTab: "1", // 传递参数指示应该激活第二个标签页
        },
      });
    },

    // 获取已存在的总批次数
    fetchTotalExistingBatches() {
      const params = {
        taskSource: this.form.taskSource,
        classA: this.form.classA,
        classB: this.form.classB,
      };
      getTotalBatchCount(params)
        .then((res) => {
          if (res.code === 200) {
            this.totalExistingBatches = res.data || 0;
          } else {
            this.$message.error("获取已存在批次数失败：" + res.msg);
            this.totalExistingBatches = 0; // 出错时清零
          }
        })
        .catch((err) => {
          console.error("获取已存在批次数失败:", err);
          this.$message.error("获取已存在批次数失败，请检查网络连接");
          this.totalExistingBatches = 0; // 出错时清零
        });
    },

    // 新增方法：计算当前设置的总计划批次
    calculateTotalPlanBatches() {
      let total = 0;
      if (this.form.link.includes("生产环节") && this.form.num1) {
        total += Number(this.form.num1) || 0;
      }
      if (this.form.link.includes("流通环节") && this.form.num2) {
        total += Number(this.form.num2) || 0;
      }
      if (this.form.link.includes("餐饮环节") && this.form.num3) {
        total += Number(this.form.num3) || 0;
      }
      return total;
    },
  },
};
</script>

<style lang="scss">
.el-date-editor .el-range-separator {
  width: 24px !important;
}

.select-waraper {
  width: 200px;
  margin-right: 10px;
}

.d-flex {
  display: flex;
  flex-wrap: wrap;
  .el-form-item__content {
    margin-left: 0 !important;
  }
}

.input-w {
  width: 90px;
  margin-right: 10px;
}

.d-flex-btn {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.title-1 {
  font-weight: bold;
  margin: 20px 0;
  font-size: 18px;
  span {
    font-size: 14px;
    font-weight: normal;
  }
}

.wrapper {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
}

/* 全局级联选择器样式修复 */
.el-cascader {
  line-height: normal;
  height: auto;
  display: block;
  width: 100%;
}

.el-cascader__dropdown {
  min-width: 200px !important;
}

.el-cascader-menus {
  min-width: 200px !important;
}

.el-cascader-menu {
  min-width: 200px !important;
  max-height: 300px !important;
}
</style>
