<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='用户昵称：'>
          <el-input v-model='form.nickName'></el-input>
        </el-form-item>
        <el-form-item label='真实姓名：'>
          <el-input v-model='form.realName'></el-input>
        </el-form-item>
        <el-form-item label='联系方式：'>
          <el-input v-model='form.telephone'></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot='bottom-options'>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' icon='el-icon-view' @click='details(scope.row)'>查看详情</el-button>
      </div>
    </simple-table>
  </simple-list-page>
</template>

<script>
import {pageList,messageDelete } from '@/api/modules/feedback.js'
export default {
  data() {
    return {
      typeList: [],
      dialogVisible: false,
      dialogTypeVisible: false,
      form: {},
      columns: [
        {
          label: '反馈内容',
          prop: 'feedbackContent',
          showOverflowTooltip: true
        },
        {
          label: '用户昵称',
          prop: 'nickName'
        },
        {
          label: '真实姓名',
          prop: 'realName'
        },
        {
          label: '联系方式',
          prop: 'telephone'
        },
        {
          label: '反馈时间',
          prop: 'feedbackTime'
        }
      ],
      editorOption: {} ,
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  created() {
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      let data={
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleQuery() {
      this.page={
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    // 编辑
    details(row) {
      this.$router.push({
        path: '/sys/feedback/detail',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
