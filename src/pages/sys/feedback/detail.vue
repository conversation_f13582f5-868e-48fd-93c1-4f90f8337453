<template>
  <simple-page title='查看详情' @back='handleBack'>
    <simple-line-card header='基本信息'>
      <el-form label-width="120px" class="demo-ruleForm" v-loading="loading">
        <el-form-item label="反馈内容：">
          {{detail.feedbackContent}}
        </el-form-item>

        <el-form-item label="反馈图片：">
          <div v-if="detail.urls.length<=0">暂无数据</div>
          <div v-else class="block" v-for="item in detail.urls" :key="item">
            <el-image style="width: 100px; height: 100px" :src="item" :preview-src-list="detail.urls" :fit="fit"></el-image>
          </div>
        </el-form-item>
        <el-form-item label="用户昵称：">
          {{detail.nickName}}
        </el-form-item>

        <el-form-item label="真实姓名：">
          {{detail.realName}}
        </el-form-item>


        <el-form-item label="联系方式：">
          {{detail.telephone}}
        </el-form-item>


        <el-form-item label="反馈时间：">
          {{detail.feedbackTime}}
        </el-form-item>
      </el-form>
    </simple-line-card>
  </simple-page>
</template>

<script>
import { detail } from '@/api/modules/feedback.js'

export default {
  data() {
    return {
      id: '',
      loading: false,
      detail: {}
    }
  },
  mounted() {
    this.id = this.$route.query.id
    this.loadDetail()
  },
  methods: {
    loadDetail() {
      this.loading = true
      detail({ id: this.id }).then(res => {

        this.detail = res.data
        this.loading = false

        const list = this.detail.feedbackPic ? this.detail.feedbackPic.split(';') : []
        this.detail.urls = [];
        for (var i = 0; i < list.length; i++) {
          this.detail.urls.push(list[i])
        }
      }).catch(() => {
        this.loading = false
      })
    },
    handleBack() {
    }
  }
}
</script>

<style lang="scss" scoped></style>
