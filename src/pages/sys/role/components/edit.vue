<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="500px"
    :close-on-click-modal="false"
    @close="closeDialog"
    destroy-on-close
  >
    <el-form
      :model="employeeForm"
      :rules="rules"
      ref="employeeFormRef"
      label-width="100px"
      autocomplete="off"
    >
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="employeeForm.roleName"
          placeholder="请输入角色名称"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="角色标识" prop="roleKey">
        <el-input
          v-model="employeeForm.roleKey"
          placeholder="请输入角色标识"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input
          v-model="employeeForm.remark"
          placeholder="请输入角色描述"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="状态" prop="status" label-width="100px">
        <el-radio-group v-model="employeeForm.status" class="w-full">
          <el-radio label="0">可用</el-radio>
          <el-radio label="1">禁用</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="菜单">
        <el-tree
          ref="menuTree"
          v-loading="loading"
          :data="menusData"
          :props="menuProps"
          show-checkbox
          node-key="id"
          @check="handleCheckMenu"
          :default-checked-keys="defaultMenus"
        >
        </el-tree>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="submitLoading"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { roleEdit } from "@/api/modules/sysRole";
import { menuPageList } from "@/api/modules/sysMenu.js";

export default {
  name: "EmployeeEdit",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dialogTitle: {
      type: String,
      default: "添加角色",
    },
    employeeFormData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      employeeForm: {
        id: "",
        status: "0",
      },
      rules: {
        roleName: [
          { required: true, message: "请输入角色名称", trigger: "blur" },
        ],
        roleKey: [
          { required: true, message: "请输入角色标识", trigger: "blur" },
        ],
      },
      roleOptions: [],
      roleOptionsSelect: [],
      passwordVisible: false,
      submitLoading: false,
      menuProps: {
        label: "menuName",
        children: "children",
      },
      menusData: [],
      menus: [], // 菜单集合
      loading: false,
      defaultMenus: [], // 默认选中的菜单
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.$nextTick(() => {
          if (val) {
            this.employeeForm = this.employeeFormData;
            this.getResourceAllMenuTree();
          }
        });
      },
    },
  },
  methods: {
    closeDialog() {
      this.$parent.dialogVisible = false;
    },
    submitForm() {
      this.$refs.employeeFormRef.validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          const checkMenus = this.$refs.menuTree.getCheckedNodes();
          // 处理菜单（不修改菜单直接点确定，父级菜单不会被获取）
          this.menus = [
            ...new Set([
              ...checkMenus.map((item) => item.id),
              ...checkMenus
                .filter((item) => item.parentId)
                .map((item) => item.parentId),
            ]),
          ];
          const formData = {
            ...JSON.parse(JSON.stringify(this.employeeForm)),
            menuIds: this.menus,
          };
          // 发送请求
          roleEdit(formData)
            .then((res) => {
              if (res.code === 200) {
                this.$message.success(
                  `角色${formData.id ? "修改" : "添加"}成功!`
                );
                this.dialogVisible = false;
                this.$parent.loadData();
                this.$parent.dialogVisible = false;
              } else {
                this.$message.error(res.msg || "操作失败");
              }
            })
            .catch((err) => {
              console.error("失败:", err);
            })
            .finally(() => {
              this.submitLoading = false;
            });
        }
      });
    },
    // 查询所有菜单树
    getResourceAllMenuTree() {
      this.defaultMenus = [];
      this.menus = [];
      let params = { roleId: this.employeeForm.id };
      menuPageList(params)
        .then((res) => {
          this.menusData = res;
          this.getChildren(res);
        })
        .catch((err) => {
          console.error(err);
        });
    },
    // 递归找到最后一层，再判断是否被选中
    getChildren(arr) {
      arr.forEach((item) => {
        if (item.children && item.children.length > 0) {
          this.getChildren(item.children);
        } else if (item.perms === "1") {
          this.defaultMenus.push(item.id);
          this.menus.push(item.id);
        }
      });
    },
    // 勾选菜单
    handleCheckMenu(current, obj) {
      // const { checkedNodes } = obj;
      // this.menus = checkedNodes.filter((item) => item.id).map((key) => key.id);
    },
  },
};
</script>

<style lang="scss" scoped></style>
