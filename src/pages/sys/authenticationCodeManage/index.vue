<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='认证码：'>
          <el-input v-model='form.authenticationCode'></el-input>
        </el-form-item>
      </el-form>
    </div>

    <div slot='bottom-options'>
      <el-button type='primary' size='mini' icon='el-icon-plus' @click='addView'>新增</el-button>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
      <div slot='member' slot-scope="scope">
         <div v-html="scope.row.member == 0 ? '否': '是' "></div>
      </div>
      <div slot='type' slot-scope="scope">
         {{scope.row.type == 0 ? '未使用': scope.row.type == 1 ? '已使用' : '已过期'}}
      </div>
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' icon='el-icon-view' @click='detail(scope.row)'>查看详情</el-button>
      </div>
    </simple-table>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="70%"
      :before-close="handleClose">
        <el-form :model="ruleForm"  :rules="rules" ref="ruleForm" label-width="130px" class="demo-ruleForm">
          <el-form-item label="认证码：" prop="authenticationCode">
               {{ruleForm.authenticationCode}} <el-button type="text" @click="genRandomNum">随机生成</el-button>
           </el-form-item>
           <el-form-item label="是否发放会员" prop="member">
              <el-radio-group v-model="ruleForm.member"  >
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                </el-radio-group>
           </el-form-item>
            <el-form-item label="发放会员天数：" :prop="ruleForm.member==1?'grantDays':''">
              <el-col :span="7">
                <el-input-number v-model="ruleForm.grantDays" size="medium" :min="0" :max="99999999" ></el-input-number>
              </el-col>
              <el-col :span="6" >
                <span >请输入≥0的整数</span>
              </el-col>
            </el-form-item>
            <el-form-item label="使用截止时间：" prop="deadline">
              <el-date-picker
                v-model="ruleForm.deadline"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit" v-loading="loading">确 定</el-button>
      </span>
    </el-dialog>


    <el-dialog
      :title="title"
      :visible.sync="dialogDetailVisible"
      width="70%"
      :before-close="handleClose">
        <el-form :model="ruleForm"  :rules="rules" ref="ruleForm" label-width="130px" class="demo-ruleForm">
          <el-form-item label="认证码：" >
               {{ruleForm.authenticationCode}}
           </el-form-item>
           <el-form-item label="是否发放会员" prop="member">
             {{ruleForm.member == 0 ? '否' : '是'}}
           </el-form-item>
            <el-form-item label="发放会员天数：" >
              {{ruleForm.grantDays}}
            </el-form-item>
            <el-form-item label="使用截止时间：" >
              {{ruleForm.deadline}}
            </el-form-item>
            <el-form-item label="有效期（天）：" >
              {{ruleForm.validDays}}
            </el-form-item>
            <el-form-item label="状态：" >
              {{ruleForm.type == 0 ? '未使用': ruleForm.type == 1 ? '已使用' : '已过期'}}
            </el-form-item>
            <el-form-item label="用户昵称：" >
              {{ruleForm.nickName}}
            </el-form-item>
            <el-form-item label="联系方式：" >
              {{ruleForm.telephone}}
            </el-form-item>
            <el-form-item label="使用时间：" >
              {{ruleForm.useTime}}
            </el-form-item>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDetailVisible = false">取 消</el-button>
      </span>
    </el-dialog>


  </simple-list-page>
</template>

<script>
import {pageList,chickenSoupDelete,detail,add,update,genRandomNum } from '@/api/modules/authenticationCodeManage.js'
export default {
  data() {
    return {
      title: '',
      typeList: [],
      loading: false,
      dialogVisible: false,
      dialogDetailVisible: false,
      form: {},
      columns: [
        {
          label: '认证码',
          prop: 'authenticationCode'
        },
        {
          label: '是否发放会员',
          prop: 'member',
          slot: 'member'
        },
        {
          label: '发放会员天数（天）',
          prop: 'grantDays'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        },
        {
          label: '截止时间',
          prop: 'deadline'
        },
        {
          label: '有效期（天）',
          prop: 'validDays'
        },
        {
          label: '状态',
          prop: 'type',
          slot: 'type'
        }
      ],
      editorOption: {} ,
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      ruleForm: {
        authenticationCode: '',
        member: 1,
        grantDays: undefined,
        deadline: ''
      },
      rules: {
        authenticationCode: [
          { required: true, message: '请生成认证码', trigger: 'blur' }
        ],
        grantDays: [
          { required: true, message: '请输入发放会员天数', trigger: 'blur' }
        ],
        deadline: [
          { required: true, message: '请选择使用截止时间', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      let data={
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleQuery() {
      this.page={
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    genRandomNum() {
      genRandomNum().then(res => {
        this.$set(this.ruleForm, 'authenticationCode', res.data)
      })
    },
    // 提交
    submit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          let request;
          if (this.ruleForm.id) {
            request = update(this.ruleForm)
          } else {
            request = add(this.ruleForm)
          }
          request.then(res => {
            this.loading = false
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.dialogVisible = false
              } else {
                this.$message.error(res.msg)
              }
              this.handleQuery()
          }).catch(() => {
              this.loading = false
          })
        } else {
          // 登录表单校验失败
          this.$message.error('表单校验失败，请检查')
        }
      })
    },
    addView() {
      this.title = "新增认证码"
      this.dialogVisible = true
      this.ruleForm = {
        authenticationCode: '',
        member: 1,
        grantDays: undefined,
        deadline: ''
      }
    },
    handleClose(done) {
      done();
    },
    detail(row) {
      this.title = "查看详情"
      // 获取详情数据
      detail({id: row.id}).then(res => {
          if (+res.code === 200) {
            this.dialogDetailVisible = true
            this.ruleForm = res.data
          } else {
            this.$message.error(res.msg)
          }
      }).catch(() => {
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
