<template>
  <simple-page title='查看详情' @back='handleBack'>
    <simple-line-card header='食品分类'>
      <simple-detail-panel :columns='columns' :data='form' :labelWidth='100'></simple-detail-panel>
    </simple-line-card>
  </simple-page>
</template>

<script>
import { fcDetail } from '@/api/modules/foodCategory.js'

export default {
  data() {
    return {
      form: {
        categoryLevel: '',
        categoryName: '',
        parentCategoryName: '',
        sortNum: 0
      },
      pid: 0,
      columns: [
        {
          label: '上级分类',
          prop: 'parentCategoryName'
        },
        {
          label: '分类级别',
          prop: 'categoryLevelName'
        },
        {
          label: '分类名称',
          prop: 'categoryName'
        }
      ]
    }
  },
  mounted() {
    this.id = this.$route.query.id
    this.loadDetail()
  },
  methods: {
    loadDetail() {
      fcDetail({ id: this.id }).then(res => {

        this.form = res.data
        if (this.form.parentId == 0) {
          this.form.parentCategoryName = '无'
        } else {
          fcDetail({ id: this.form.parentId }).then(res => {

            // this.form.parentCategoryName = res.data.categoryName
            this.$set(this.form, 'parentCategoryName', res.data.categoryName)
            console.log(this.form)
          })
        }
      }
      )
    },
    handleBack() {
      // this.$router.back(-1)
    }
  }
}
</script>

<style lang="scss" scoped></style>
