<template>
  <simple-page title='新增食品分类' @back='handleBack' v-loading='loading'>
    <el-form ref='form' :model='form' label-width='80px' :rules='rules'>
      <el-form-item label='分类名称' prop='categoryName'>
        <el-input v-model='form.categoryName'></el-input>
      </el-form-item>
      <el-form-item>
        <el-col>
              <span class='dialog-footer'>
              <el-button @click='back'>取 消</el-button>
              <el-button type='primary' @click='submit'>确 定</el-button>
            </span>
        </el-col>
      </el-form-item>
    </el-form>
  </simple-page>
</template>

<script>
import {fcSave} from '@/api/modules/foodCategory.js'

export default {
  data() {
    return {
      pid:0,
      loading: false,
      form: {
        categoryName: '',
        parentId:0,
        categoryLevel:1
      },
      rules: {
        categoryName: [
          {required: true, message: '分类名称不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  mounted() {
    this.form.parentId = this.$route.query.parentId
    this.form.categoryLevel = this.$route.query.categoryLevel
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          fcSave(this.form).then(rel => {
            this.loading = false
            this.$message.success(rel.msg)
            this.back()
          }).catch((e) => {
            this.$message.error(e.msg)
            this.loading = false
          })
        }
      })
    },
    back(){
      this.$router.back(-1)
    },
    handleBack() {

    }
  }
}
</script>

<style lang="scss" scoped></style>
