<template>
  <simple-page title='编辑食品分类' @back='handleBack' v-loading='loading'>
    <el-form ref='form' :model='form' label-width='80px' :rules='rules'>
      <el-form-item label='分类名称' prop='name'>
        <el-input v-model='form.categoryName'></el-input>
      </el-form-item>
      <el-form-item>
        <el-col>
              <span class='dialog-footer'>
              <el-button @click='back'>取 消</el-button>
              <el-button type='primary' @click='submit'>确 定</el-button>
            </span>
        </el-col>
      </el-form-item>
    </el-form>
  </simple-page>
</template>

<script>
import {fcEdit,fcDetail} from '@/api/modules/foodCategory.js'

export default {
  data() {
    return {
      loading: false,
      form: {
        categoryName: ''
      },
      rules: {
        categoryName: [
          {required: true, message: '分类名称不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  mounted() {
    this.form.id = this.$route.query.id
    this.loadDetail()
  },
  methods: {
    loadDetail() {
      fcDetail({id: this.form.id}).then(res => {
        this.form = res.data
      })
    },
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          fcEdit(this.form).then(rel => {
            this.loading = false
            this.$message.success(rel.msg)
            this.back()
          }).catch((e) => {
            this.$message.error(e.msg)
            this.loading = false
          })
        }
      })
    },
    handleBack() {
      // this.$router.back(-1)
    },
    back() {
      this.$router.back(-1)
    }
  }
}
</script>

<style lang="scss" scoped></style>
