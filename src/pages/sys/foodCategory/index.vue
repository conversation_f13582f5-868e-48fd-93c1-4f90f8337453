<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='食品大类' prop='firstly'>
          <el-select v-model='form.firstly' clearable placeholder='请选择' @change='getSecondCategory'>
            <el-option v-for='item in firstly' :key='item.id' :label='item.categoryName' :value='item.id'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label='食品亚类' prop='second'>
          <el-select v-model='form.second' clearable placeholder='请选择' @change='getThirdlyCategory'>
            <el-option v-for='item in second' :key='item.id' :label='item.categoryName' :value='item.id'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label='食品次亚类' prop='thirdly'>
          <el-select v-model='form.thirdly' clearable placeholder='请选择' @change='getFourthlyCategory'>
            <el-option v-for='item in thirdly' :key='item.id' :label='item.categoryName' :value='item.id'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label='食品细类' prop='fourthly'>
          <el-select v-model='form.fourthly' clearable placeholder='请选择'>
            <el-option v-for='item in fourthly' :key='item.id' :label='item.categoryName' :value='item.id'>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div slot='bottom-options'>
      <el-button type='primary' size='mini' icon='el-icon-plus' @click='addView'>新增</el-button>
      <el-button type='primary' size='mini' icon='el-icon-plus' @click='showWpload'>导入</el-button>
      <el-button v-if='parentId!=0' type='info' size='mini' icon='el-icon-caret-left' @click='backTopo()'>返回顶层</el-button>
    </div>
    <!-- <simple-table :columns='columns' :data='list' :page='page' @load='loadData' :treeProps="{children: 'children', hasChildren: 'hasChildren'}">
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' icon='el-icon-view' @click='detailView(scope.row)'>查看</el-button>
        <el-button type='text' size='mini' icon='el-icon-view' @click='editView(scope.row)'>编辑</el-button>
        <el-button type='text' size='mini' icon='el-icon-view' @click='delView(scope.row)'>删除</el-button>
        <el-button type='text' size='mini' icon='el-icon-view' @click='loadChildren(scope.row.id)'>操作下级</el-button>
      </div>
    </simple-table> -->
    <el-table v-loading="loading" :data="list" style="width: 100%;margin-bottom: 20px;" row-key="id" border :tree-props="{children: 'children'}">

      <el-table-column prop="categoryName" label="分类名称" align="left">
      </el-table-column>

      <el-table-column prop="categoryLevel" label="分类等级" align="center">
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="180">
        <template slot-scope="scope">

          <el-button v-if="scope.row.categoryLevel != 4" type='text' size='mini' icon='el-icon-view' @click='addChildClick(scope.row)'>新增</el-button>
          <el-button type='text' size='mini' icon='el-icon-view' @click='detailView(scope.row)'>查看</el-button>
          <el-button type='text' size='mini' icon='el-icon-view' @click='editView(scope.row)'>编辑</el-button>
          <el-button type='text' size='mini' icon='el-icon-view' @click='delView(scope.row)'>删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="page.total > 0" :total="+page.total" :page.sync="page.current" :limit.sync="page.size" @pagination="handlePage" />

    <el-dialog title='食品分类导入' :visible.sync='uploadFormVisible' :before-close="handleClose" width='400px'>
      <el-form>
        <el-form-item>
          <div class="el-upload__text">请按模板规定方式填写上传文件<el-button type='text' @click="downClick()">下载模板</el-button></div>
          <el-upload class="upload-demo" drag :action="uploadUrl" :headers="headers" :auto-upload='true' name='file' :before-upload="beforeAvatarUpload" :on-success="handleFileSuccess" :on-error="handleFileError">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <!-- <div class="el-upload__tip" slot="tip">只能上传Excel文件,<a :href='downloadUrl'>下载模板</a> </div> -->
          </el-upload>
        </el-form-item>
      </el-form>
    </el-dialog>
  </simple-list-page>
</template>

<script>
import { fcRemove } from '@/api/modules/foodCategory'
import { fcPageList, firstFoodCategory, othorFoodCategory } from '@/api/modules/foodCategory.js'
import Pagination from '@/components/Pagination'
import { getToken } from '@/utils/auth'

export default {
  components: { Pagination },
  data() {
    return {
      loading: false,
      uploadFormVisible: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/system/foodCategory/import',
      downloadUrl: process.env.VUE_APP_BASE_API + '/system/foodCategory/download',
      headers: {
        Authorization: getToken()
      },
      parentId: 0,
      categoryLevel: 0,
      form: {},
      columns: [
        {
          label: '分类名称',
          prop: 'categoryName'
        },
        {
          label: '分类等级',
          prop: 'categoryLevel',
          formatter: (row) => {
            if (row.categoryLevel == 1) {
              return '食品大类'
            } else if (row.categoryLevel == 2) {
              return '食品亚类'
            } else if (row.categoryLevel == 3) {
              return '食品次亚类'
            } else if (row.categoryLevel == 4) {
              return '食品细类'
            }
          }
        },
        {
          label: '拥有子级',
          prop: 'hasChildren',
          formatter: (row) => {
            return row.hasChildren ? '是' : '否'
          }
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      firstly: [],
      second: [],
      thirdly: [],
      fourthly: []
    }
  },
  mounted() {
    this.loadData()
    this.firstlyCategory()
  },
  methods: {
    firstlyCategory() {
      firstFoodCategory().then(req => {
        this.firstly = req.data
      })
    },
    getSecondCategory() {
      othorFoodCategory({ parentId: this.form.firstly }).then(req => {
        this.second = req.data
      })
    },
    getThirdlyCategory() {
      othorFoodCategory({ parentId: this.form.second }).then(req => {
        this.thirdly = req.data
      })
    },
    getFourthlyCategory() {
      othorFoodCategory({ parentId: this.form.thirdly }).then(req => {
        this.fourthly = req.data
      })
    },
    handlePage() {
      this.loadData()
    },
    loadData() {
      this.loading = true
      let data = {
        current: this.page.current,
        size: this.page.size
      }
      if (this.form.fourthly) {
        data.levelId = this.form.fourthly
      } else if (this.form.thirdly) {
        data.levelId = this.form.thirdly
      } else if (this.form.second) {
        data.levelId = this.form.second
      } else if (this.form.firstly) {
        data.levelId = this.form.firstly
      }
      fcPageList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      }).finally(() => {
        this.loading = false
      })
    },
    loadChildren(id) {
      this.parentId = id
      this.categoryLevel++
      let data = {
        current: 0,
        size: this.page.size,
        parentId: this.parentId
      }
      fcPageList(data).then(res => {
        this.list = res.data.records
      })
    },
    backTopo() {
      this.parentId = 0
      this.categoryLevel = 1
      this.loadData()
    },
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
    },
    detailView(row) {
      this.$router.push({
        path: '/sya/foodCategory/detail',
        query: {
          id: row.id
        }
      })
    },
    addView() {
      this.$router.push({
        path: '/sya/foodCategory/add',
        query: {
          parentId: this.parentId,
          categoryLevel: this.categoryLevel
        }
      })
    },
    addChildClick(row) {

      this.$router.push({
        path: '/sya/foodCategory/add',
        query: {
          parentId: row.id,
          categoryLevel: row.categoryLevel
        }
      })
    },
    editView(row) {
      this.$router.push({
        path: '/sya/foodCategory/edit',
        query: {
          id: row.id
        }
      })
    },
    delView(row) {
      this.$confirm('确认删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        fcRemove({ id: row.id }).then(res => {
          this.$message.success(res.msg)
          this.loadData()
        })
      })

    },
    handleClose(done) {
      done();
    },
    showWpload() {
      this.uploadFormVisible = true
    },
    beforeAvatarUpload(file) {
      const isXLSX = file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      if (!isXLSX) {
        this.$message.error('上传文件只能是 xls/xlsx 格式!')
      }
      return isXLSX
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      // this.$message.success(res.msg)
      this.$alert('导入成功', "导入结果", {
        dangerouslyUseHTMLString: true
      });
      // this.getList();
    },
    handleFileError(response, file, fileList) {
      this.$alert('导入失败', "导入结果", {
        dangerouslyUseHTMLString: true
      });
    },
    downClick() {
      console.log(this.downloadUrl)
      const link = document.createElement('a') // 创建元素
      // let blob = new Blob([res.data]);
      link.style.display = 'none'
      link.href = this.downloadUrl // 创建下载的链接
      link.setAttribute('download', '食品分类.xlsx') // 给下载后的文件命名 fileName文件名  type文件格式
      document.body.appendChild(link)
      link.click() // 点击下载
      document.body.removeChild(link) //  下载完成移除元素
      window.URL.revokeObjectURL(link.href) // 释放掉blob对象
    }
  }
}
</script>

<style lang="scss" scoped></style>
