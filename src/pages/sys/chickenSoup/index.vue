<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='每日鸡汤：'>
          <el-input v-model='form.chickenSoupContent'></el-input>
        </el-form-item>
      </el-form>
    </div>

    <div slot='bottom-options'>
      <el-button type='primary' size='mini' icon='el-icon-plus' @click='addView'>新增</el-button>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
      <div slot='chickenSoupContent' slot-scope="scope">
         <div v-html="scope.row.chickenSoupContent.length > 50 ? scope.row.chickenSoupContent.substring(0,50) + '....': scope.row.chickenSoupContent "></div>
      </div>
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' icon='el-icon-edit' @click='editView(scope.row)'>编辑</el-button>
        <el-button type='text' size='mini' icon='el-icon-delete' @click='delte(scope.row)'>删除</el-button>
      </div>
    </simple-table>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="70%"
      :before-close="handleClose">
        <el-form :model="ruleForm"  :rules="rules" ref="ruleForm" label-width="130px" class="demo-ruleForm">
          <el-form-item label="每日鸡汤：" prop="chickenSoupContent">
            <simple-editor class="editor-content" v-model="ruleForm.chickenSoupContent"
            :showImage="false" :showVideo="false"></simple-editor>
            <div style="margin-top: 20px;height:10px;width: 10px"></div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit" v-loading="loading">确 定</el-button>
      </span>
    </el-dialog>
  </simple-list-page>
</template>

<script>
import {pageList,chickenSoupDelete,detail,add,update } from '@/api/modules/chickenSoup.js'
export default {
  data() {
    return {
      title: '',
      typeList: [],
      dialogVisible: false,
      dialogTypeVisible: false,
      form: {},
      columns: [
        {
          label: '每日鸡汤',
          prop: 'chickenSoupContent',
          slot: 'chickenSoupContent'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        }
      ],
      editorOption: {} ,
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      ruleForm: {
        chickenSoupContent: ''
      },
      rules: {
        chickenSoupContent: [
          { required: true, message: '请输入消息名称', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      let data={
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleQuery() {
      this.page={
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    // 提交
    submit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          let request;
          if (this.ruleForm.id) {
            request = update(this.ruleForm)
          } else {
            request = add(this.ruleForm)
          }
          request.then(res => {
            this.loading = false
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.dialogVisible = false
              } else {
                this.$message.error(res.msg)
              }
              this.handleQuery()
          }).catch(() => {
              this.loading = false
          })
        } else {
          // 登录表单校验失败
          this.$message.error('表单校验失败，请检查')
        }
      })
    },
    addView() {
      this.title = "新增每日鸡汤"
      this.dialogVisible = true
      this.ruleForm = {
       chickenSoupContent: ''
      }
    },
    // 删除
    delte(row) {
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          chickenSoupDelete({id: row.id}).then(res => {
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.handleQuery()
              } else {
                this.$message.error(res.msg)
              }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    // 编辑
    editView(row) {
      this.title = "编辑每日鸡汤"
      // 获取详情数据
      detail({id: row.id}).then(res => {
        this.loading = false
          if (+res.code === 200) {
            this.dialogVisible = true
            this.ruleForm = res.data
          } else {
            this.$message.error(res.msg)
          }
      }).catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
