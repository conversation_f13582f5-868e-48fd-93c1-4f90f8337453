<template>
  <simple-page title="新增版本" @back="handleBack">
      <el-form :model="ruleForm"  :rules="rules" ref="ruleForm" label-width="130px" class="demo-ruleForm">
        <el-form-item label="平台：" >
          Android
        </el-form-item>
        <el-form-item label="版本号：" prop="versionNum">
          <el-input v-model="ruleForm.versionNum" maxlength="20" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="更新类型：" >
          强制更新
        </el-form-item>
        <el-form-item label="灰度发布：" >
          全量发布
        </el-form-item>
        <el-form-item label="更新内容：" prop="updateContent">  
           <simple-editor class="editor-content" v-model="ruleForm.updateContent" 
           :showImage="false" :showVideo="false"></simple-editor>
            <div style="margin-top: 10px;height:10px;width: 10px"></div>
      </el-form-item>

        <el-form-item label="APK包上传：" prop="apkUrl">
          <el-upload
          class="upload-demo"
          drag
          :action="uploadUrl"
          :headers="headers"
          :file-list="fileList"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
          accept='.apk'>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传.apk文件</div>
        </el-upload>
       </el-form-item>
        <el-form-item style="margin-top: 100px">  
          <el-col :offset="16">
              <span  class="dialog-footer">
              <el-button @click="goList">取 消</el-button>
              <el-button type="primary" @click="submit" v-loading="loading">确 定</el-button>
            </span>
          </el-col>
       
           
      </el-form-item>
      </el-form>
  </simple-page>
</template>

<script>
import {add,update } from '@/api/modules/version.js'
import { getToken } from '@/utils/auth';
export default {
  data() {
    var checkNum = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请输入版本号'));
        }
        const test =  /^([1-9]\d|[1-9])(.([1-9]\d|\d)){2}$/.test(value)
        if (!test) {
           callback(new Error('版本号不正确，例子：1.0.1'));
        }
        callback();
      };
    return {
      loading: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/oss/endpoint/put-public-file',
      headers: {
        Authorization: getToken()
      },
      fileList: [],
      ruleForm: {
        platform: 'Android',
        versionNum: '',
        updateType: '强制更新',
        grayscaleRelease: '全量发布',
        updateContent: '',
        apkName: '',
        apkUrl: ''
      },
      rules: {
        versionNum: [
          { validator: checkNum, trigger: 'blur' }
        ],
        updateContent: [
          { required: true, message: '请输入更新内容', trigger: 'blur' }
        ],
        apkUrl: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ],
      }
    }
  },
  mounted() {
  },
  methods: {
    handleClose(done) {
      done()
    },
    // 提交
    submit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          let request;
          if (this.ruleForm.id) {
            request = update(this.ruleForm)
          } else {
            request = add(this.ruleForm)
          }
          request.then(res => {
            this.loading = false
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.goList()
              } else {
                this.$message.error(res.msg)
              }
              this.handleQuery()
          }).catch(() => {
              this.loading = false
          })
        } else {
          // 登录表单校验失败
          this.$message.error('表单校验失败，请检查')
        }
      })
    },
    goList() {
      this.$router.push({
        path: '/sys/version'
      })
    },
    handleBack() {},
    handleAvatarSuccess (res, file) {
      this.fileList = []
      this.fileList.push({
        name: res.data.originalName,
        url: res.data.link
      })
      this.ruleForm.apkName = res.data.originalName
      this.ruleForm.apkUrl = res.data.link
      this.loading =false
    },
    beforeAvatarUpload (file) {
      console.log(file.type)
      const isJPG = file.type==="application/vnd.android.package-archive" 
      // const isLt2M = file.size / 1024 / 1024 < 2
      if (!isJPG) {
        this.$message.error('上传文件只能是 apk 格式!')
      }
      this.loading = true
      this.ruleForm.apkName = ''
      this.ruleForm.apkUrl = ''
      // if (!isLt2M) {
      //   this.$message.error('上传头像图片大小不能超过 2MB!')
      // }
      return isJPG
    },
  },
}
</script>
