<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='版本号：'>
          <el-input v-model='form.versionNum'></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot='bottom-options'>
      <el-button type='primary' size='mini' icon='el-icon-plus' @click='addView'>新增</el-button>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' icon='el-icon-edit' @click='editView(scope.row)'>编辑</el-button>
        <el-button type='text' size='mini' icon='el-icon-delete' @click='delte(scope.row)'>删除</el-button>
      </div>
    </simple-table>
  </simple-list-page>
</template>

<script>
import {pageList,versionDelete } from '@/api/modules/version.js'
export default {
  data() {
    return {
      typeList: [],
      dialogVisible: false,
      dialogTypeVisible: false,
      form: {},
      fileList: [],
      columns: [
        {
          label: '平台',
          prop: 'platform'
        },
        {
          label: '版本号',
          prop: 'versionNum'
        },
        {
          label: '更新类型',
          prop: 'updateTypeDesc'
        },
        {
          label: '灰度发布',
          prop: 'grayscaleRelease'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        }
      ],
      editorOption: {} ,
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  created() {
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      let data={
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleQuery() {
      this.page={
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    // 删除
    delte(row) {
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          versionDelete({id: row.id}).then(res => {
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.handleQuery()
              } else {
                this.$message.error(res.msg)
              }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    addView() {
       this.$router.push({
        path: '/sys/version/add'
      })
    },
    // 编辑
    editView(row) {
      this.$router.push({
        path: '/sys/version/edit',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
