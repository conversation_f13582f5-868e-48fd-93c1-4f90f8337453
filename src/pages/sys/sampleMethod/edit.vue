<template>
  <simple-page title='编辑抽样办法' @back='handleBack' v-loading='loading'>
    <el-form ref='form' :model='form' label-width='80px' :rules='rules' :inline='true'>
      <el-form-item label='食品大类' prop='levelOneId'>
        <el-select v-model='form.levelOneId' clearable placeholder='请选择' @change='getSecondCategory'>
          <el-option v-for='item in firstly' :key='item.id' :label='item.categoryName' :value='item.id'>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label='食品亚类'>
        <el-select v-model='form.levelTwoId' clearable placeholder='请选择' @change='getThirdlyCategory'>
          <el-option v-for='item in second' :key='item.id' :label='item.categoryName' :value='item.id'>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label='食品次亚类' label-width='90px'>
        <el-select v-model='form.levelThreeId' clearable placeholder='请选择' @change='getFourthlyCategory'>
          <el-option v-for='item in thirdly' :key='item.id' :label='item.categoryName' :value='item.id'>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label='食品细类'>
        <el-select v-model='form.levelFourId' clearable placeholder='请选择'>
          <el-option v-for='item in fourthly' :key='item.id' :label='item.categoryName' :value='item.id'>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-form ref='form' :model='form' label-width='80px' :rules='rules'>
      <el-form-item label='抽样办法' prop='samplingMethod'>
        <simple-editor maxlength="2000" class="editor-content" v-model="form.samplingMethod" :showImage="true" :showVideo="false"></simple-editor>
      </el-form-item>
      <el-form-item>
        <el-col>
          <span class='dialog-footer'>
            <el-button @click='back'>取 消</el-button>
            <el-button type='primary' @click='submit'>确 定</el-button>
          </span>
        </el-col>
      </el-form-item>
    </el-form>
  </simple-page>
</template>

<script>
import { firstFoodCategory, othorFoodCategory } from '@/api/modules/foodCategory.js'
import { smDetail, smEdit } from '@/api/modules/sampleMethod'
export default {
  data() {
    return {
      pid: 0,
      loading: false,
      firstly: [],
      second: [],
      thirdly: [],
      fourthly: [],
      form: {
        levelOneId: '',
        levelTwoId: '',
        levelThreeId: '',
        levelFourId: '',
        samplingMethod: ''
      },
      rules: {
        firstly: [
          { required: true, message: '食品大类不能为空', trigger: 'blur' }
        ],
        samplingMethod: [
          { required: true, message: '抽样办法不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.form.id = this.$route.query.id
    this.loadDetail()

  },
  methods: {
    firstlyCategory() {
      firstFoodCategory().then(req => {
        this.firstly = req.data
      })
    },
    getSecondCategory() {
      othorFoodCategory({ parentId: this.form.levelOneId }).then(req => {
        this.second = req.data
      })
    },
    getThirdlyCategory() {
      othorFoodCategory({ parentId: this.form.levelTwoId }).then(req => {
        this.thirdly = req.data
      })
    },
    getFourthlyCategory() {
      othorFoodCategory({ parentId: this.form.levelThreeId }).then(req => {
        this.fourthly = req.data
      })
    },
    loadDetail() {
      smDetail({ id: this.form.id }).then(rel => {
        this.form = rel.data
        this.firstlyCategory()
        this.getSecondCategory()
        this.getThirdlyCategory()
        this.getFourthlyCategory()
        this.form.levelThreeId = rel.data.levelThreeId
      })
    },
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          if (this.form.levelFourId) {
            this.form.id = this.form.levelFourId
          } else if (this.form.levelThreeId) {
            this.form.id = this.form.levelThreeId
          } else if (this.form.levelTwoId) {
            this.form.id = this.form.levelTwoId
          } else if (this.form.levelOneId) {
            this.form.id = this.form.levelOneId
          }
          smEdit(this.form).then(rel => {
            this.loading = false
            this.$message.success(rel.msg)
            this.back()
          }).catch((e) => {
            this.$message.error(e.msg)
            this.loading = false
          })
        }
      })
    },
    handleBack() {
      // this.$router.back(-1)
    },
    back() {

      this.$router.back(-1)
    }
  }
}
</script>

<style lang="scss" scoped></style>
