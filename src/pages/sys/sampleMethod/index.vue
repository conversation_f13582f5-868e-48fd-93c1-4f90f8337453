<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='食品大类' prop='firstly'>
          <el-select v-model='form.levelOneId' clearable placeholder='请选择' @change='getSecondCategory'>
            <el-option
              v-for='item in firstly'
              :key='item.id'
              :label='item.categoryName'
              :value='item.id'>
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label='食品亚类' prop='second'>
          <el-select v-model='form.levelTwoId' clearable placeholder='请选择' @change='getThirdlyCategory'>
            <el-option
              v-for='item in second'
              :key='item.id'
              :label='item.categoryName'
              :value='item.id'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label='食品次亚类' prop='thirdly'>
          <el-select v-model='form.levelThreeId' clearable placeholder='请选择' @change='getFourthlyCategory'>
            <el-option
              v-for='item in thirdly'
              :key='item.id'
              :label='item.categoryName'
              :value='item.id'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label='食品细类' prop='fourthly'>
          <el-select v-model='form.levelFourId' clearable placeholder='请选择'>
            <el-option
              v-for='item in fourthly'
              :key='item.id'
              :label='item.categoryName'
              :value='item.id'>
            </el-option>
          </el-select>
        </el-form-item> -->
      </el-form>
    </div>
    <div slot='bottom-options'>
      <el-button type='primary' size='mini' icon='el-icon-plus' @click='addView'>新增</el-button>
      <el-button type='primary' size='mini' icon='el-icon-plus' @click='showWpload'>导入</el-button>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' icon='el-icon-view' @click='editView(scope.row)'>编辑</el-button>
        <el-button type='text' size='mini' icon='el-icon-delete' @click='remove(scope.row.id)'>删除</el-button>
      </div>
    </simple-table>
    <el-dialog title='抽样办法导入' :visible.sync='uploadFormVisible' :before-close="handleClose" width='400px'>
      <el-form >
        <el-form-item >
          <div class="el-upload__text">请按模板规定方式填写上传文件<el-button type='text' @click="downClick()">下载模板</el-button></div>
          <el-upload
            class="upload-demo"
            drag
            :action="uploadUrl"
            :headers="headers"
            :auto-upload='true'
            name='file'
            :before-upload="beforeAvatarUpload"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传Excel文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
    </el-dialog>
  </simple-list-page>

</template>


<script>
import {smPageList, smDelete} from '@/api/modules/sampleMethod.js'
import {firstFoodCategory, othorFoodCategory} from '@/api/modules/foodCategory'
import { getToken } from '@/utils/auth';

export default {
  data() {
    return {
      downloadUrl: process.env.VUE_APP_BASE_API + '/system/samplingMethod/download',
      uploadFormVisible: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/system/samplingMethod/import',
      headers: {
        Authorization: getToken()
      },
      pid: 0,
      form: {},
      columns: [
        {
          label: '食品大类',
          prop: 'levelOneName'
        },
        {
          label: '分类名称',
          prop: 'categoryName'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      firstly: [],
      second: [],
      thirdly: [],
      fourthly: []
    }
  },
  mounted() {
    this.loadData()
    this.firstlyCategory()
  },
  methods: {
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      smPageList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    firstlyCategory() {
      firstFoodCategory().then(req => {
        this.firstly = req.data
      })
    },
    getSecondCategory() {
      othorFoodCategory({parentId: this.form.levelOneId}).then(req => {
        this.second = req.data
      })
    },
    getThirdlyCategory() {
      othorFoodCategory({parentId: this.form.levelTwoId}).then(req => {
        this.thirdly = req.data
      })
    },
    getFourthlyCategory() {
      othorFoodCategory({parentId: this.form.levelThreeId}).then(req => {
        this.fourthly = req.data
      })
    },
    backTopo() {
      this.pid = 0
      this.loadData()
    },
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
    },
    detailView(row) {
      this.$router.push({
        path: '/sya/sampleMethod/detail',
        query: {
          id: row.id
        }
      })
    },
    addView() {
      this.$router.push({
        path: '/sya/sampleMethod/add',
        query: {
          pid: this.pid
        }
      })
    },
    editView(row) {
      this.$router.push({
        path: '/sya/sampleMethod/edit',
        query: {
          id: row.id
        }
      })
    },
    remove(id) {
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          smDelete({id: id}).then(rel => {
            if (+rel.code === 200) {
              this.$message.success(rel.msg)
              this.loadData()
            } else {
              this.$message.error(rel.msg)
            }

          })
        }).catch(() => {

        });

    },
    handleClose(done) {
      done();
    },
    showWpload(){
      this.uploadFormVisible=true
    },
    beforeAvatarUpload(file){
      const isXLSX = file.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      if (!isXLSX) {
        this.$message.error('上传文件只能是 xls/xlsx 格式!')
      }
      return isXLSX
    },
    downClick() {
      console.log(this.downloadUrl)
      const link = document.createElement('a') // 创建元素
      // let blob = new Blob([res.data]);
      link.style.display = 'none'
      link.href = this.downloadUrl // 创建下载的链接
      link.setAttribute('download', '食品分类.xlsx') // 给下载后的文件命名 fileName文件名  type文件格式
      document.body.appendChild(link)
      link.click() // 点击下载
      document.body.removeChild(link) //  下载完成移除元素
      window.URL.revokeObjectURL(link.href) // 释放掉blob对象
    }
  }
}
</script>

<style lang="scss" scoped></style>
