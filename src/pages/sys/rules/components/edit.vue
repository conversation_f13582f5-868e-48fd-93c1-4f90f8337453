<template>
  <div class='wrapper'>
    <el-dialog
      title='编辑'
      :visible.sync='dialogVisible'
      width='35%'
      @close='close'>
      <el-form :model='form' ref='form' :rules='rules' label-width='200px' label-position='right' size='small'
               class='dialog-form'>
        <el-form-item class='item' label='规则名称：'>
          <label>{{ form.name }}</label>
        </el-form-item>
        <el-form-item class='item' label='时间范围：'>
          <label>{{ form.time }}</label>
        </el-form-item>
        <el-form-item class='item' label='抽样批次限制：'>
          <el-input-number v-model='form.batch' label='描述文字' :min='0'></el-input-number>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {editRulesBatch} from '@/api/modules/sysRulesBatch'

export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      form: {},
      rules: {
        batch: [
          {required: true, message: '请输入抽样批次限制', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.form))

          this.loading = true
          editRulesBatch(data).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
              this.$emit('confirm')
            }, 500)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    open(data) {
      this.form = JSON.parse(JSON.stringify(data))
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}

.dialog-form {
  margin: 20px auto;
  width: 500px;

  .item {
    width: 80%;
  }
}
</style>
