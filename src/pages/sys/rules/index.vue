<template>
  <div class='wrapper'>
    <el-card class='box-card' style='margin-top: 10px' shadow='hover'>
      <div slot='header' class='list-header' style='text-align: left;margin-bottom: 7px'>
        <span>规则配置</span>
      </div>

      <el-table ref='table' v-loading='loading' :data='list' size='small'>
        <el-table-column type='index' width='70' label='序号' align='center' />
        <el-table-column label='规则名称' width='600' prop='name' align='center' />
        <el-table-column label='时间范围' prop='time' align='center' />
        <el-table-column label='抽样批次限制' prop='batch' align='center' />
        <el-table-column width='120' label='操作' align='center' class-name='small-padding fixed-width'>
          <template slot-scope='scope'>
            <el-button type='text' size='mini' @click='handleEdit(scope.row)'>编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

    </el-card>
    <edit-dialog ref='edit' @confirm='loadData' />
  </div>
</template>

<script>
import EditDialog from './components/edit'
import {listRulesBatch} from '@/api/modules/sysRulesBatch'

export default {
  components: {EditDialog},
  data() {
    return {
      loading: true,
      list: [],
      page: {
        total: 0
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.loading = true
      let data = {
        current: this.page.current,
        size: this.page.size
      }
      listRulesBatch(data).then(res => {
        this.list = res.data
      }).finally(() => this.loading = false)
    },
    handleEdit(row) {
      this.$refs.edit.open(row)
    }
  }
}
</script>

<style lang='scss' scoped></style>
