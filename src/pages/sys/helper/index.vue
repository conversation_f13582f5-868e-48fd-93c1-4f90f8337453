<template>
  <div class='wrapper'>
    <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot='search'>
        <el-form :inline='true' :model='form'>
          <el-form-item label='会员类型：' class='ml20'>
            <el-select v-model='form.memberStatus' placeholder='请选择' class='col-24'>
              <el-option
                v-for='item in optionMemberType'
                :key='item.value'
                :label='item.label'
                :value='item.value'>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot='bottom-options'>
        <el-button type='primary' size='mini' @click='handleInsert'>新增</el-button>
      </div>
      <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
        <div slot='options' slot-scope='scope'>
          <el-button type='text' size='mini' @click='handleEdit(scope.row)'>编辑</el-button>
          <el-button type='text' size='mini' @click='handleDel(scope.row)'>删除</el-button>
        </div>
      </simple-table>
    </simple-list-page>
    <insert-dialog ref='insert' @confirm='handleQuery' />
    <edit-dialog ref='edit' @confirm='handleQuery' />
  </div>
</template>

<script>
import InsertDialog from './components/insert'
import EditDialog from './components/edit'
import {delHelper, pageHelper} from '@/api/modules/helper'

export default {
  components: {InsertDialog, EditDialog},
  data() {
    return {
      form: {
        memberMealName: null,
        memberType: null
      },
      columns: [
        {
          label: '会员类型',
          prop: 'memberStatusName'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        }
      ],
      optionMemberType: [
        {value: 0, label: '普通会员'},
        {value: 1, label: 'VIP会员'},
        {value: 2, label: 'SVIP会员'}
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {
        memberMealName: null,
        memberType: null
      }
      this.handleQuery()
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageHelper(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleInsert() {
      this.$refs.insert.open()
    },
    handleEdit(row) {
      this.$refs.edit.open(row.id)
    },
    handleDel(row) {
      this.$confirm('确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        delHelper({id: row.id}).then(res => {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.loadData()
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang='scss' scoped></style>
