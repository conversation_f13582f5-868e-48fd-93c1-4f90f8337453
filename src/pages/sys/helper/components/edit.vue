<template>
  <div class='wrapper'>
    <el-dialog title='编辑' :visible.sync='dialogVisible' width='60%' @close='close'>
      <el-form :model='form' ref='form' :rules='rules' label-width='180px' label-position='right' size='small' class='dialog-form'>
        <el-form-item class='item' label='会员类型：' prop='memberStatus'>
          <el-select v-model='form.memberStatus' placeholder='请选择' class='col-24'>
            <el-option v-for='item in optionMemberType' :key='item.value' :label='item.label' :value='item.value'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class='item' label='抽样助手图片：'>
          <div class='itemcontent' v-for='(item,index) in form.assistantList' :key="index">
            <div class='list-banner'>
              <template>
                <div class='banner'>
                  <el-form-item class='banner-item' label-width='160px' label='抽样助手交流群图片：' :prop="'assistantList.'+index+'.url'">
                    <el-upload class='img-uploader' :action='uploadUrl' :headers='headers' :show-file-list='false' :on-success='function (response) { return handleUploadBannerSuccess(response, index) }'
                      :on-remove='handleUploadRemove' accept='.png,.jpg,.jpge,.webp'>
                      <img v-if='listUrl[index]' :src='listUrl[index]' class='img'>
                      <i v-else class='el-icon-plus img-uploader-icon'></i>
                    </el-upload>
                  </el-form-item>
                  <el-form-item class='banner-item' label-width='160px' label='图片链接地址：' :prop="'assistantList.'+index+'.detailUrl'">
                    <el-input v-model='item.detailUrl'></el-input>
                  </el-form-item>
                </div>
              </template>
            </div>
            <el-button v-if="index==0" type='text' class='ml20' size='medium' style='margin-top: 70px; margin-left:15px' @click='handleInsertBanner'>添加
            </el-button>
            <el-button v-if="form.assistantList.length>1" type='text' class='ml20' size='medium' style='margin-top: 70px' @click='deleteClick(item, index)'>删除
            </el-button>
          </div>
        </el-form-item>
        <el-form-item class='item' label='识别次数：' prop='ocrTimes'>
          <el-select v-model='form.ocrTimes' placeholder='请选择' class='col-24'>
            <el-option v-for='item in optionTimes' :key='item.value' :label='item.label' :value='item.value'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class='item' label='识别次数记录规则：' prop='ocrTimesRuleType'>
          <el-radio-group v-model='form.ocrTimesRuleType'>
            <el-radio :label='1'>识别成功+识别失败</el-radio>
            <el-radio :label='2'>只算识别成功</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item class='item' label='功能使用次数分配规则：' prop='ocrTimesDistributionType'>
          <el-radio-group v-model='form.ocrTimesDistributionType'>
            <el-radio :label='1'>抽样助手所有功能共用功能使用次数</el-radio>
            <!--            <el-radio :label='2'>抽样助手所有功能分别拥有功能使用次数</el-radio>-->
          </el-radio-group>
        </el-form-item>
        <el-form-item class='item' label='提示信息：' prop='tips'>
          <x-editor class='editor-content' v-model='form.tips' :showImage='false' :showVideo='false' @blur='checkTips' @change='checkTips'></x-editor>
        </el-form-item>
        <el-form-item class='item' label='抽样助手交流群图片：'>
          <el-upload class='img-uploader' :action='uploadUrl' :headers='headers' :show-file-list='false' :on-success='handleUploadSuccess' :on-remove='handleUploadRemove' accept='.png,.jpg,.jpge,.webp'>
            <img v-if='imageUrl' :src='imageUrl' class='img'>
            <i v-else class='el-icon-plus img-uploader-icon'></i>
          </el-upload>
        </el-form-item>
        <el-form-item class='item' label='抽样助手交流群名称：'>
          <el-input v-model='form.groupName'></el-input>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { preHelper, updateHelper } from '@/api/modules/helper'
import XEditor from '@/components/XEditor'
import { getToken } from '@/utils/auth'

export default {
  components: { XEditor },
  data() {
    let validateUpload = (rule, value, callback) => {
      if (!this.imageUrl) {
        callback(new Error('请上传抽样助手交流群图片'))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false,
      loading: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/oss/endpoint/put-public-file',
      headers: {
        Authorization: getToken()
      },
      optionMemberType: [
        { value: 0, label: '普通会员' },
        { value: 1, label: 'VIP会员' },
        { value: 2, label: 'SVIP会员' }
      ],
      optionTimes: [
        { value: -1, label: '不限' },
        { value: 1, label: '1次' },
        { value: 2, label: '2次' },
        { value: 3, label: '3次' },
        { value: 4, label: '4次' },
        { value: 5, label: '5次' },
        { value: 6, label: '6次' },
        { value: 7, label: '7次' },
        { value: 8, label: '8次' },
        { value: 9, label: '9次' },
        { value: 10, label: '10次' },
        { value: 11, label: '11次' },
        { value: 12, label: '12次' },
        { value: 13, label: '13次' },
        { value: 14, label: '14次' },
        { value: 15, label: '15次' }
      ],
      form: {
        memberStatus: null,//枚举 会员类型：1-普通会员|2-VIP会员|3-SVIP会员,
        ocrTimesType: null,//识别次数是否不限：1-不限|2-限制,
        ocrTimes: null,//识别次数,
        ocrTimesRuleType: null,//识别次数记录规则：1-识别成功+识别失败|2-只算识别成功,
        ocrTimesDistributionType: 1,//1-抽样助手所有功能共用功能使用次数 |2-抽样助手所有功能分别拥有功能使用次数
        tips: null,//提示信息,
        groupUrl: null,//抽样助手交流群图片,
        groupName: null,//抽样助手交流群名称,
        assistantList: [
          {
            resourceUrl: null,//图片地址,
            detailUrl: null,//图片链接地址,
            orderNum: null//排序，数值越大越靠前
          }
        ]
      },
      rules: {
        memberStatus: [
          { required: true, message: '请选择会员类型', trigger: 'change' }
        ],
        ocrTimes: [
          { required: true, message: '请选择识别次数', trigger: 'change' }
        ],
        ocrTimesRuleType: [
          { required: true, message: '请选择识别次数记录规则', trigger: 'change' }
        ],
        ocrTimesDistributionType: [
          { required: true, message: '功能使用次数分配规则', trigger: 'change' }
        ],
        tips: [
          { required: true, message: '请输入提示信息', trigger: 'blur' }
        ],
        groupName: [
          { required: true, message: '请输入抽样助手交流群名称', trigger: 'blur' }
        ],
        imageUrl: [
          { required: true, validator: validateUpload, trigger: 'change' }
        ]
      },
      imageUrl: null,
      listUrl: [null]
    }
  },
  methods: {
    deleteClick(item, index) {
      let arr = []
      this.listUrl.forEach(item => {
        if (item) arr.push(item)
      });
      let dic = this.listUrl[this.listUrl.length - 1]
      if (arr.length == 1 && !dic && index == 0) {
        return
      }
      this.form.assistantList.splice(index, 1)
      this.listUrl.splice(index, 1)
    },
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.form))

          let l = data.assistantList.length
          let list = []
          for (let i = 0; i < l; i++) {
            if (this.listUrl[i])
              list.push({
                resourceUrl: this.listUrl[i],
                detailUrl: data.assistantList[i].detailUrl,
                orderNum: l - i
              })
          }
          data.assistantList = list

          data.groupUrl = this.imageUrl

          if (data.ocrTimes > 0) {
            data.ocrTimesType = 2
          } else {
            data.ocrTimesType = 1
          }

          data.id = this.id

          this.loading = true
          updateHelper(data).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
              this.$emit('confirm')
            }, 500)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    loadData() {
      preHelper({ id: this.id }).then(res => {
        this.form = res.data
        this.imageUrl = this.form.groupUrl

        if (res.data.assistantList.length > 0) {
          this.listUrl = this.form.assistantList.map((item) => item.resourceUrl)
        } else {
          this.listUrl = [null]
          this.form.assistantList = [{
            resourceUrl: null,//图片地址,
            detailUrl: null,//图片链接地址,
            orderNum: null//排序，数值越大越靠前
          }]
        }
      })
    },
    open(id) {
      console.log(this.form)
      this.id = id
      this.loadData()
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
      this.imageUrl = null
      this.listUrl = [null]
      this.$refs.form.resetFields()
    },
    handleUploadSuccess(res) {
      this.imageUrl = res.data.link
      this.$refs.form.validateField('imageUrl')
    },
    handleUploadRemove() {
      this.imageUrl = null
      this.$refs.form.validateField('imageUrl')
    },
    handleUploadBannerSuccess(res, index) {
      this.listUrl[index] = res.data.link
      this.$forceUpdate()
    },
    handleInsertBanner() {
      let dic = this.listUrl[this.listUrl.length - 1]
      if (!dic) {
        return
      }
      this.listUrl.push(null)
      this.form.assistantList.push(
        {
          resourceUrl: null,//图片地址,
          detailUrl: null,//图片链接地址,
          orderNum: null//排序，数值越大越靠前
        }
      )
    },
    checkTips() {
      this.$refs.form.validateField('tips')
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 15%;

  .item {
    width: 80%;
  }
}

.img-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 88px;
  height: 88px;
}

.img-uploader:hover {
  border-color: #409eff;
}

.img-uploader-icon {
  font-size: 14px;
  color: #8c939d;
  width: 88px;
  height: 88px;
  line-height: 88px;
  text-align: center;
}

.img {
  width: 88px;
  height: 88px;
  display: block;
}

.itemcontent {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.list-banner {
  display: flex;
  width: 85%;
  flex-wrap: wrap;
}

.banner {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  padding-top: 18px;
  margin-bottom: 10px;
  width: 100%;

  .banner-item {
    width: 95%;
  }
}
</style>
