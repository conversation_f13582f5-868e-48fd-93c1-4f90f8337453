<template>
  <div class="employee-manage">
    <div class="filter-options">
      <el-form ref="searchForm" :inline="true" :model="searchForm" size="small">
        <el-form-item label="账号：">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="员工姓名：">
          <el-input
            v-model="searchForm.realName"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadData(searchForm)"
            >查询</el-button
          >
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="operation-btns">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAdd"
          >添加用户</el-button
        >
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="list"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="username"
          label="账号"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="orgName"
          label="所属机构"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="realName"
          label="用户姓名"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="telephone"
          label="联系方式"
          min-width="150"
          align="center"
        ></el-table-column>
        <!-- <el-table-column
          prop="roleName"
          label="角色"
          min-width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="remark"
          label="备注"
          min-width="200"
          align="center"
        ></el-table-column> -->
        <el-table-column label="操作" width="220" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-refresh"
              @click="handleResetPass(scope.row)"
              >重置密码</el-button
            >
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="text"
              icon="el-icon-delete"
              class="delete-btn"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
        ></el-pagination>
      </div>
    </div>

    <!-- 添加/编辑用户弹窗 -->
    <m-edit
      :dialogTitle="dialogTitle"
      :visible.sync="dialogVisible"
      :employeeFormData="employeeForm"
    ></m-edit>
  </div>
</template>

<script>
import {
  getEmployeeList,
  deleteEmployee,
  resetPassword,
} from "@/api/modules/employee";
import Edit from "./components/edit.vue";

export default {
  name: "Employee",
  components: {
    "m-edit": Edit,
  },
  data() {
    return {
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10,
      },
      dialogVisible: false,
      dialogTitle: "添加用户",
      employeeForm: {},
      loading: false,
      searchForm: {
        username: "",
        realName: "",
      },
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 初始表格数据
    loadData(search = {}) {
      const params = {
        ...search,
        current: this.page.current,
        size: this.page.size,
      };
      this.loading = true;
      getEmployeeList(params)
        .then((res) => {
          if (res.code === 200) {
            this.list = (res.data.records || []).map((item) => {
              const newItem = { ...item };
              Object.keys(newItem).forEach((key) => {
                if (
                  newItem[key] === null ||
                  newItem[key] === undefined ||
                  newItem[key] === ""
                ) {
                  newItem[key] = "-";
                }
              });
              return newItem;
            });
            this.page.total = parseInt(res.data.total || 0);
          } else {
            this.$message.error(res.msg || "获取用户列表失败");
          }
        })
        .catch((err) => {
          console.error("获取用户列表失败:", err);
          this.$message.error("获取用户列表失败，请检查网络连接");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleReset() {
      this.searchForm = { username: "", realName: "" };
      this.loadData();
    },
    handleSizeChange(val) {
      this.page.size = val;
      this.loadData();
    },
    handleCurrentChange(val) {
      this.page.current = val;
      this.loadData();
    },
    handleAdd() {
      this.dialogTitle = "添加用户";
      this.dialogVisible = true;
      this.employeeForm = {
        id: "",
        username: "",
        password: "123456",
        realName: "",
        telephone: "",
        roleName: "",
        roleIds: [],
        remark: "",
      };
    },
    handleEdit(row) {
      this.dialogTitle = "编辑用户";
      this.employeeForm = {
        id: row.id,
        username: row.username,
        password: "",
        realName: row.realName,
        telephone: row.telephone,
        roleName: row.roleName,
        remark: row.remark || "",
        orgId: row.orgId,
        roleIds: row.roleIds || [],
      };
      this.dialogVisible = true;
    },
    handleDelete(row) {
      this.$confirm("用户删除后，该用户将无法登录平台？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteEmployee(row.id)
          .then((res) => {
            if (res.code === 200) {
              this.$message.success("用户删除成功");
              this.deleteDialogVisible = false;
              this.loadData();
            } else {
              this.$message.error(res.msg || "用户删除失败");
            }
          })
          .catch((err) => {
            console.error("删除用户失败:", err);
            this.$message.error("删除用户失败，请检查网络连接");
          })
          .finally(() => {});
      });
    },
    handleResetPass(row) {
      this.$confirm("确定要重置该用户密码为123456?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const params = {
          userId: row.id,
        };
        resetPassword(params).then((res) => {
          if (res.code === 200) {
            this.$message.success("重置成功!");
          } else {
            this.$message.error(res.msg || "重置失败");
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.employee-manage {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;

  .custom-tabs {
    /deep/ .el-tabs__nav-wrap::after {
      display: none; /* 移除Tab下的深灰色线 */
    }
  }
}

.filter-options {
  margin-bottom: 20px;

  .operation-btns {
    margin-bottom: 10px;
  }
}

.table-container {
  width: 100%;
  overflow-x: auto;

  .delete-btn {
    color: #f56c6c;
    margin-left: 10px;
  }
}

.pagination-container {
  margin-bottom: 20px;
  text-align: right;
}

.password-input-container {
  position: relative;
}

.password-eye {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #c0c4cc;
  font-size: 16px;
}

/deep/ .el-table__header {
  th {
    &:last-child {
      transform: translateY(-1px) !important;
    }
  }
}
</style>
