<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="500px"
    :close-on-click-modal="false"
    @close="closeDialog"
    :destroy-on-close="true"
  >
    <el-form
      :model="employeeForm"
      :rules="rules"
      ref="employeeFormRef"
      label-width="100px"
      autocomplete="off"
    >
      <el-form-item label="用户账号" prop="username">
        <el-input
          :disabled="dialogTitle !== '添加用户'"
          v-model="employeeForm.username"
          placeholder="请输入用户账号"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="用户密码" prop="password" v-if="!employeeForm.id">
        <div class="password-input-container">
          <el-input
            v-model="employeeForm.password"
            :type="passwordVisible ? 'text' : 'password'"
            placeholder="请输入用户密码"
            autocomplete="new-password"
            show-password
          ></el-input>
        </div>
      </el-form-item>
      <el-form-item label="用户姓名" prop="realName">
        <el-input
          v-model="employeeForm.realName"
          placeholder="请输入用户姓名"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="所属机构" prop="orgId">
        <el-select
          v-model="employeeForm.orgId"
          placeholder="请选择所属机构"
          autocomplete="off"
        >
          <el-option
            v-for="item in orgOptions"
            :key="item.id"
            filterable
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="联系方式" prop="telephone">
        <el-input
          v-model="employeeForm.telephone"
          placeholder="请输入联系方式"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item label="角色" prop="roleIds">
        <el-checkbox-group v-model="employeeForm.roleIds">
          <el-checkbox
            v-for="item in roleOptions"
            :key="item.id"
            :label="item.id"
            >{{ item.roleName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- <el-form-item label="备注">
        <el-input
          v-model="employeeForm.remark"
          type="textarea"
          placeholder="请输入备注信息"
        ></el-input>
      </el-form-item> -->
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="submitLoading"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  addEmployee,
  updateEmployeeUser,
  getRole,
  getOrgList,
} from "@/api/modules/employee";
import { getRolesList } from "@/api/modules/sysRole";

export default {
  name: "EmployeeEdit",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dialogTitle: {
      type: String,
      default: "添加用户",
    },
    employeeFormData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      employeeForm: {
        id: "",
        username: "",
        password: "",
        realName: "",
        telephone: "",
        roleIds: [],
        remark: "",
      },
      orgOptions: [],
      rules: {
        username: [
          { required: true, message: "请输入用户账号", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入用户密码", trigger: "blur" },
        ],
        realName: [
          { required: true, message: "请输入用户姓名", trigger: "blur" },
        ],
        telephone: [
          { required: true, message: "请输入联系方式", trigger: "blur" },
        ],
        roleIds: [{ required: true, message: "请选择角色", trigger: "change" }],
        orgId: [
          { required: true, message: "请选择所属机构", trigger: "change" },
        ],
      },
      roleOptions: [],
      roleOptionsSelect: [],
      passwordVisible: false,
      submitLoading: false,
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.$nextTick(() => {
          if (val) {
            this.$refs.employeeFormRef &&
              this.$refs.employeeFormRef.clearValidate();
            this.employeeForm = this.employeeFormData;
            this.queryRoleList();
            this.queryOrgList();
            if (this.employeeForm.id) {
              this.getRoleListSelect(this.employeeForm.id);
            }
          }
        });
      },
    },
  },
  methods: {
    closeDialog() {
      this.$parent.dialogVisible = false;
    },
    submitForm() {
      this.$refs.employeeFormRef.validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          const formData = {
            ...JSON.parse(JSON.stringify(this.employeeForm)),
            orgName: this.orgOptions.find(
              (item) => item.id === this.employeeForm.orgId
            ).name,
          };

          if (formData.id) {
            // 编辑模式，删除密码字段
            delete formData.password;

            // 发送请求
            updateEmployeeUser(formData)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success("用户信息更新成功");
                  this.dialogVisible = false;
                  this.$parent.loadData();
                  this.$parent.dialogVisible = false;
                } else {
                  this.$message.error(res.msg || "用户信息更新失败");
                }
              })
              .catch((err) => {
                console.error("更新用户信息失败:", err);
                this.$message.error("更新用户信息失败，请检查网络连接");
              })
              .finally(() => {
                this.submitLoading = false;
              });
          } else {
            // 新增，准备API需要的数据格式
            const registerData = {
              username: formData.username,
              password: formData.password,
              realName: formData.realName,
              telephone: formData.telephone,
              remark: formData.remark,
              roleIds: formData.roleIds,
              orgId: formData.orgId,
              orgName: this.orgOptions.find(
                (item) => item.id === formData.orgId
              ).name,
            };

            addEmployee(registerData)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success("用户添加成功");
                  this.dialogVisible = false;
                  this.$parent.loadData();
                  this.$parent.dialogVisible = false;
                } else {
                  this.$message.error(res.msg || "用户添加失败");
                }
              })
              .catch((err) => {
                console.error("添加用户失败:", err);
                this.$message.error("添加失败:" + err.msg);
              })
              .finally(() => {
                this.submitLoading = false;
              });
          }
        }
      });
    },
    // 当前用户选中角色
    getRoleListSelect(userId) {
      const params = { userId };
      getRole(params).then((res) => {
        if (res.code === 200) {
          this.employeeForm.roleIds = res.data;
        }
      });
    },
    // 所有角色列表
    queryRoleList() {
      getRolesList()
        .then((res) => {
          if (res.code === 200) {
            this.roleOptions = res.data;
            console.log(3333, res.data);
          } else {
            this.$message.error(res.msg || "获取角色列表失败");
          }
        })
        .catch((err) => {
          console.error("获取角色列表失败:", err);
          this.$message.error("获取角色列表失败，请检查网络连接");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 机构枚举值
    queryOrgList() {
      getOrgList()
        .then((res) => {
          if (res.code === 200) {
            this.orgOptions = res.data;
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((err) => {
          console.error("失败:", err);
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
