<template>
  <el-row class="menu-edit">
    <el-form ref="form" inline v-model="formInfo" :rules="rules">
      <el-col :span="20">
        <el-form-item label="菜单名称" prop="menuName" label-width="100px">
          <el-input
            class="w-full"
            v-model="formInfo.menuName"
            placeholder="请输入菜单名称"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item label="访问地址" prop="path" label-width="100px">
          <el-input
            class="w-full"
            v-model="formInfo.path"
            placeholder="请输入菜单名称"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item label="排序" prop="orderNum" label-width="100px">
          <el-input
            class="w-full"
            v-model="formInfo.orderNum"
            placeholder="请输入数字"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item label="描述" prop="remark" label-width="100px">
          <el-input
            class="w-full"
            v-model="formInfo.remark"
            placeholder="请输入菜单描述"
          ></el-input
        ></el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item label="状态" label-width="100px">
          <el-radio-group
            v-model="formInfo.status"
            class="w-full"
            @change="validationErrors.status = false"
          >
            <el-radio label="0">可用</el-radio>
            <el-radio label="1">禁用</el-radio>
          </el-radio-group>
          <div
            v-if="validationErrors.status"
            style="color: #f56c6c; font-size: 12px; margin-top: 5px"
          >
            请选择状态
          </div>
          <!-- <div
            style="font-size: 12px; color: #67c23a; margin-top: 5px"
            v-else-if="formInfo.status === '0' || formInfo.status === 0"
          >
            已选择: 可用
          </div> -->
          <div
            style="font-size: 12px; color: #67c23a; margin-top: 5px"
            v-else-if="formInfo.status === '1' || formInfo.status === 1"
          >
            已选择: 禁用
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item label="平台" label-width="100px">
          <el-radio-group
            v-model="formInfo.platform"
            class="w-full"
            @change="validationErrors.platform = false"
          >
            <el-radio :label="1">PC</el-radio>
            <el-radio :label="2">小程序</el-radio>
          </el-radio-group>
          <div
            v-if="validationErrors.platform"
            style="color: #f56c6c; font-size: 12px; margin-top: 5px"
          >
            请选择平台
          </div>
          <!-- <div v-else style="font-size: 12px; color: #67c23a; margin-top: 5px">
            已选择:
            {{
              formInfo.platform === 1
                ? "PC"
                : formInfo.platform === 2
                ? "小程序"
                : "未选择"
            }}
          </div> -->
        </el-form-item>
      </el-col>
    </el-form>
    <el-col class="menu-edit-bottom">
      <el-button type="primary" :loading="loading" @click="handleAddMenu"
        >确认修改</el-button
      >
    </el-col>
  </el-row>
</template>

<script>
import { menuEdit } from "@/api/modules/sysMenu.js";
export default {
  props: {
    parentId: {
      type: Number,
      default() {
        return null;
      },
    },
    formInfoData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      //=====================================表格表单数据====================================//
      formInfo: {
        platform: 1, // 设置默认值，防止未定义
        status: "0", // 设置默认值，防止未定义
      },
      rules: {
        // 表单输入判断
        menuName: [{ required: true, message: "请输入名称", trigger: "blur" }],
        path: [{ required: true, message: "请输入访问地址", trigger: "blur" }],
      },
      // 自定义验证错误信息
      validationErrors: {
        status: false,
        platform: false,
      },
      loading: false, // 添加数据加载效果
    };
  },
  watch: {
    formInfoData: {
      handler(newVal, oldVal) {
        console.log("接收到的表单数据:", newVal);
        // 确保表单数据中有platform字段，如果没有则设置默认值为1(PC)
        // 同时确保platform是数字类型，防止字符串类型导致radio不选中
        this.formInfo = {
          ...newVal,
          platform: newVal.platform !== undefined ? Number(newVal.platform) : 1,
          status: newVal.status || "0",
        };

        // 确保有值时不显示验证错误
        if (
          this.formInfo.status ||
          this.formInfo.status === 0 ||
          this.formInfo.status === "0"
        ) {
          this.validationErrors.status = false;
        }

        if (this.formInfo.platform || this.formInfo.platform === 0) {
          this.validationErrors.platform = false;
        }

        console.log("处理后的表单数据:", this.formInfo);
      },
      immediate: true, // 确保组件初始化时也执行
    },
  },
  methods: {
    //=====================================组件间交互====================================//
    handleAddMenu() {
      // 重置验证错误
      this.validationErrors = {
        status: false,
        platform: false,
      };

      // 自定义验证
      let isValid = true;

      // 验证状态
      if (
        !this.formInfo.status &&
        this.formInfo.status !== 0 &&
        this.formInfo.status !== "0"
      ) {
        this.validationErrors.status = true;
        isValid = false;
      }

      // 验证平台
      if (!this.formInfo.platform && this.formInfo.platform !== 0) {
        this.validationErrors.platform = true;
        isValid = false;
      }

      // 表单验证
      this.$refs.form.validate((valid) => {
        if (valid && isValid) {
          this.loading = true;

          // 确保platform是数字类型
          const params = {
            ...this.formInfo,
            platform: Number(this.formInfo.platform),
          };

          console.log("提交的菜单数据:", params);

          menuEdit(params)
            .then(() => {
              this.$emit("finsh");
              this.$message.success("修改成功");
            })
            .catch((err) => {
              console.error("修改菜单失败:", err);
              this.$message.error("修改失败，请检查网络连接");
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          this.$message.warning("请完善必填信息");
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("close", false);
    },
    //=====================================其他操作=====================================//
  },
};
</script>

<style lang="scss">
.w-full {
  width: 20vw;
}
.menu-edit {
  display: inline-block;
  &-bottom {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
  }
}
.menu-popover {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}
.btn {
  border-width: 0px;
}
.el-tree-node__content {
  padding: 15px 0 !important;
}
</style>
