<template>
  <el-row class="menu-edit">
    <el-form ref="form" inline v-model="formInfo" :rules="rules">
      <el-col :span="20">
        <el-form-item label="菜单名称" prop="menuName" label-width="100px">
          <el-input
            class="w-full"
            v-model="formInfo.menuName"
            placeholder="请输入菜单名称"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item label="访问地址" prop="path" label-width="100px">
          <el-input
            class="w-full"
            v-model="formInfo.path"
            placeholder="请输入菜单名称"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item label="排序" prop="orderNum" label-width="100px">
          <el-input
            class="w-full"
            v-model="formInfo.orderNum"
            placeholder="请输入数字"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item label="描述" prop="remark" label-width="100px">
          <el-input
            class="w-full"
            v-model="formInfo.remark"
            placeholder="请输入菜单描述"
          ></el-input
        ></el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item label="状态" prop="status" label-width="100px">
          <el-radio-group v-model="formInfo.status" class="w-full">
            <el-radio label="0">可用</el-radio>
            <el-radio label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item label="平台" prop="platform" label-width="100px">
          <el-radio-group v-model="formInfo.platform" class="w-full">
            <el-radio :label="1">PC</el-radio>
            <el-radio :label="2">小程序</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-form>
    <el-col class="menu-edit-bottom">
      <el-button type="primary" :loading="loading" @click="handleAddMenu"
        >确认</el-button
      >
      <el-button type="danger" @click="handleClose">取消</el-button>
    </el-col>
  </el-row>
</template>

<script>
import { menuSave } from "@/api/modules/sysMenu.js";
export default {
  props: {
    parentId: {
      type: Number,
      default() {
        return null;
      },
    },
  },
  data() {
    return {
      //=====================================表格表单数据====================================//
      formInfo: {
        parentId: null,
        menuName: "",
        path: "",
        remark: "",
        orderNum: null,
        status: "0",
        platform: 1,
      },
      rules: {
        // 表单输入判断
        menuName: [{ required: true, message: "请输入名称", trigger: "blur" }],
        path: [{ required: true, message: "请输入访问地址", trigger: "blur" }],
        orderNum: [
          { required: true, message: "请输入排序数字", trigger: "blur" },
        ],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
      },
      loading: false, // 添加数据加载效果
    };
  },
  methods: {
    //=====================================组件间交互====================================//
    handleAddMenu() {
      this.loading = true;
      const params = {
        ...this.formInfo,
        parentId: this.parentId,
        platform: Number(this.formInfo.platform), // 确保platform是数字类型
      };
      console.log(params);
      menuSave(params)
        .then(() => {
          this.handleClose();
          this.$emit("finsh");
          this.$message.success("新增成功");
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
      //   } else {
      //     this.$nextTick(() => {
      //       // eslint-disable-next-line no-unused-expressions
      //       document.querySelector(".el-form-item.is-error input")
      //         ? document.querySelector(".el-form-item.is-error input").focus()
      //         : null;
      //     });
      //     this.$message.warning("请完善必填信息");
      //     this.loading = false;
      //   }
      // });
    },
    handleClose() {
      this.$emit("close", false);
    },
    //=====================================其他操作=====================================//
  },
};
</script>

<style lang="scss">
.w-full {
  width: 20vw;
}
.menu-edit {
  display: inline-block;
  &-bottom {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
  }
}
.menu-popover {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}
.btn {
  border-width: 0px;
}
.el-tree-node__content {
  padding: 15px 0 !important;
}
</style>
