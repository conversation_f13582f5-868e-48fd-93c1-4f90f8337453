<template>
  <div>
    <div class="menu-container">
      <el-card
        class="menu-container-left"
        v-loading="loading"
        element-loading-text="数据加载中"
        element-loading-background="rgba(255, 255, 255, 0.8)"
        element-loading-spinner="el-icon-loading"
      >
        <template slot="header">
          <div slot="header" class="card-header">
            <h3>菜单列表</h3>
            <div class="menu-container-left-btn">
              <i
                class="el-icon-refresh"
                title="刷新"
                @click="handleRefreshTree"
              ></i>
              <el-button type="text" @click="handleAddRootMenu">新增</el-button>
            </div>
          </div>
        </template>
        <el-tree
          :data="menuData"
          node-key="id"
          :props="defaultProps"
          @node-click="handleNodeClick"
          :default-expanded-keys="defaultExpanded"
          @node-drag-end="handleDragEnd"
        >
          <div slot-scope="{ node, data }" class="menu-popover">
            <span>{{ node.label }}</span>
            <el-popover
              v-model="data.visible"
              placement="right-end"
              width="88"
              trigger="hover"
            >
              <div class="text-align-left d-flex flex-column">
                <el-button class="btn" plain @click="handleAddNodeMenu(data)"
                  >新增菜单</el-button
                >
                <el-button
                  class="btn"
                  plain
                  @click="handleDeleteNodeMenu(node, data)"
                  >删除</el-button
                >
              </div>
              <el-button slot="reference" class="font-size-bg" type="text"
                >···</el-button
              >
            </el-popover>
          </div>
        </el-tree>
      </el-card>
      <el-card title="修改菜单" class="menu-container-left">
        <h3 slot="header" class="card-header">修改菜单</h3>
        <s-edit
          v-show="formInfo.id"
          :parentId="parentId"
          :formInfoData="formInfo"
          @finsh="handleRefreshTree"
        >
        </s-edit>
      </el-card>
    </div>
    <el-dialog
      title="新增菜单"
      :visible.sync="dialogVisible"
      @close="dialogVisible = false"
      destroy-on-close
    >
      <s-add
        :parentId="parentId"
        @close="dialogVisible = false"
        @finsh="handleRefreshTree"
      >
      </s-add>
    </el-dialog>
  </div>
</template>

<script>
import { menuPageList, menuRemove } from "@/api/modules/sysMenu.js";
export default {
  components: {
    "s-add": () => import("./components/add.vue"),
    "s-edit": () => import("./components/edit.vue"),
  },
  data() {
    return {
      //=====================================表格表单数据====================================//
      formInfo: {},
      menuData: [], // 获取菜单数据
      //=====================================业务数据====================================//
      parentId: null, // 父级id
      defaultProps: {
        // 菜单树形结构展示字段替换
        children: "children",
        label: "menuName",
      },
      //=====================================其他数据====================================//
      loading: false, // 编辑加载效果
      dialogVisible: false, // 新增表单弹窗控制
      radio: null, // 系统编码选项
      defaultExpanded: [], // 默认展开节点
    };
  },
  created() {
    this.getResourceAllMenuTree();
  },
  methods: {
    //=====================================获取远程数据====================================//
    // 查询所有菜单树
    getResourceAllMenuTree() {
      this.loading = true;
      menuPageList()
        .then((res) => {
          console.log(res);
          this.menuData = res;
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 菜单树排列序更新
    getResourceUpdateSort(source, sortItem) {
      this.loading = true;
      const params = {
        source,
        sortItem,
      };
      this.axios
        .post("/resource/updateSort", params)
        .then((res) => {
          console.log(res);
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
          this.getResourceAllMenuTree(this.radio);
        });
    },
    // 根据id查询菜单详情
    getQueryMenuById(id) {
      const params = { id };
      this.axios.get("/resource/menu", { params }).then((res) => {
        this.formInfo = res.data;
      });
    },
    //=====================================前后端交互====================================//
    // 确认修改菜单
    handleMenuModify() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          const params = this.$refs.form.formInfo;
          this.axios
            .put("/resource/menu", params)
            .then((res) => {
              console.log(res);
            })
            .catch((err) => {
              console.error(err);
            })
            .finally(() => {
              this.loading = false;
              this.getSysEnums1();
            });
        } else {
          this.$nextTick(() => {
            // eslint-disable-next-line no-unused-expressions
            document.querySelector(".el-form-item.is-error input")
              ? document.querySelector(".el-form-item.is-error input").focus()
              : null;
          });
          this.$message.warning("请完善必填信息");
          this.loading = false;
        }
      });
    },
    //=====================================其他操作====================================//
    // 增加根节点菜单
    handleAddRootMenu() {
      this.parentId = null;
      this.dialogVisible = true;
    },
    // 增加子节点菜单
    handleAddNodeMenu(val) {
      console.log("新增节点id", val.id);
      this.parentId = val.id;
      this.dialogVisible = true;
    },
    // 删除节点菜单
    handleDeleteNodeMenu(node, data) {
      this.$confirm("此操作将永久删除此条记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          menuRemove(data.id)
            .then((res) => {
              // 删除节点不刷新
              const parent = node.parent;
              const children = parent.data.children || parent.data;
              const index = children.findIndex((d) => d.id === data.id);
              children.splice(index, 1);
              this.formInfo = {};
            })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.getSysEnums1();
            });
        })
        .catch((err) => {
          if (err === "cancel" || err === "close") {
            return;
          }
          this.$errorThrow(err, this);
        });
    },
    // 节点点击事件
    handleNodeClick(node, ev) {
      // 直接使用原始对象，保留所有属性
      this.formInfo = { ...node };
      console.log("菜单节点数据", this.formInfo);
    },
    // 树型单元拖拽结束
    handleDragEnd(draggingNode, dropNode, dropType) {
      console.log(draggingNode);
      console.log("tree drag end: ", dropNode && dropNode.name);
      if (
        dropType === "after" ||
        dropType === "before" ||
        dropType === "inner"
      ) {
        this.getResourceUpdateSort(this.radio, this.menuData);
      }
    },
    allowDrag(draggingNode) {
      console.log(draggingNode);
      return true;
    },
    // 刷新
    handleRefreshTree() {
      this.getResourceAllMenuTree();
    },
  },
};
</script>

<style lang="scss" scoped>
.menu-container {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  gap: 20px;
  &-left,
  &-right {
    flex: 50%;
    height: calc(100vh - 130px);
    overflow-y: auto;
  }
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30px;
}
.menu-popover {
  flex: 1;
}
.w-full {
  width: 100%;
}
.menu-container-left-btn {
  i {
    color: #409eff;
    cursor: pointer;
    margin-right: 10px;
  }
}
</style>
