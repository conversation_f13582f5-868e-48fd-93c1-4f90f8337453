<template>
  <div class='wrapper'>
    <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot='search'>
        <el-form :inline='true' :model='form'>
          <el-form-item label='图片名称：' class='ml20'>
            <el-input v-model='form.resourceName'></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot='bottom-options'>
        <el-button type='primary' size='mini' @click='handleInsert'>新增</el-button>
      </div>
      <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
        <div slot='options' slot-scope='scope'>
          <el-button type='text' size='mini' @click='handleEdit(scope.row)'>编辑</el-button>
          <el-button type='text' size='mini' @click='handleDel(scope.row)'>删除</el-button>
        </div>
      </simple-table>
    </simple-list-page>
    <detail-dialog ref='detail' />
    <insert-dialog ref='insert' @confirm='handleQuery' />
    <edit-dialog ref='edit' @confirm='handleQuery' />
  </div>
</template>

<script>
import InsertDialog from './components/insert'
import EditDialog from './components/edit'
import {delAppBanner, pageAppBanner} from '@/api/modules/appbanner'

export default {
  components: {InsertDialog, EditDialog},
  data() {
    return {
      form: {
        resourceName: null
      },
      columns: [
        {label: '图片名称', prop: 'resourceName'},
        {label: '创建时间', prop: 'createTime'},
        {label: '排序', prop: 'orderNum'}
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {
        resourceName: null
      }
      this.handleQuery()
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageAppBanner(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleInsert() {
      this.$refs.insert.open()
    },
    handleEdit(row) {
      this.$refs.edit.open(row.id)
    },
    handleDel(row) {
      this.$confirm('确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        delAppBanner({id: row.id}).then(res => {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.loadData()
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang='scss' scoped></style>
