<template>
  <div class='wrapper'>
    <el-dialog
      title='编辑'
      :visible.sync='dialogVisible'
      width='60%'
      @close='close'>
      <el-form :model='form' ref='form' :rules='rules' label-width='120px' label-position='right' size='small'
               class='dialog-form'>
        <el-form-item class='item' label='图片名称：'>
          <el-input v-model='form.resourceName'></el-input>
        </el-form-item>
        <el-form-item class='item' label='首页banner：' prop='imageUrl'>
          <el-upload
            class='img-uploader'
            :action='uploadUrl'
            :headers='headers'
            :show-file-list='false'
            :on-success='handleUploadSuccess'
            :on-remove='handleUploadRemove'
            accept='.png,.jpg,.jpge,.webp'>
            <img v-if='imageUrl' :src='imageUrl' class='img'>
            <i v-else class='el-icon-plus img-uploader-icon'></i>
          </el-upload>
        </el-form-item>
        <el-form-item class='item' label='图片链接地址：'>
          <el-input v-model='form.detailUrl'></el-input>
        </el-form-item>
        <el-form-item class='item' label='排序：'>
          <el-input v-model='form.orderNum' v-int></el-input>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getToken} from '@/utils/auth'
import {preAppBanner, updateAppBanner} from '@/api/modules/appbanner'

export default {
  data() {
    let validateUpload = (rule, value, callback) => {
      if (!this.imageUrl) {
        callback(new Error('请上传首页banner图片'))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false,
      loading: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/oss/endpoint/put-public-file',
      headers: {
        Authorization: getToken()
      },
      form: {
        resourceName: null,//图片名称,
        resourceUrl: null,//首页banner地址（图片完整地址）,
        detailUrl: null,//图片链接地址,
        orderNum: null//排序，数值越大越靠前
      },
      rules: {
        imageUrl: [
          {required: true, validator: validateUpload, trigger: 'change'}
        ]
      },
      imageUrl: null
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.resourceUrl = this.imageUrl
          this.form.id = this.id
          this.loading = true
          updateAppBanner(this.form).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
              this.$emit('confirm')
            }, 500)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    loadData() {
      preAppBanner({id: this.id}).then(res => {
        this.form = res.data
        this.imageUrl = this.form.resourceUrl
      })
    },
    open(id) {
      this.id = id
      this.form = {
        resourceName: null,//图片名称,
        resourceUrl: null,//首页banner地址（图片完整地址）,
        detailUrl: null,//图片链接地址,
        orderNum: null//排序，数值越大越靠前
      }
      this.dialogVisible = true
      this.loadData()
    },
    close() {
      this.dialogVisible = false
      this.imageUrl = null
      this.$refs.form.resetFields()
    },
    handleUploadSuccess(res) {
      this.imageUrl = res.data.link
      this.form.imageUrl = res.data.link
      this.$refs.form.validate()
    },
    handleUploadRemove() {
      this.imageUrl = null
      this.form.imageUrl = null
      this.$refs.form.validate()
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 20%;

  .item {
    width: 70%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.rg1 {
  width: 160px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg2 {
  width: 80px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg-content {
  display: flex;
  align-items: center;
  margin-top: 32px;
}

.img-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 88px;
  height: 88px;
}

.img-uploader:hover {
  border-color: #409EFF;
}

.img-uploader-icon {
  font-size: 14px;
  color: #8c939d;
  width: 88px;
  height: 88px;
  line-height: 88px;
  text-align: center;
}

.img {
  width: 88px;
  height: 88px;
  display: block;
}
</style>
