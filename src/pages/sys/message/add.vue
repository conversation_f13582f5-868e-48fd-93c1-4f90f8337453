<template>
  <simple-page title="新增消息" @back="handleBack">
      <el-form :model="ruleForm"  :rules="rules" ref="ruleForm" label-width="130px" class="demo-ruleForm">
        <el-form-item label="消息名称：" prop="title">
          <el-input v-model="ruleForm.title" maxlength="50" show-word-limit></el-input>
        </el-form-item>
        <el-row :gutter="20">
       <el-form-item label="消息类型：" prop="messageType">
             <el-col :span="6">
              <el-select v-model="ruleForm.messageType" placeholder="请选择消息类型">
                <el-option
                  v-for="item in types"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-col>
       </el-form-item>
          </el-row>
        <el-form-item label="消息内容：" prop="messageContent">  
           <simple-editor class="editor-content" v-model="ruleForm.messageContent" 
           :showImage="false" :showVideo="false"></simple-editor>
           <div style="margin-top: 20px;height:10px;width: 10px"></div>
      </el-form-item>

        <el-form-item style="margin-top: 100px">  
          <el-col :offset="16">
              <span  class="dialog-footer">
              <el-button @click="goList">取 消</el-button>
              <el-button type="primary" @click="submit" v-loading="loading">确 定</el-button>
            </span>
          </el-col>
       
           
      </el-form-item>
      </el-form>
  </simple-page>
</template>

<script>
import {add,update } from '@/api/modules/message.js'
export default {
  data() {
    return {
      loading: false,
      types: [
        {
          value: 1,
          label: '通知'
        },
        {
          value: 2,
          label: '公告'
        },
        {
          value: 3,
          label: '系统消息'
        }
      ],
      ruleForm: {
        title: '',
        messageType: 1,
        messageContent: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入消息名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        messageType: [
          { required: true, message: '请选择消息类型', trigger: 'change' }
        ],
        messageContent: [
          { required: true, message: '请输入消息内容', trigger: 'blur' }
        ],
      }
    }
  },
  mounted() {
  },
  methods: {
    handleClose(done) {
      done()
    },
    // 提交
    submit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.loading = true
          let request;
          if (this.ruleForm.id) {
            request = update(this.ruleForm)
          } else {
            request = add(this.ruleForm)
          }
          request.then(res => {
            this.loading = false
              if (+res.code === 200) {
                this.$message.success(res.msg)
                this.goList()
              } else {
                this.$message.error(res.msg)
              }
              this.handleQuery()
          }).catch(() => {
              this.loading = false
          })
        } else {
          // 登录表单校验失败
          this.$message.error('表单校验失败，请检查')
        }
      })
    },
    goList() {
      this.$router.push({
        path: '/sys/message'
      })
    },
    handleBack() {}
  },
}
</script>
