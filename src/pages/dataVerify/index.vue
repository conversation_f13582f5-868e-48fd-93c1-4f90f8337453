<template>
  <div class='wrapper'>
    <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot='search'>
        <el-form :inline='true' :model='form'>
          <el-form-item label='抽样单号'>
            <el-input v-model='form.sampleCode'></el-input>
          </el-form-item>
          <el-form-item label='抽样时间'>
            <el-date-picker v-model="form.spD38" value-format="yyyy-MM-dd" type="date" placeholder="选择日期"> </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div slot='bottom-options'>
        <el-button type='primary' size='mini' icon='el-icon-plus' @click='downLoad'>导出</el-button>
      </div>
      <simple-table :columns='columns' :data='list' :page='page' :loading="loading" :optionsWidth='200' @load='loadData' :showOptions="false">
        <div slot='isRead' slot-scope="scope">
          {{scope.row.isRead == 1 ? '已读' : '未读'}}
        </div>
      </simple-table>
    </simple-list-page>
  </div>
</template>

<script>
import { screeningDataPage } from '@/api/modules/dataVerify.js'
import { getToken } from '@/utils/auth'
import axios from 'axios'
import Cookies from 'js-cookie'
export default {
  data() {
    return {
      downloadUrl: process.env.VUE_APP_BASE_API + '/screening/screeningDataExport',
      form: {},
      columns: [
        {
          label: '校验字段名称',
          prop: 'filedDesc'
        },
        {
          label: '校验字段值',
          prop: 'text'
        },
        {
          label: '抽样单号',
          prop: 'sampleCode'
        },
        {
          label: '样品名称',
          prop: 'spS14'
        },
        {
          label: '抽样地点',
          prop: 'spS2'
        },
        {
          label: '抽样人员',
          prop: 'spS37'
        },
        {
          label: '抽样时间',
          prop: 'spD38'
        },
        {
          label: '筛查时间',
          prop: 'screeningTime'
        },
        {
          label: '机构名称',
          prop: 'orgName'
        },
        {
          label: '状态',
          prop: 'isRead',
          slot: 'isRead'
        },
        {
          label: '规则编码',
          prop: 'ruleCode'
        },
        {
          label: '校验规则',
          prop: 'ruleName'
        },
        {
          label: '校验结果',
          prop: 'errDesc'
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      loading: false
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    loadData() {
      this.loading = true
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      screeningDataPage(data).then(res => {
        this.loading = false
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    downLoad() {
      //  process.env.VUE_APP_BASE_API + '/sampleTask/downTemplate'
      let params = {
        sampleCode: this.form.sampleCode,
        spD38: this.form.spD38,
      }
      axios({
        method: 'get',
        url: this.downloadUrl,
        responseType: 'blob',
        headers: { 'Authorization': "SSOTGTCookie=" + getToken() + ";ebs_username=" + Cookies.get('ebs_username') },
        params
      }).then(res => {
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', '数据校验.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link)
      })
    }
  }
}
</script>

<style lang='scss' scoped></style>
