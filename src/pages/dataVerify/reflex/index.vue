<template>
  <div class='wrapper'>
    <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot='search'>
        <el-form :inline='true' :model='form'>
          <el-form-item label='机构名称'>
            <el-input v-model='form.orgName'></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot='bottom-options'>
        <el-button type='primary' size='mini' icon='el-icon-plus' @click='addView'>新增</el-button>
      </div>
      <simple-table :columns='columns' :data='list' :page='page' :optionsWidth='200' @load='loadData'>
        <div slot='options' slot-scope='scope'>
          <el-button type='text' size='mini' @click='editView(scope.row)'>编辑</el-button>
          <el-button type='text' size='mini' @click='deleteView(scope.row)'>删除</el-button>
        </div>
      </simple-table>
    </simple-list-page>
    <insert-dialog ref='insert' @confirm='loadData'></insert-dialog>
    <edit-dialog ref='edit' @confirm='loadData'></edit-dialog>
  </div>
</template>

<script>
import { delReflex, screeningReflexPage } from '@/api/modules/dataVerify.js'
import editDialog from './edit'
import insertDialog from './insert'
export default {
  components: { insertDialog, editDialog },
  data() {
    return {
      form: {},
      columns: [
        {
          label: '机构id',
          prop: 'orgid'
        },
        {
          label: '机构名称',
          prop: 'orgName'
        },
        {
          label: '映射id',
          prop: 'reflexId'
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      },
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      screeningReflexPage(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    addView() {
      this.$refs.insert.open()
    },
    editView(row) {
      this.$refs.edit.open(row.id)
    },
    deleteView(row) {
      this.$confirm('确定删除当前数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {

        delReflex({ id: row.id }).then(res => {
          if (+res.code === 200) {
            this.$message.success(res.msg)
            this.loadData()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(result => {
        this.$message({
          type: 'warning',
          message: result.msg
        })
      })
    }
  }
}
</script>

<style lang='scss' scoped></style>
