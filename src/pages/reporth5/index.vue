<template>
  <div class='report'>
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scale=no" />
    <div class='top'>
      <img class='logo' src='@/assets/image/chou.png' alt=''>
      <div class='txt'>
        <p class='t1'>抽样助手</p>
        <p class='t2'>让抽样更简单</p>
      </div>
      <button class='bt' @click='go'>下载</button>
    </div>
    <div class='detail'>
      <div class='shadow-bottom' />
      <p class='title'><span v-if='tag'>{{ tag }}</span><span v-else class='tag'></span>{{ data.title }}</p>
      <p class='info'>{{ '发表于' + data.createTime }}<span>{{ data.commentCount + '个评论'
        }}</span><span>{{ data.collectionCount + '个收藏' }}</span></p>
      <el-image class='head' :src='data.avatar' fit='cover' />
      <p class='nick'>{{ data.nickName }}</p>
      <p class='content'>{{ data.content }}</p>
      <div class='imgs' v-if='data.fileUrls&&data.fileUrls.length>0'>
        <el-image class='img' v-for='u in data.fileUrls' :src='u' fit='cover' />
      </div>
      <div class='imgs' v-if='data.topicList&&data.topicList.length>0'>
        <label class='topic' v-for='t in data.topicList'>{{ '#' + t.topicName }}</label>
      </div>
    </div>
    <!--    <button class='open'>{{ 'APP内打开' }}</button>-->
  </div>
</template>
<script>
import { detailReport } from '@/api/modules/report'

export default {
  name: 'Report',
  data() {
    return {
      loading: false,
      id: '',
      data: {},
      tag: '',
      topics: ['食品添加剂', '食品安全', '学习交流']
    }
  },
  created() {
    this.id = this.$route.query.id
    this.loadData()
  },
  methods: {
    loadData() {
      detailReport(this.id).then(res => {
        this.data = res.data
        if (this.data.typeLabel)
          this.tag = '【' + this.data.typeLabel + '】'
      })
    },
    go() {
      window.location.href = 'https://fir.xcxwo.com/mfxd'
    }
  }
}
</script>

<style rel='stylesheet/scss' lang='scss' scoped>
.report {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  height: 100%;
  background: #F8F8F8;
}

.shadow-bottom {
  height: 10px;
  width: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, .24), rgba(0, 0, 0, 0) 100%);
}

.top {
  width: 100%;
  height: 60px;
  background: white;
  display: flex;

  .logo {
    width: 40px;
    height: 40px;
    margin: 10px;
  }

  .txt {
    width: 200px;
    margin-top: 10px;

    .t1 {
      font-size: 14px;
      line-height: 20px;
    }

    .t2 {
      font-size: 12px;
      line-height: 20px;
    }
  }

  .bt {
    width: 80px;
    height: 32px;
    margin: 14px 10px 14px 10px;
    background: #2A76FD;
    border: 0;
    border-radius: 20px;
    color: white;
    font-size: 16px;
    font-weight: 600;
  }
}

.detail {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  background: white;
  padding-bottom: 8px;

  .title {
    margin-left: 3px;
    line-height: 20px;
    color: #333333;
    font-size: 14px;
    font-weight: 550;

    .tag {
      margin-left: 7px;
    }
  }

  .info {
    width: 100%;
    margin-left: 10px;
    line-height: 17px;
    margin-top: 2px;
    font-size: 12px;
    color: #999999;

    span {
      margin-left: 10px;
    }
  }

  .head {
    width: 18px;
    height: 18px;
    margin-left: 10px;
    margin-top: 8px;
    border-bottom: 1px solid #969696;
    border-radius: 9px;
  }

  .nick {
    line-height: 18px;
    margin-top: 8px;
    margin-left: 6px;
    color: #666666;
    font-size: 12px;
  }

  .content {
    width: 100%;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    padding-left: 10px;
    padding-right: 10px;
    margin-top: 8pt;
    white-space: pre-wrap;
  }

  .imgs {
    margin-left: 10px;
    display: flex;
    flex-wrap: wrap;
    margin-top: 4px;
    align-content: flex-start;

    .img {
      border-radius: 6px;
      width: 102px;
      height: 102px;
      margin-top: 4px;
      margin-right: 4px;
    }

    .topic {
      height: 22px;
      border-radius: 11px;
      line-height: 22px;
      padding-left: 6px;
      padding-right: 6px;
      margin-top: 4px;
      margin-right: 6px;
      background: #E3EDFB;
      color: #2A76FD;
      font-size: 12px;
    }
  }
}

.open {
  width: 120px;
  height: 40px;
  margin: 100px auto 0 auto;
  background: #2A76FD;
  border: 0;
  border-radius: 20px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 0 10px #2A76FD;
}
</style>
