<template>
  <div style="width: 100%;">
    <el-tabs v-model="tabIndex" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="0"></el-tab-pane>
      <el-tab-pane label="计划详情" name="1"></el-tab-pane>
    </el-tabs>
    <info v-if="tabIndex == 0"></info>
    <plan v-if="tabIndex == 1"></plan>
  </div>
</template>

<script>
import info from '../info/index.vue'
import plan from '../plan/index.vue'

export default {
  components: { info, plan },
  data() {
    return {
      tabIndex: 0
    }
  },
  methods: {
    handleClick(tab, event) {
      this.tabIndex == tab.name
    }
  }
}
</script>

<style>
</style>