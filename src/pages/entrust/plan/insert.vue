<template>
  <div class='wrapper'>
    <el-dialog title='新增' :visible.sync='dialogVisible' width='60%' @close='close'>
      <el-form :model='form' ref='form' :rules='rules' label-width='180px' label-position='right' size='small' class='dialog-form'>
        <el-form-item class='item' label='报送分类B：' prop='classB'>
          <!-- <el-input v-model='form.classB'></el-input> -->
          <el-select v-model="form.classB" filterable placeholder="请选择" style="width:100%;">
            <el-option v-for="(item, index) in classBList" :key="index" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class='item' label='食品大类：' prop='newscbname'>
          <!-- <el-cascader v-model="value" :options="options" :props="{label:'label',value: 'label', multiple: true}" @change="handleChange"></el-cascader> -->
          <el-cascader style="width:100%" v-model="form.newscbname" :show-all-levels="true" popper-class="first-no-check-cascader" filterable :options="treeList" @expand-change="changeSourceType"
            :props="{label:'cateName',value: 'cateName', children: 'children', multiple: true}">
          </el-cascader>
        </el-form-item>
        <el-form-item class='item' label='食品分类（未指定分类）：'>
          <el-input v-model='form.foodCate'></el-input>
        </el-form-item>
        <el-form-item class='item' label='计划数量：' prop="total">
          <el-input v-model='form.total'></el-input>
          <!-- <el-date-picker v-model="form.testDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"> </el-date-picker> -->
        </el-form-item>
        <el-form-item class='item' label='年份：'>
          <el-input v-model='form.year'></el-input>
        </el-form-item>
        <el-form-item class='item' label='计划抽样完成日期：'>
          <!-- <el-input v-model='form.plannedCompletionTime'></el-input> -->
          <el-date-picker v-model="form.plannedCompletionTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"> </el-date-picker>
        </el-form-item>
        <el-form-item class='item' label='报告出具日期：'>
          <el-date-picker v-model="form.reportTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"> </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { findTreeListAll, insertSampleCate, selectClassB } from '@/api/modules/entrust.js';

export default {
  data() {
    return {
      value: [],
      options: [],
      classBList: [],
      rules: {
        classB: [
          { required: true, message: '请输入报送分类B', trigger: 'blur' }
        ],
        newscbname: [
          { required: true, message: '请选择食品分类', trigger: 'change' }
        ],
        total: [
          { required: true, message: '请输入计划数量', trigger: 'blur' }
        ]
      },
      treeList: [],
      dialogVisible: false,
      loading: false,
      form: {
        scbname: '',
        newscbname: [],
        plannedCompletionTime: '',
        reportTime: '',
        year: ''
      },
      newscbname: [],
      cascaderTag: [], // 暂存点击时已有的tag
    }
  },
  methods: {
    changeSourceType(selectItem) {

      // 一级分类只能选一个，如果一级分类修改了，清空已有值
      if (!this.cascaderTag.includes(selectItem[0])) {
        this.newscbname = [];
      }
      this.cascaderTag = selectItem;
    },

    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          let params = this.form
          if (this.form.reportTime) {
            params.reportTime = this.form.reportTime + " 23:59:59"
          }
          if (this.form.plannedCompletionTime) {
            params.plannedCompletionTime = this.form.plannedCompletionTime + " 23:59:59"
          }
          insertSampleCate(params).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
              this.$emit('confirm')
            }, 500)
          }).catch(result => {
            this.$message({
              type: 'warning',
              message: result.msg
            })
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    selectClassB() {
      selectClassB().then(res => {
        this.classBList = res.data
      })
    },
    findTreeListAll() {
      findTreeListAll().then(res => {

        const aaaa = this.getTypeList(res.data)
        this.treeList = this.getTypeList(res.data)
      })
    },
    getTypeList(listData) {
      listData.forEach((items) => {
        if (items.children && items.children.length > 0) {
          this.getTypeList(items.children)
        } else {
          items.children = undefined
        }
      })
      return listData
    },
    open() {
      this.form = {

      }
      this.selectClassB()
      this.findTreeListAll()
      this.dialogVisible = true

    },
    close() {
      this.dialogVisible = false
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 20%;

  .item {
    width: 70%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.rg1 {
  width: 160px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg2 {
  width: 80px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg-content {
  display: flex;
  align-items: center;
  margin-top: 32px;
}

.img-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 88px;
  height: 88px;
}

.img-uploader:hover {
  border-color: #409eff;
}

.img-uploader-icon {
  font-size: 14px;
  color: #8c939d;
  width: 88px;
  height: 88px;
  line-height: 88px;
  text-align: center;
}

.img {
  width: 88px;
  height: 88px;
  display: block;
}
</style>
<style lang='scss'>
.first-no-check-cascader {
  .el-cascader-panel {
    // height: 400px !important;
    .el-cascader-menu:first-child {
      .el-cascader-node {
        .el-checkbox {
          display: none !important;
        }
      }
    }
  }
}
</style>