<template>
  <div class='wrapper'>
    <el-dialog title='编辑' :visible.sync='dialogVisible' width='60%' @close='close'>
      <el-form :model='form' ref='form' :rules='rules' label-width='120px' label-position='right' size='small' class='dialog-form'>
        <el-form-item class='item' label='计划数量：' prop='scbname'>
          <el-input-number v-model='form.total' :min="0"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消
</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { updateTotal } from '@/api/modules/entrust'

export default {
  data() {
    return {
      id: '',
      dialogVisible: false,
      loading: false,
      form: {
        total: 0,
        key: ''
      },
      rules: {
        total: [
          { required: true, message: '请输入计划数量', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.id = this.id
          this.loading = true
          updateTotal(this.form).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
              this.$emit('confirm')
            }, 500)
          }).catch(result => {
            this.$message({
              type: 'warning',
              message: result.msg
            })
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },

    open(dic) {
      this.form = {
        key: dic.key,
        total: +dic.total
      }
      this.dialogVisible = true
      this.loadData(id)
    },
    close() {
      this.dialogVisible = false
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 20%;

  .item {
    width: 70%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.rg1 {
  width: 160px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg2 {
  width: 80px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg-content {
  display: flex;
  align-items: center;
  margin-top: 32px;
}

.img-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 88px;
  height: 88px;
}

.img-uploader:hover {
  border-color: #409eff;
}

.img-uploader-icon {
  font-size: 14px;
  color: #8c939d;
  width: 88px;
  height: 88px;
  line-height: 88px;
  text-align: center;
}

.img {
  width: 88px;
  height: 88px;
  display: block;
}
</style>
