<template>
  <div class='wrapper'>
    <simple-list-page @query='handleQuery' @reset='handleReset'>
      <div slot='search'>
        <el-form :inline='true' :model='form'>
          <el-form-item label='报送分类B：'>
            <el-select v-model="form.classB" filterable placeholder="请选择" style="width:100%;">
              <el-option v-for="(item, index) in classBList" :key="index" :label="item" :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label='食品大类：'>
            <el-select v-model="form.cate1" filterable placeholder="请选择" style="width:100%;"  :props="{label:'cateName',value: 'cateName'}">
              <el-option v-for="(item, index) in treeList" :key="index" :label="item.cateName" :value="item.cateName">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label='食品分类（未指定分类）：'>
            <el-input v-model='form.foodCate' placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label='计划抽样完成日期：'>
            <!-- <el-input v-model='form.plannedCompletionTime'></el-input> -->
            <el-date-picker
              v-model="form.plannedCompletionTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div slot='bottom-options'>
        <el-button type='primary' size='mini' icon='el-icon-plus' @click='addView'>新增</el-button>
        <el-button type='primary' size='mini' icon='el-icon-plus' @click='showWpload'>导入</el-button>
      </div>
      <simple-table :columns='columns' :data='list' :page='page' :optionsWidth='200' @load='loadData'>
        <div slot='options' slot-scope='scope'>
          <el-button type='text' size='mini' @click='editView(scope.row)'>编辑</el-button>
          <el-button type='text' size='mini' @click='deleteView(scope.row)'>删除</el-button>
        </div>
      </simple-table>
    </simple-list-page>
    <insert-dialog ref='insert' @confirm='loadData'></insert-dialog>
    <edit-dialog ref='edit' @confirm='loadData'></edit-dialog>

    <el-dialog title='导入' :visible.sync='uploadFormVisible' :before-close="handleClose" width='400px'>
      <el-form>
        <el-form-item>
          <div class="el-upload__text">请按模板规定方式填写上传文件<el-button type='text' @click="downClick()">下载模板</el-button></div>
          <el-upload ref="upload" id="upload" class="upload-demo" drag :action="uploadUrl" :headers="headers" :auto-upload='true' name='file' :before-upload="beforeAvatarUpload" :on-success="handleFileSuccess"
            :on-error="handleFileError">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <!-- <div class="el-upload__tip" slot="tip">只能上传Excel文件,<a :href='downloadUrl'>下载模板</a> </div> -->
          </el-upload>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { deleteSampleCate, selectClassB, selectSampleCateData, findTreeListAll } from '@/api/modules/entrust.js'
import { getToken } from '@/utils/auth'
import axios from 'axios'
import Cookies from 'js-cookie'
import editDialog from './edit'
import insertDialog from './insert'
import dayjs from 'dayjs'

export default {
  components: { insertDialog, editDialog },
  data() {
    return {
      classBList: [],
      headers: {
        Authorization: "SSOTGTCookie=" + getToken() + ";ebs_username=" + Cookies.get('ebs_username')
      },
      uploadFormVisible: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/sampleTask/sampleCatesImports',
      downloadUrl: process.env.VUE_APP_BASE_API + '/sampleTask/downTemplate/',
      form: {},
      columns: [
        {
          label: '报送分类B',
          prop: 'classB'
        },
        {
          label: '食品大类',
          prop: 'cate1'
        },
        {
          label: '食品细类',
          prop: 'cate4',
          showOverflowTooltip: true
        },
        {
          label: '食品分类（未指定分类）',
          prop: 'foodCate'
        },
        {
          label: '计划数',
          prop: 'total'
        },
        {
          label: '年份',
          prop: 'year'
        },
        {
          label: '计划抽样完成日期',
          prop: 'plannedCompletionTime',
          formatter(row, column, cellValue, index) {
            return  row.plannedCompletionTime ? dayjs(row.plannedCompletionTime).format("YYYY-MM-DD") : "-"
          }
        },
        {
          label: '报告出具日期',
          prop: 'reportTime',
          formatter(row, column, cellValue, index) {
            return  row.reportTime ? dayjs(row.reportTime).format("YYYY-MM-DD") : "-"
          }
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      treeList: [],
      cascaderTag: [], // 暂存点击时已有的tag
    }
  },
  mounted() {
    this.loadData()
    this.selectClassB()
    this.findTreeListAll()
  },
  methods: {
    selectClassB() {
      selectClassB().then(res => {
        this.classBList = res.data
      })
    },
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      if (this.form.plannedCompletionTime && this.form.plannedCompletionTime.length) {
        data.startDate = dayjs(this.form.plannedCompletionTime[0]).format(
          "YYYY-MM-DD"
        );
        data.endDate = dayjs(this.form.plannedCompletionTime[1]).format("YYYY-MM-DD");
        delete data.plannedCompletionTime
      }
      selectSampleCateData(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    addView() {
      this.$refs.insert.open()
    },
    editView(row) {
      this.$refs.edit.open({ key: row.key, total: row.total })
    },
    deleteView(row) {
      this.$confirm('确定删除当前数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        deleteSampleCate({ key: row.key }).then(res => {
          if (+res.code === 200) {
            this.$message.success(res.msg)
            this.loadData()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(result => {
        this.$message({
          type: 'warning',
          message: "取消删除操作"
        })
      })
    },
    handleClose(done) {
      done();
    },
    showWpload() {
      this.uploadFormVisible = true
    },
    beforeAvatarUpload(file) {
      const isXLSX = file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      if (!isXLSX) {
        this.$message.error('上传文件只能是 xls/xlsx 格式!')
      }
      return isXLSX
    },
    // 文件上传成功处理
    handleFileSuccess(res, file, fileList) {
      this.uploadFormVisible = false
      this.$refs.upload.clearFiles()
      if (res.code == 200) {
        this.$alert('导入成功', "导入结果", {
          dangerouslyUseHTMLString: true
        });
      } else {
        // .join('<br/>')
        this.$alert(res.msg, '导入结果', { dangerouslyUseHTMLString: true })
      }
      this.loadData()
    },
    handleFileError(response, file, fileList) {

      this.$alert('导入失败', "导入结果", {
        dangerouslyUseHTMLString: true
      });
    },
    downClick() {
      axios({
        method: 'get',
        url: process.env.VUE_APP_BASE_API + '/sampleTask/downTemplate',
        responseType: 'blob',
        headers: { 'Authorization': "SSOTGTCookie=" + getToken() + ";ebs_username=" + Cookies.get('ebs_username') }
      }).then(res => {
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', '导入模板.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link)
      })
    },
    // 食品分类树型列表查询
    findTreeListAll() {
      findTreeListAll().then(res => {
        this.treeList = res.data
      })
    },
  }
}
</script>

<style lang='scss' scoped></style>
