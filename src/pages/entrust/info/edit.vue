<template>
  <div class='wrapper'>
    <el-dialog title='编辑' :visible.sync='dialogVisible' width='60%' @close='close'>
      <el-form :model='form' ref='form' :rules='rules' label-width='140px' label-position='right' size='small' class='dialog-form'>
        <el-form-item class='item' label='报送分类A：' prop='newscbname'>
          <el-input v-model='form.newscbname'></el-input>
        </el-form-item>
        <el-form-item class='item' label='报送分类B：' prop='scbname'>
          <el-input v-model='form.scbname'></el-input>
        </el-form-item>
        <el-form-item class='item' label='年份：'>
          <el-input v-model='form.year'></el-input>
        </el-form-item>
        <el-form-item class='item' label='计划抽样完成日期：'>
          <!-- <el-input v-model='form.planFinishDate'></el-input> -->
          <el-date-picker v-model="form.planFinishDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"> </el-date-picker>
        </el-form-item>
        <el-form-item class='item' label='报告出具日期：'>
          <el-date-picker v-model="form.testDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"> </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { selectXrHnyById, updateXrHny } from '@/api/modules/entrust'

export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      form: {
        newscbname: '',
        scbname: '',
        year: '',
        planFinishDate: '',
        testDate: ''
      },
      rules: {
        newscbname: [
          { required: true, message: '请输入报送分类A', trigger: 'blur' }
        ],
        scbname: [
          { required: true, message: '请输入报送分类B', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.id = this.id
          this.loading = true
          updateXrHny(this.form).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
              this.$emit('confirm')
            }, 500)
          }).catch(result => {
            this.$message({
              type: 'warning',
              message: result.msg
            })
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    loadData() {
      selectXrHnyById({ id: this.id }).then(res => {
        if (res.code !== 200) {
          return this.$message.error(res.msg)
        }
        this.form = res.data
      }).catch((err) => {
        this.$message.error(err.msg)
      })
    },
    open(id) {
      this.id = id
      this.form = {
        resourceName: null,//图片名称,
        resourceUrl: null,//首页banner地址（图片完整地址）,
        detailUrl: null,//图片链接地址,
        orderNum: null//排序，数值越大越靠前
      }
      this.dialogVisible = true
      this.loadData()
    },
    close() {
      this.dialogVisible = false
      this.imageUrl = null
      this.$refs.form.resetFields()
    },
    handleUploadSuccess(res) {
      this.imageUrl = res.data.link
      this.form.imageUrl = res.data.link
      this.$refs.form.validate()
    },
    handleUploadRemove() {
      this.imageUrl = null
      this.form.imageUrl = null
      this.$refs.form.validate()
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 20%;

  .item {
    width: 70%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.rg1 {
  width: 160px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg2 {
  width: 80px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg-content {
  display: flex;
  align-items: center;
  margin-top: 32px;
}

.img-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 88px;
  height: 88px;
}

.img-uploader:hover {
  border-color: #409eff;
}

.img-uploader-icon {
  font-size: 14px;
  color: #8c939d;
  width: 88px;
  height: 88px;
  line-height: 88px;
  text-align: center;
}

.img {
  width: 88px;
  height: 88px;
  display: block;
}
</style>
