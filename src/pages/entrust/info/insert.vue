<template>
  <div class='wrapper'>
    <el-dialog title='新增' :visible.sync='dialogVisible' width='60%' @close='close'>
      <el-form :model='form' ref='form' :rules='rules' label-width='140px' label-position='right' size='small' class='dialog-form'>
        <el-form-item class='item' label='报送分类A：' prop='newscbname'>
          <el-input v-model='form.newscbname'></el-input>
        </el-form-item>
        <el-form-item class='item' label='报送分类B：' prop='scbname'>
          <el-input v-model='form.scbname'></el-input>
        </el-form-item>
        <el-form-item class='item' label='年份：'>
          <el-input v-model='form.year'></el-input>
        </el-form-item>
        <el-form-item class='item' label='计划抽样完成日期：'>
          <!-- <el-input v-model='form.planFinishDate'></el-input> -->
          <el-date-picker v-model="form.planFinishDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"> </el-date-picker>
        </el-form-item>
        <el-form-item class='item' label='报告出具日期：'>
          <!-- <el-input v-model='form.testDate'></el-input> -->
          <el-date-picker v-model="form.testDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"> </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button size='small' @click='close'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='loading'>提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { insertXrHny } from '@/api/modules/entrust.js'

export default {
  data() {
    let validateUpload = (rule, value, callback) => {
      if (!this.imageUrl) {
        callback(new Error('请上传首页banner图片'))
      } else {
        callback()
      }
    }
    return {
      rules: {
        newscbname: [
          { required: true, message: '请输入报送分类A', trigger: 'blur' }
        ],
        scbname: [
          { required: true, message: '请输入报送分类B', trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      loading: false,
      form: {
        scbname: '',
        year: '',
        planFinishDate: '',
        testDate: ''
      }
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.resourceUrl = this.imageUrl
          this.loading = true
          insertXrHny(this.form).then(result => {
            this.$message({
              type: 'success',
              message: result.msg
            })
            setTimeout(() => {
              this.close()
              this.$emit('confirm')
            }, 500)
          }).finally(() => {
            this.loading = false
          }).catch(result => {
            this.$message({
              type: 'warning',
              message: result.msg
            })
          })
        }
      })
    },
    open() {
      this.form = {

      }
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-footer {
}

.dialog-form {
  margin: 20px 0 20px 20%;

  .item {
    width: 70%;

    .content {
      width: 100%;
      display: flex;
      align-items: center;

      .in {
        width: 68%;
        margin-right: auto;
      }
    }
  }
}

.rg1 {
  width: 160px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg2 {
  width: 80px;

  .ra {
    height: 32px;
    margin-top: 8px;
    text-align: center;
  }
}

.rg-content {
  display: flex;
  align-items: center;
  margin-top: 32px;
}

.img-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 88px;
  height: 88px;
}

.img-uploader:hover {
  border-color: #409eff;
}

.img-uploader-icon {
  font-size: 14px;
  color: #8c939d;
  width: 88px;
  height: 88px;
  line-height: 88px;
  text-align: center;
}

.img {
  width: 88px;
  height: 88px;
  display: block;
}
</style>
