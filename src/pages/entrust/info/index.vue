<template>
  <div class="wrapper">
    <simple-list-page @query="handleQuery" @reset="handleReset">
      <div slot="search">
        <el-form :inline="true" :model="form">
          <el-form-item label="报送分类B：">
            <el-input v-model="form.scbname" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="计划抽样完成日期：">
            <!-- <el-input v-model='form.planFinishDate'></el-input> -->
            <el-date-picker
              v-model="form.planFinishDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div slot="bottom-options">
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="addView"
          >新增</el-button
        >
      </div>
      <simple-table
        :columns="columns"
        :data="list"
        :page="page"
        :optionsWidth="200"
        @load="loadData"
      >
        <div slot="options" slot-scope="scope">
          <el-button type="text" size="mini" @click="editView(scope.row)"
            >编辑</el-button
          >
          <el-button type="text" size="mini" @click="deleteView(scope.row)"
            >删除</el-button
          >
        </div>
      </simple-table>
    </simple-list-page>
    <insert-dialog ref="insert" @confirm="loadData"></insert-dialog>
    <edit-dialog ref="edit" @confirm="loadData"></edit-dialog>
  </div>
</template>

<script>
import { deleteXrHny, selectXrHnyList } from "@/api/modules/entrust.js";
import editDialog from "./edit";
import insertDialog from "./insert";
import dayjs from "dayjs";
export default {
  components: { insertDialog, editDialog },
  data() {
    return {
      form: {},
      columns: [
        {
          label: "报送分类A",
          prop: "newscbname",
        },
        {
          label: "报送分类B",
          prop: "scbname",
        },
        {
          label: "年份",
          prop: "year",
        },
        {
          label: "计划抽样完成日期",
          prop: "planFinishDate",
        },
        {
          label: "报告出具日期",
          prop: "testDate",
        },
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10,
      },
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10,
      };
      this.loadData();
    },
    handleReset() {
      this.form = {};
      this.handleQuery();
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size,
      };
      if (this.form.planFinishDate && this.form.planFinishDate.length) {
        data.startDate = dayjs(this.form.planFinishDate[0]).format(
          "YYYY-MM-DD"
        );
        data.endDate = dayjs(this.form.planFinishDate[1]).format("YYYY-MM-DD");
         delete data.planFinishDate
      }
      selectXrHnyList(data).then((res) => {
        this.list = res.data.records;
        this.page.total = res.data.total;
      });
    },
    addView() {
      this.$refs.insert.open();
    },
    editView(row) {
      this.$refs.edit.open(row.id);
    },
    deleteView(row) {
      this.$confirm("确定删除当前数据吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(() => {
          deleteXrHny({ id: row.id }).then((res) => {
            if (+res.code === 200) {
              this.$message.success(res.msg);
              this.loadData();
            } else {
              this.$message.error(res.msg);
            }
          });
        })
        .catch((result) => {
          this.$message({
            type: "warning",
            message: "取消删除操作",
          });
        });
    },
  },
};
</script>

<style lang="scss">
.el-date-editor .el-range-separator {
  width: 24px !important;
}
</style>
