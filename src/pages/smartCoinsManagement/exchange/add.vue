<template>
  <simple-page title='新增兑换商品' @back='handleBack'>
    <el-form :model='ruleForm' :rules='rules' ref='ruleForm' label-width='130px' class='demo-ruleForm'>
      <el-form-item label='商品类型：' prop='goodsType'>
        <el-select v-model='ruleForm.goodsType' placeholder='请选择常见问题类型'>
          <el-option
            v-for='item in typeList'
            :key='item.value'
            :label='item.label'
            :value='item.value'>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label='商品名称：' prop='goodsName'>
        <el-col :span='12'>
          <el-input v-model='ruleForm.goodsName'></el-input>
        </el-col>
      </el-form-item>

      <el-form-item label='展示图：' prop='showPic'>
        <el-upload
          :action='uploadUrl'
          :headers='headers'
          list-type='picture-card'
          :file-list='ruleForm.showPics'
          :on-success='handleAvatarSuccess'
          :on-remove='handleRemove'
          accept='.png,.jpg,.jpge,.webp'>
          <i class='el-icon-plus'></i>
        </el-upload>
      </el-form-item>
      <p style='font-size:13px; margin-left:130px;'>建议尺寸：694*694像素</p>

      <el-form-item label='智能币：' prop='smartCoinsPrice'>
        <el-col :span='12'>
          <el-input-number v-model='ruleForm.smartCoinsPrice' size='medium' :min='0' :max='99999999'></el-input-number>
          <span style='margin-left:20px;'>智能币  请输入≥0的整数</span>
        </el-col>
      </el-form-item>

      <el-form-item label='会员天数：' v-if='ruleForm.goodsType == 1' prop='memberDays'>
        <el-col :span='12'>
          <el-input-number v-model='ruleForm.memberDays' size='medium' :min='0' :max='99999999'></el-input-number>
          <span style='margin-left:20px;'>天 请输入≥0的整数</span>
        </el-col>
      </el-form-item>

      <el-form-item label='优惠金额：' v-if='ruleForm.goodsType == 2' prop='fullAmount'>
        <el-col :span='1'>
          满
        </el-col>
        <el-col :span='4'>
          <el-input-number v-model='ruleForm.fullAmount' size='medium' :min='0' :max='99999999'></el-input-number>
        </el-col>
        <el-col :span='2'>
          元，优惠
        </el-col>
        <el-col :span='8'>
          <el-input-number v-model='ruleForm.discountAmount' size='medium' :min='0' :max='99999999'></el-input-number>
          <span style='margin-left:20px;'>元 请输入≥0的整数</span>
        </el-col>
      </el-form-item>

      <el-form-item label='库存数量：' prop='inventoryQuantity'>
        <el-col :span='12'>
          <el-input-number v-model='ruleForm.inventoryQuantity' size='medium' :min='0'
                           :max='99999999'></el-input-number>
          <span style='margin-left:20px;'>请输入≥0的整数</span>
        </el-col>
      </el-form-item>

      <el-form-item label='库存预警：' prop='inventoryWarningCount'>
        <el-col :span='12'>
          <el-input-number v-model='ruleForm.inventoryWarningCount' size='medium' :min='0'
                           :max='99999999'></el-input-number>
          <span style='margin-left:20px;'>默认为0不提醒。当设置数量后，达到库存值就会在商品列表展示</span>
        </el-col>
      </el-form-item>

<!--      <el-form-item label='库存计算方式：' prop='inventoryQuantityFunc'>-->
<!--        <el-row>-->
<!--          <el-col :span='12'>-->
<!--            <template>-->
<!--              <el-radio-group v-model='ruleForm.inventoryQuantityFunc'>-->
<!--                <el-radio :label='1'>付款减库存</el-radio>-->
<!--                <el-radio :label='2'>下单减库存</el-radio>-->
<!--              </el-radio-group>-->
<!--            </template>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--      </el-form-item>-->

      <el-form-item label='商品简介：' prop='goodsIntroduction'>
        <el-input
          type='textarea'
          :rows='2'
          placeholder='请输入内容'
          v-model='ruleForm.goodsIntroduction'>
        </el-input>
      </el-form-item>
      <el-form-item label='使用时间：' v-if='ruleForm.goodsType == 2' prop='useTimeType'>
        <el-row>
          <el-col :span='4'>
            <el-radio-group v-model='ruleForm.useTimeType' @change='useTimeTypeChange'>
              <el-radio :label='1'>不限</el-radio>
              <el-radio :label='2'>自定义</el-radio>
            </el-radio-group>
          </el-col>
          <el-col :span='12'>
            <template>
              <el-date-picker
                :disabled='useTimeTypeDisabled'
                v-model='ruleForm.useTimes'
                format='yyyy-MM-dd'
                value-format='yyyy-MM-dd'
                type='daterange'
                align='right'
                unlink-panels
                range-separator='至'
                start-placeholder='开始日期'
                end-placeholder='结束日期'>
              </el-date-picker>
            </template>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label='售卖时间：' prop='saleTimeType'>
        <el-row>
          <el-col :span='4'>
            <template>
              <el-radio-group v-model='ruleForm.saleTimeType' @change='saleTimeTypeChange'>
                <el-radio :label='1'>不限</el-radio>
                <el-radio :label='2'>自定义</el-radio>
              </el-radio-group>
            </template>
          </el-col>
          <el-col :span='12'>
            <template>
              <el-date-picker
                :disabled='saleTimeTypeDisabled'
                v-model='ruleForm.saleTimes'
                format='yyyy-MM-dd'
                value-format='yyyy-MM-dd'
                type='daterange'
                align='right'
                unlink-panels
                range-separator='至'
                start-placeholder='开始日期'
                end-placeholder='结束日期'>
              </el-date-picker>
            </template>
          </el-col>
        </el-row>
      </el-form-item>


      <el-form-item label='每人限购次数：' prop='everyOneCapType'>
        <el-col :span='4'>
          <template>
            <el-radio-group v-model='ruleForm.everyOneCapType'>
              <el-radio :label='1'>不限</el-radio>
              <el-radio :label='2'>每人兑换</el-radio>
            </el-radio-group>
          </template>
        </el-col>
        <el-col :span='6'>
          <el-input-number v-model='ruleForm.everyOneCap' :disabled='ruleForm.everyOneCapType == 1'
                           size='mini' :min='0' :max='99999999'></el-input-number>
          <span style='margin-left: 20px;'>次</span>
        </el-col>
      </el-form-item>


<!--      <el-form-item label='排序：'>-->
<!--        <el-col :span='4'>-->
<!--          <template>-->
<!--            <el-radio-group v-model='ruleForm.orderNumType'>-->
<!--              <el-radio :label='1'>置顶</el-radio>-->
<!--              <el-radio :label='2'>自定义</el-radio>-->
<!--            </el-radio-group>-->
<!--          </template>-->
<!--        </el-col>-->
<!--        <el-col :span='6'>-->
<!--          <el-input-number v-model='ruleForm.orderNum' :disabled='ruleForm.orderNumType == 1'-->
<!--                           size='mini' :min='0' :max='99999999'></el-input-number>-->
<!--        </el-col>-->
<!--      </el-form-item>-->
      <el-form-item label='状态：' prop='goodsStatus'>
        <el-switch
          v-model='ruleForm.goodsStatus'
          active-color='#13ce66'
          inactive-color='#ff4949'
          :active-value='1'
          :inactive-value='2'
          active-text='开'
          inactive-text='关'>
        </el-switch>
      </el-form-item>
      <el-form-item style='margin-top: 100px'>
        <el-col :offset='16'>
              <span class='dialog-footer'>
              <el-button @click='goList'>取 消</el-button>
              <el-button type='primary' @click='submit' v-loading='loading'>确 定</el-button>
            </span>
        </el-col>
      </el-form-item>


    </el-form>

  </simple-page>
</template>

<script>
import {pageList, add, detail, update} from '@/api/modules/exchange.js'
import {getToken} from '@/utils/auth'

export default {
  data() {
    return {
      loading: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/oss/endpoint/put-public-file',
      headers: {
        Authorization: getToken()
      },
      saleTimeTypeDisabled: true,
      useTimeTypeDisabled: true,
      typeList: [
        {
          value: 1,
          label: '会员'
        },
        {
          value: 2,
          label: '优惠券'
        },
        {
          value: 3,
          label: '虚拟商品'
        }
      ],
      fileList: [],
      ruleForm: {
        goodsName: '',
        goodsType: 1,
        smartCoinsPrice: undefined,
        inventoryQuantity: undefined,
        memberDays: undefined,
        useTimeType: 1,
        useTimes: [],
        saleTimeType: 1,
        saleTimes: [],
        everyOneCapType: 1,
        everyOneCap: undefined,
        orderNumType: 1,
        orderNum: undefined,
        goodsStatus: 1,
        showPics: [],
        showPic: ''
      },
      rules: {
        goodsName: [
          {required: true, message: '请输入商品名称', trigger: 'blur'},
          {min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur'}
        ],
        showPic: [
          {required: true, message: '请上传展示图', trigger: 'blur'}
        ],
        smartCoinsPrice: [
          {required: true, message: '请输入智能币', trigger: 'blur'}
        ],
        memberDays: [
          {required: true, message: '请输入会员天数', trigger: 'blur'}
        ],
        inventoryQuantity: [
          {required: true, message: '请输入库存数量', trigger: 'blur'}
        ],
        inventoryWarningCount: [
          {required: true, message: '请输入库存预警', trigger: 'blur'}
        ],
        // inventoryQuantityFunc: [
        //   {required: true, message: '请选择库存计算方式', trigger: 'change'}
        // ],
        goodsIntroduction: [
          {required: true, message: '请输入简介', trigger: 'blur'}
        ],
        useTimeType: [
          {required: true, message: '请选择使用时间类型', trigger: 'change'}
        ],
        saleTimeType: [
          {required: true, message: '请选择售卖时间类型', trigger: 'change'}
        ],
        everyOneCapType: [
          {required: true, message: '请选择每人限购次数类型', trigger: 'change'}
        ],
        goodsStatus: [
          {required: true, message: '请选择状态', trigger: 'change'}
        ],
        fullAmount: [
          {required: true, message: '请输入满减金额', trigger: 'blur'}
        ]
      }
    }
  },
  mounted() {

  },
  methods: {
    // 提交
    submit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          // 验证表单
          let isVaild = this.validForm()
          if (!isVaild) {
            return
          }
          if (this.ruleForm.useTimes && this.ruleForm.useTimes.length > 0) {
            this.ruleForm.useTime = this.ruleForm.useTimes[0] + '~' + this.ruleForm.useTimes[1]
          }
          if (this.ruleForm.saleTimes && this.ruleForm.saleTimes.length > 0) {
            this.ruleForm.saleTime = this.ruleForm.saleTimes[0] + '~' + this.ruleForm.saleTimes[1]
          }
          if (this.ruleForm.showPics && this.ruleForm.showPics.length > 0) {
            let list = []
            this.ruleForm.showPics.forEach(file => {
              list.push(file.url)
            })
            this.ruleForm.showPic = list.join(';')
          }
          this.loading = true
          add(this.ruleForm).then(res => {
            this.loading = false
            if (+res.code === 200) {
              this.$message.success(res.msg)
              this.$router.push({
                path: '/smartCoinsManagement/exchange'
              })
            } else {
              this.$message.error(res.msg)
            }
            this.handleQuery()
          }).catch(() => {
            this.loading = false
          })
        } else {
          this.loading = false
          // 登录表单校验失败
          this.$message.error('表单校验失败，请检查')
        }
      })
    },

    validForm() {
      if (this.ruleForm.useTimeType == 2 && (!this.ruleForm.useTimes ||
        this.ruleForm.useTimes && this.ruleForm.useTimes.length <= 0)) {
        this.$message.error('请选择自定义使用时间')
        return false
      }
      if (this.ruleForm.saleTimeType == 2 && (!this.ruleForm.saleTimes ||
        this.ruleForm.saleTimes && this.ruleForm.saleTimes.length <= 0)) {
        this.$message.error('请选择自定义售卖时间')
        return false
      }
      if (this.ruleForm.everyOneCapType == 2 && (!this.ruleForm.everyOneCap ||
        this.ruleForm.everyOneCap && this.ruleForm.everyOneCap <= 0)) {
        this.$message.error('请输入每人兑换次数')
        return false
      }
      if (this.ruleForm.smartCoinsPrice <= 0) {
        this.$message.error('请输入智能币且必须大于0')
        return false
      }
      if (this.ruleForm.goodsType == 1 && this.ruleForm.memberDays <= 0) {
        this.$message.error('请输入会员天数且必须大于0')
        return false
      }
      if (this.ruleForm.goodsType == 2 && this.ruleForm.fullAmount <= 0) {
        this.$message.error('请输入优惠金额满【多少】元')
        return false
      }
      if (this.ruleForm.goodsType == 2 && this.ruleForm.discountAmount <= 0) {
        this.$message.error('请输入优惠金额优惠金额')
        return false
      }
      if (this.ruleForm.inventoryQuantity <= 0) {
        this.$message.error('请输入库存数量且必须大于0')
        return false
      }
      return true
    },
    handleRemove(file) {
      const list = this.ruleForm.showPics.filter(s => s.uuid != file.uuid)
      //删除
      this.ruleForm.showPics = list
      let showFiles = []
      this.ruleForm.showPics.forEach(file => {
        showFiles.push(file.url)
      })
      this.ruleForm.showPic = showFiles.join(';')
    },
    // 售卖类型变更
    saleTimeTypeChange() {
      if (this.ruleForm.saleTimeType == 1) {
        this.saleTimeTypeDisabled = true
        this.ruleForm.saleTimes = []
      } else {
        this.saleTimeTypeDisabled = false
      }
    },

    // 使用时间类型变更
    useTimeTypeChange() {
      if (this.ruleForm.useTimeType == 1) {
        this.useTimeTypeDisabled = true
        this.ruleForm.useTimes = []
      } else {
        this.useTimeTypeDisabled = false
      }
    },
    handleAvatarSuccess(res, file) {
      console.log(res.data)
      this.ruleForm.showPics.push({
        name: res.data.originalName,
        url: res.data.link
      })
      let list = []
      this.ruleForm.showPics.forEach(file => {
        list.push(file.url)
      })
      this.ruleForm.showPic = list.join(';')
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'application/pdf'
      // const isLt2M = file.size / 1024 / 1024 < 2
      if (!isJPG) {
        this.$message.error('上传文件只能是 pdf 格式!')
      }
      // if (!isLt2M) {
      //   this.$message.error('上传头像图片大小不能超过 2MB!')
      // }
      return isJPG
    },
    goList() {
      this.$router.push({
        path: '/smartCoinsManagement/exchange'
      })
    },
    handleBack() {
    }
  }
}
</script>
