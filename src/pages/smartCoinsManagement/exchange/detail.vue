<template>
  <simple-page title='查看兑换商品详情' @back='handleBack'>
    <simple-line-card header='基本信息'>
      <el-form  label-width="120px" class="demo-ruleForm" v-loading="loading">
       <el-form-item label="商品类型：">
             {{detail.goodsType == 1 ? '会员' : detail.goodsType == 2 ? '优惠券': '虚拟商品'}}
       </el-form-item>
       <el-form-item label="商品名称：" >
           {{detail.goodsName}}
        </el-form-item>

        <!-- <el-form-item label="展示图：" >
           <el-image v-for="" style="width: 100px; height: 100px" :src="detail.zsPric"></el-image>
           <div  class="block"  v-for="item in detail.showPics" :key="item">
              <el-image
                style="width: 100px; height: 100px"
                :src="item.url"
                :fit="fit"></el-image>
            </div>
        </el-form-item> -->
        <el-form-item label="展示图：" v-if="detail.showPics && detail.showPics.length > 0">
           <div  class="block"  >
              <el-image
                v-for="item in detail.showPics" :key="item"
                style="margin:5px;width: 100px; height: 100px"
                :src="item.url"
                :fit="fit"></el-image>
            </div>
        </el-form-item>

        <el-form-item label="智能币：" >
             {{detail.smartCoinsPrice}} 智能币
        </el-form-item>


        <el-form-item label="会员天数：" v-if="detail.goodsType == 1">
          {{detail.memberDays}} 天
        </el-form-item>


        <el-form-item label="优惠金额：" v-if="detail.goodsType == 2" >
            <el-col :span="12">
              满 {{detail.fullAmount}} 元，优惠 {{detail.discountAmount}} 元
            </el-col>
        </el-form-item>

        <el-form-item label="库存数量：" >
          {{detail.inventoryQuantity}}
        </el-form-item>
        <el-form-item label="库存预警：" >
          {{detail.inventoryWarningCount}}
        </el-form-item>
        <el-form-item label="商品简介：" >
          {{detail.goodsIntroduction}}
        </el-form-item>
        <el-form-item label="使用时间：" >
           {{detail.useTimeType == 1 ? '不限' : detail.useTime }}
        </el-form-item>
        <el-form-item label="售卖时间：">
          {{detail.saleTimeType == 1 ? '不限' : detail.saleTime }}
        </el-form-item>

        <!-- <el-form-item label="商品详情图：" v-if="detail.showPics && detail.showPics.length > 0">
           <div  class="block"  v-for="item in detail.showPics" :key="item">
              <el-image
                style="width: 100px; height: 100px"
                :src="item.url"
                :fit="fit"></el-image>
            </div>
        </el-form-item> -->
       <el-form-item label="每人限购次数：" >
           {{detail.everyOneCapType == 1 ? '不限' : detail.everyOneCap }}
       </el-form-item>

<!--       <el-form-item label="排序：">-->
<!--             {{detail.orderNumType == 1 ? '置顶' : detail.orderNum }}-->
<!--       </el-form-item>-->
       <el-form-item label="状态：" >
          {{detail.goodsStatus == 1 ? '上架中' : '待上架' }}
       </el-form-item>
      </el-form>
    </simple-line-card>
  </simple-page>
</template>

<script>
import {pageList ,add ,detail,exchangeDelete,update,changeStatus,down } from '@/api/modules/exchange.js'

export default {
  data() {
    return {
      id: '',
      loading: false,
      detail: {}
    }
  },
  mounted() {
    this.id = this.$route.query.id
    this.loadDetail()
  },
  methods: {
    loadDetail() {
      this.loading  = true
      detail({id: this.id}).then(res => {
        this.detail = res.data
        this.loading  = false
        const list = this.detail.showPic.split(';')
        this.detail.showPics = [];
        this.detail.zsPric = list[0]
        for (var i =0;i<list.length ; i++) {
          this.detail.showPics.push({
            name: 'file',
            url:list[i]
          })
        }
      }).catch(() => {
        this.loading = false
      })
    },
    handleBack() {
    }
  }
}
</script>

<style lang="scss" scoped></style>
