<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='商品名称：'>
          <el-input v-model='form.goodsName'></el-input>
        </el-form-item>
        <el-form-item label='状态：'>
          <el-select v-model='form.goodsStatus' placeholder='请选择'>
            <el-option
              v-for='item in status'
              :key='item.value'
              :label='item.label'
              :value='item.value'>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div slot='bottom-options'>
      <el-button type='primary' size='mini' icon='el-icon-plus' @click='addView'>新增</el-button>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
      <div slot='goodsStatus' slot-scope='scope'>
        {{ scope.row.goodsStatus == 1 ? '上架中' : '待上架' }}
      </div>
      <div slot='quantityStatusLabel' slot-scope='scope'>
        <label class='green' v-if='scope.row.isAmple'>库存充足</label>
        <label class='red' v-else>{{ scope.row.quantityStatusLabel }}</label>
      </div>
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' icon='el-icon-view' v-if='scope.row.goodsStatus == 1'
                   @click='details(scope.row)'>查看
        </el-button>
        <el-button type='text' size='mini' icon='el-icon-delete' v-if='scope.row.goodsStatus == 1'
                   @click='down(scope.row)'>下架
        </el-button>
        <el-button type='text' size='mini' icon='el-icon-edit' v-if='scope.row.goodsStatus == 2'
                   @click='editView(scope.row)'>编辑
        </el-button>
        <el-button type='text' size='mini' icon='el-icon-delete' v-if='scope.row.goodsStatus == 2'
                   @click='delte(scope.row)'>删除
        </el-button>
      </div>
    </simple-table>
  </simple-list-page>
</template>

<script>
import {pageList, add, detail, exchangeDelete, update, changeStatus, down} from '@/api/modules/exchange.js'

export default {
  data() {
    return {
      loading: false,
      form: {
        goodsName: '',
        goodsStatus: ''
      },
      status: [
        {
          value: '',
          label: '全部'

        },
        {
          value: 1,
          label: '上架中'

        },
        {
          value: 2,
          label: '待上架'
        }
      ],
      columns: [
        {
          label: '商品名称',
          prop: 'goodsName'
        },
        {
          label: '智能币价格',
          prop: 'smartCoinsPrice'
        },
        {
          label: '库存数量',
          prop: 'inventoryQuantity'
        },
        // {
        //   label: '排序',
        //   prop: 'orderNumName'
        // },
        {
          label: '创建时间',
          prop: 'createTime'
        },
        {
          label: '状态',
          prop: 'goodsStatus',
          slot: 'goodsStatus'
        },
        {
          label: '库存状态',
          prop: 'quantityStatusLabel',
          slot: 'quantityStatusLabel'
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageList(data).then(res => {
        this.list = res.data.records
        this.list.map(item => item.isAmple = (item.quantityStatusLabel === '库存充足'))
        this.page.total = res.data.total
      })
    },
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    // 下架
    down(row) {
      this.$confirm('是否确定下架' + row.goodsName + '?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        down({id: row.id}).then(res => {
          if (+res.code === 200) {
            this.$message.success(res.msg)
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消下架'
        })
      })
    },
    // 删除
    delte(row) {
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        exchangeDelete({id: row.id}).then(res => {
          if (+res.code === 200) {
            this.$message.success(res.msg)
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    addView() {
      this.$router.push({
        path: '/smartCoinsManagement/exchange/add'
      })
    },
    details(row) {
      this.$router.push({
        path: '/smartCoinsManagement/exchange/detail',
        query: {
          id: row.id
        }
      })
    },
    // 编辑
    editView(row) {
      this.$router.push({
        path: '/smartCoinsManagement/exchange/edit',
        query: {
          id: row.id
        }
      })
    },
    handleClose(done) {
      done()
    }
  }
}
</script>

<style lang='scss' scoped></style>
