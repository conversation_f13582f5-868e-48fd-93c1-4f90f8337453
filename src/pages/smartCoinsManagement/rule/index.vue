<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='规则名称：'>
          <el-input v-model='form.taskName'></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot='bottom-options'>
      <el-button type='primary' size='mini' icon='el-icon-plus' @click='addView'>新增</el-button>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
      <div slot='taskStatus' slot-scope='scope'>
        <el-switch v-model='scope.row.taskStatus' @change='taskStatus(scope.row)' active-color='#13ce66' inactive-color='#ff4949' :active-value='1' :inactive-value='2' active-text='开' inactive-text='关'>
        </el-switch>
      </div>

      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' icon='el-icon-edit' @click='editView(scope.row)'>编辑</el-button>
        <el-button type='text' size='mini' icon='el-icon-delete' @click='delte(scope.row)'>删除</el-button>
      </div>
    </simple-table>
    <el-dialog :title='title' :visible.sync='dialogVisible' width='70%' :before-close='handleClose'>
      <el-form :model='ruleForm' :rules='rules' ref='ruleForm' label-width='130px' class='demo-ruleForm'>
        <el-form-item label='规则名称：' prop='taskName'>
          <el-input v-model='ruleForm.taskName' maxlength='50' show-word-limit></el-input>
        </el-form-item>
        <el-form-item label='任务类型：'>
          <el-row>
            <el-col :span='6'>
              <el-select v-model='ruleForm.taskTypeLeft' @change='taskTypeChange' placeholder='请选择任务类型'>
                <el-option v-for='item in taskTypeLefts' :key='item.value' :label='item.label' :value='item.value'>
                </el-option>
              </el-select>
            </el-col>
            <el-col :span='4'>
              <el-select v-model='ruleForm.taskTypeRight' placeholder='请选择' @change='taskTypeRightChange'>
                <el-option v-for='item in taskTypeRights' :key='item.value' :label='item.label' :value='item.value'>
                </el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label='智能币规则：'>
          <el-row v-if='ruleForm.taskTypeRight == 1'>
            <el-col :span='12'>
              <template>
                <el-radio-group v-model='ruleForm.isSameSmartCoins' @change='sameSmartCoinsChange'>
                  <el-radio :label='1'>每日签到智能币相同</el-radio>
                  <el-radio :label='2'>每日签到智能币不同（连续签到）</el-radio>
                </el-radio-group>
              </template>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.taskTypeRight == 8'>
            <el-col :span='12'>
              <template>
                <el-radio-group v-model='ruleForm.samplingSheetExchangeType' style='line-height: 30px;margin-top: 10px'>
                  <el-radio :label='0'>同一抽样单被购买一次给上传用户一次智能币</el-radio>
                  <el-radio :label='1'>同一抽样单被购买多次仅给上传用户发放一次智能币</el-radio>
                </el-radio-group>
              </template>
            </el-col>
            <el-col :span='24'>
              <span style='margin-right: 20px'>{{ typeTitle }}</span>
              <el-input-number v-model='ruleForm.samplingSheetRewardSmartCoins' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.taskTypeRight == 1 && ruleForm.isSameSmartCoins == 1'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>{{ typeTitle }}</span>
              <el-input-number v-model='ruleForm.dailySmartCoins' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>

          <el-row v-if='ruleForm.taskTypeRight == 2'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>{{ typeTitle }}</span>
              <el-input-number v-model='ruleForm.inviteRewardSmartCoins' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>

          <el-row v-if='ruleForm.taskTypeRight == 3'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>{{ typeTitle }}</span>
              <el-input-number v-model='ruleForm.loginRewardSmartCoins' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.taskTypeRight == 4'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>{{ typeTitle }}</span>
              <el-input-number v-model='ruleForm.registerRewardSmartCoins' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.taskTypeRight == 5'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>{{ typeTitle }}</span>
              <el-input-number v-model='ruleForm.completePersonRewardSmartCoins' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.taskTypeRight == 6'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>{{ typeTitle }}</span>
              <el-input-number v-model='ruleForm.completeAccountRewardSmartCoins' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.taskTypeRight == 7'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>{{ typeTitle }}</span>
              <el-input-number v-model='ruleForm.samplingSheetRewardSmartCoins' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>

          <el-row v-if='ruleForm.isSameSmartCoins == 2'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>签到第1日，可获得</span>
              <el-input-number v-model='ruleForm.dailySmartCoins1' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.isSameSmartCoins == 2'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>签到第2日，可获得</span>
              <el-input-number v-model='ruleForm.dailySmartCoins2' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.isSameSmartCoins == 2'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>签到第3日，可获得</span>
              <el-input-number v-model='ruleForm.dailySmartCoins3' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.isSameSmartCoins == 2'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>签到第4日，可获得</span>
              <el-input-number v-model='ruleForm.dailySmartCoins4' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.isSameSmartCoins == 2'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>签到第5日，可获得</span>
              <el-input-number v-model='ruleForm.dailySmartCoins5' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.isSameSmartCoins == 2'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>签到第6日，可获得</span>
              <el-input-number v-model='ruleForm.dailySmartCoins6' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.isSameSmartCoins == 2'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>签到第7日，可获得</span>
              <el-input-number v-model='ruleForm.dailySmartCoins7' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.isSameSmartCoins == 2'>
            <el-col :span='12'>
              <span style='margin-right: 20px'>后续每日，可获得</span>
              <el-input-number v-model='ruleForm.dailySmartCoinsAfter' size='mini' :min='1' :max='********'></el-input-number>
              <span style='margin-left: 20px'>智能币</span>
            </el-col>
          </el-row>
          <el-row v-if='ruleForm.taskTypeRight == 1'>
            <el-checkbox v-model='ruleForm.checkedExtraReward'></el-checkbox>
            <span style='margin-right: 20px'>每连续签满</span>
            <el-input-number v-model='ruleForm.everyContinueSignFullDays' :disabled='!ruleForm.checkedExtraReward' size='mini' :min='1' :max='********' style='margin-right: 20px' />
            <span style='margin-right: 20px'> 天，额外奖励</span>
            <el-input-number v-model='ruleForm.extraReward' size='mini' :disabled='!ruleForm.checkedExtraReward' :min='1' :max='********' style='margin-right: 20px' />
            <span style='margin-right: 20px'>智能币，会员</span>
            <el-input-number v-model='ruleForm.extraRewardMemberDays' size='mini' :disabled='!ruleForm.checkedExtraReward' :min='1' :max='********' style='margin-right: 20px' />
            天
          </el-row>
        </el-form-item>
        <el-form-item label='每日上限：' v-if='ruleForm.taskTypeRight == 1||ruleForm.taskTypeRight==7'>
          <el-col :span='12'>
            <template>
              <el-radio-group v-model="ruleForm.dailyCapType">
                <el-radio :label='1'>仅一次</el-radio>
                <el-radio v-if="ruleForm.taskTypeRight==7" :label='2'>不限</el-radio>
                <el-radio v-if="ruleForm.taskTypeRight==7" :label='3'>限制次数</el-radio>
                <el-input-number v-if="ruleForm.taskTypeRight==7" :disabled="ruleForm.dailyCapType!=3" v-model='ruleForm.dailyCap' size='mini' :min='1' :max='********'></el-input-number>
              </el-radio-group>
            </template>
          </el-col>
        </el-form-item>
        <el-form-item label='任务有效期：'>
          <el-row>
            <el-col :span='4'>
              <template>
                <el-radio-group v-model='ruleForm.taskPeriodType' @change='taskPeriodTypeChange'>
                  <el-radio :label='1'>不限</el-radio>
                  <el-radio :label='2'>自定义</el-radio>
                </el-radio-group>
              </template>
            </el-col>
            <el-col :span='12'>
              <template>
                <el-date-picker :disabled='taskPeriodTypeDisabled' v-model='ruleForm.taskPeriods' format='yyyy-MM-dd' value-format='yyyy-MM-dd' type='daterange' align='right' unlink-panels range-separator='至'
                  start-placeholder='开始日期' end-placeholder='结束日期'>
                </el-date-picker>
              </template>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label='智能币清零规则：'>
          <el-row>
            <el-col>
              <el-radio v-model='ruleForm.clearRuleType' :label='1'>不清零</el-radio>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-radio v-model='ruleForm.clearRuleType' :label='2'>自然年清零上一年获得智能币 （每年12月31日 23:59分清空上一年获得的智能币）
              </el-radio>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-radio v-model='ruleForm.clearRuleType' :label='3'>自然年清零所有智能币 （每年12月31日 23:59分清空获得的全部智能币）</el-radio>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <span slot='footer' class='dialog-footer'>
        <el-button @click='dialogVisible = false'>取 消</el-button>
        <el-button type='primary' @click='submit' v-loading='loading'>确 定</el-button>
      </span>
    </el-dialog>
  </simple-list-page>
</template>

<script>
import { add, changeStatus, detail, pageList, ruleDelete, update } from '@/api/modules/rule.js'

import { getToken } from '@/utils/auth'

export default {
  data() {
    return {
      typeTitle: '每日签到，可获得',
      title: '新增智能币规则',
      loading: false,
      dialogVisible: false,
      taskPeriodTypeDisabled: true,
      form: {},
      uploadUrl: process.env.VUE_APP_BASE_API + '/oss/endpoint/put-public-file',
      headers: {
        Authorization: getToken()
      },
      taskTypeLefts: [{
        value: 1,
        label: '日常任务'
      }, {
        value: 2,
        label: '新手任务'
      }],
      taskTypeRights: [{
        value: 1,
        label: '签到'
      }
        // ,{
        //   value: 2,
        //   label: '邀请/分享成功'
        // }
        , {
        value: 3,
        label: '登录'
      }],
      taskType1Rights: [
        {
          value: 1,
          label: '签到'
        },
        // {
        //   value: 2,
        //   label: '邀请/分享成功'
        // },
        {
          value: 3,
          label: '登录'
        }, {
          value: 7,
          label: '上传抽样单'
        }, {
          value: 8,
          label: '抽样单被兑换'
        }
      ],
      taskType2Rights: [{
        value: 4,
        label: '注册（新手任务）'
      }, {
        value: 5,
        label: '完善个人信息（新手任务）'
      }, {
        value: 6,
        label: '完善账号信息（新手任务）'
      }],
      fileList: [],
      ruleForm: {
        taskName: '',
        taskTypeLeft: 1,
        taskTypeRight: 1,
        isSameSmartCoins: 1,
        dailySmartCoins: undefined,
        everyContinueSignFullDays: undefined,
        extraReward: undefined,
        extraRewardMemberDays: undefined,
        taskPeriods: [],
        dailyCap: 1,
        dailyCapType: 1,
        isExtraReward: 2,
        checkedExtraReward: undefined,
        taskPeriodType: 1,
        clearRuleType: 1,
        dailySmartCoins1: undefined,
        dailySmartCoins2: undefined,
        dailySmartCoins3: undefined,
        dailySmartCoins4: undefined,
        dailySmartCoins5: undefined,
        dailySmartCoins6: undefined,
        dailySmartCoins7: undefined,
        loginRewardSmartCoins: undefined,
        inviteRewardSmartCoins: undefined,
        registerRewardSmartCoins: undefined,
        completePersonRewardSmartCoins: undefined,
        completeAccountRewardSmartCoins: undefined,
        samplingSheetRewardSmartCoins: null,
        samplingSheetExchangeType: 0
      },
      rules: {
        taskName: [
          { required: true, message: '请输入规则名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        taskTypeLeft: [
          { required: true, message: '请选择任务类型', trigger: 'change' }
        ],
        taskTypeRight: [
          { required: true, message: '请选择任务类型', trigger: 'change' }
        ]
      },
      columns: [
        {
          label: '规则名称',
          prop: 'taskName'
        },
        {
          label: '获得智能币数',
          prop: 'rewardSmartCoins'
        },
        {
          label: '每日上限/人',
          prop: 'dailyCapName'
        },
        {
          label: '任务有效期',
          prop: 'taskPeriodName'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        },
        {
          label: '状态',
          prop: 'taskStatus',
          slot: 'taskStatus'
        }
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    taskTypeRightChange() {
      // 切换类型清空每日签到，可获得 数量
      this.ruleForm.dailySmartCoins = undefined
      this.ruleForm.inviteRewardSmartCoins = undefined
      this.ruleForm.loginRewardSmartCoins = undefined
      this.ruleForm.registerRewardSmartCoins = undefined
      this.ruleForm.completePersonRewardSmartCoins = undefined
      this.ruleForm.completeAccountRewardSmartCoins = undefined
      this.ruleForm.checkedExtraReward = undefined
      this.ruleForm.dailyCapType = 1
      this.ruleForm.dailyCap = undefined
      this.ruleForm.samplingSheetExchangeType = 0
      this.ruleForm.samplingSheetRewardSmartCoins = undefined
      // 切换类型的时候 智能币规则 改变为1
      this.ruleForm.isSameSmartCoins = 1
      this.getTitle()

    },
    getTitle() {
      switch (this.ruleForm.taskTypeRight) {
        case 1:
          this.typeTitle = '每日签到，可获得'
          break
        case 2:
          this.typeTitle = '每邀请/分享1名好友注册成功，奖励'
          break
        case 3:
          this.typeTitle = '每日首次登录/访问成功，奖励'
          break
        case 4:
          this.typeTitle = '注册成功，奖励'
          break
        case 5:
          this.typeTitle = '完善个人信息，可获得'
          break
        case 6:
          this.typeTitle = '完善账户信息，可获得'
          break
        case 7:
          this.typeTitle = '每审核通过一张抽检单，奖励'
          break
        case 8:
          this.typeTitle = '每兑换成功一张抽检单，奖励'
          break
        default:
          break
      }
    },
    // 更改某一条的状态
    taskStatus(row) {
      changeStatus({ id: row.id, taskStatus: row.taskStatus }).then(res => {
        if (+res.code === 200) {
          this.$message.success('更新' + row.taskName + '状态成功')
        } else {
          this.$message.error('更新' + row.taskName + '状态失败')
        }
      }).catch((err) => {
        row.taskStatus = !row.taskStatus
        // this.$message.error(err)
      }).finally(() => {
        this.loadData()
      })
    },
    // 切换智能规则类型的时候 清空相应的数量
    sameSmartCoinsChange() {
      if (this.ruleForm.taskTypeRight == 1)
        if (this.ruleForm.isSameSmartCoins == 1) {
          this.ruleForm.dailySmartCoins1 = undefined
          this.ruleForm.dailySmartCoins2 = undefined
          this.ruleForm.dailySmartCoins3 = undefined
          this.ruleForm.dailySmartCoins4 = undefined
          this.ruleForm.dailySmartCoins5 = undefined
          this.ruleForm.dailySmartCoins6 = undefined
          this.ruleForm.dailySmartCoins7 = undefined
        } else {
          this.ruleForm.dailySmartCoins = undefined
        }
    },
    taskTypeChange() {
      if (this.ruleForm.taskTypeLeft == 1) {
        this.taskTypeRights = this.taskType1Rights
        this.ruleForm.taskTypeRight = 1
      } else {
        this.taskTypeRights = this.taskType2Rights
        this.ruleForm.taskTypeRight = 4
      }
      this.taskTypeRightChange()
    },
    // 任务有效期 改变状态
    taskPeriodTypeChange() {
      if (this.ruleForm.taskPeriodType == 1) {
        this.taskPeriodTypeDisabled = true
        // this.ruleForm.taskPeriods = []
        this.$set(this.ruleForm, 'taskPeriods', [])

      } else {
        this.taskPeriodTypeDisabled = false
      }
    },
    loadData() {
      let data = {
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleQuery() {
      this.page = {
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    // 提交
    submit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          // 验证表单
          let isVaild = this.validForm()
          if (!isVaild) {
            this.loading = false
            return
          }
          if (this.ruleForm.taskPeriods && this.ruleForm.taskPeriods.length > 0) {
            this.ruleForm.taskPeriod = this.ruleForm.taskPeriods[0] + '~' + this.ruleForm.taskPeriods[1]
          }
          if (this.ruleForm.checkedExtraReward == 1) {
            this.ruleForm.isExtraReward = 1
          } else {
            this.ruleForm.isExtraReward = 2
          }
          this.loading = true
          let request
          if (this.ruleForm.id) {
            request = update(this.ruleForm)
          } else {
            request = add(this.ruleForm)
          }
          request.then(res => {
            this.loading = false
            if (+res.code === 200) {
              this.$message.success(res.msg)
              this.dialogVisible = false
            } else {
              this.$message.error(res.msg)
            }
            this.handleQuery()
          }).catch((err) => {
            this.loading = false
          })
        } else {
          this.loading = false
          // 登录表单校验失败
          this.$message.error('表单校验失败，请检查')
        }
      })


    },
    validForm() {
      // 如果类型为签到类型，并且智能币规则每日签到类型，判断每日签到，可获得
      if (this.ruleForm.taskTypeRight == 1 && this.ruleForm.isSameSmartCoins == 1) {
        if (!this.ruleForm.dailySmartCoins || this.ruleForm.dailySmartCoins && this.ruleForm.dailySmartCoins == 0) {
          this.$message.error(this.typeTitle + '不能为空和0')
          return false
        }
      }
      if (this.ruleForm.taskTypeRight == 2) {
        if (!this.ruleForm.inviteRewardSmartCoins || this.ruleForm.inviteRewardSmartCoins && this.ruleForm.inviteRewardSmartCoins == 0) {
          this.$message.error(this.typeTitle + '不能为空和0')
          return false
        }
      }
      if (this.ruleForm.taskTypeRight == 3) {
        if (!this.ruleForm.loginRewardSmartCoins || this.ruleForm.loginRewardSmartCoins && this.ruleForm.loginRewardSmartCoins == 0) {
          this.$message.error(this.typeTitle + '不能为空和0')
          return false
        }
      }
      if (this.ruleForm.taskTypeRight == 4) {

        if (!this.ruleForm.registerRewardSmartCoins || this.ruleForm.registerRewardSmartCoins && this.ruleForm.registerRewardSmartCoins == 0) {
          this.$message.error(this.typeTitle + '不能为空和0')
          return false
        }
      }
      if (this.ruleForm.taskTypeRight == 5) {
        if (!this.ruleForm.completePersonRewardSmartCoins || this.ruleForm.completePersonRewardSmartCoins && this.ruleForm.completePersonRewardSmartCoins == 0) {
          this.$message.error(this.typeTitle + '不能为空和0')
          return false
        }
      }

      if (this.ruleForm.taskTypeRight == 6) {
        if (!this.ruleForm.completeAccountRewardSmartCoins || this.ruleForm.completeAccountRewardSmartCoins && this.ruleForm.completeAccountRewardSmartCoins == 0) {
          this.$message.error(this.typeTitle + '不能为空和0')
          return false
        }
      }
      if (this.ruleForm.taskTypeRight == 7) {
        if (!this.ruleForm.samplingSheetRewardSmartCoins || this.ruleForm.samplingSheetRewardSmartCoins && this.ruleForm.samplingSheetRewardSmartCoins == 0) {
          this.$message.error(this.typeTitle + '不能为空和0')
          return false
        }
        if (this.ruleForm.dailyCapType == 3) {
          if (!this.ruleForm.dailyCap || this.ruleForm.dailyCap && this.ruleForm.dailyCap == 0) {
            this.$message.error('每日上限不能为空和0')
            return false
          }
        }
      }

      if (this.ruleForm.taskTypeRight == 8) {
        if (!this.ruleForm.samplingSheetRewardSmartCoins || this.ruleForm.samplingSheetRewardSmartCoins && this.ruleForm.samplingSheetRewardSmartCoins == 0) {
          this.$message.error(this.typeTitle + '不能为空和0')
          return false
        }
      }


      if (this.ruleForm.taskTypeRight == 1 && this.ruleForm.checkedExtraReward) {
        if (!this.ruleForm.everyContinueSignFullDays || this.ruleForm.everyContinueSignFullDays && this.ruleForm.everyContinueSignFullDays == 0) {
          this.$message.error('每连续签满天数不能为空和0')
          return false
        }
        if (!this.ruleForm.extraReward || this.ruleForm.extraReward && this.ruleForm.extraReward == 0) {
          this.$message.error('每连续签满额外奖励智能币不能为空和0')
          return false
        }
        if (!this.ruleForm.extraRewardMemberDays || this.ruleForm.extraRewardMemberDays && this.ruleForm.extraRewardMemberDays == 0) {
          this.$message.error('每连续签满会员天数不能为空和0')
          return false
        }

      }

      // 如果类型为签到类型，并且智能币规则每日签到类型，判断连续签到，判断1-7
      if (this.ruleForm.taskTypeRight == 1 && this.ruleForm.isSameSmartCoins == 2) {
        if (!this.ruleForm.dailySmartCoins1 || this.ruleForm.dailySmartCoins1 && this.ruleForm.dailySmartCoins1 == 0) {
          this.$message.error('签到第1日，可获得不能为空和0')
          return false
        }
        if (!this.ruleForm.dailySmartCoins2 || this.ruleForm.dailySmartCoins2 && this.ruleForm.dailySmartCoins2 == 0) {
          this.$message.error('签到第2日，可获得不能为空和0')
          return false
        }
        if (!this.ruleForm.dailySmartCoins3 || this.ruleForm.dailySmartCoins3 && this.ruleForm.dailySmartCoins3 == 0) {
          this.$message.error('签到第3日，可获得不能为空和0')
          return false
        }
        if (!this.ruleForm.dailySmartCoins4 || this.ruleForm.dailySmartCoins4 && this.ruleForm.dailySmartCoins4 == 0) {
          this.$message.error('签到第4日，可获得不能为空和0')
          return false
        }
        if (!this.ruleForm.dailySmartCoins5 || this.ruleForm.dailySmartCoins5 && this.ruleForm.dailySmartCoins5 == 0) {
          this.$message.error('签到第5日，可获得不能为空和0')
          return false
        }
        if (!this.ruleForm.dailySmartCoins6 || this.ruleForm.dailySmartCoins6 && this.ruleForm.dailySmartCoins6 == 0) {
          this.$message.error('签到第6日，可获得不能为空和0')
          return false
        }
        if (!this.ruleForm.dailySmartCoins7 || this.ruleForm.dailySmartCoins7 && this.ruleForm.dailySmartCoins7 == 0) {
          this.$message.error('签到第7日，可获得不能为空和0')
          return false
        }
      }

      if (this.ruleForm.taskPeriodType == 2 && (!this.ruleForm.taskPeriods ||
        this.ruleForm.taskPeriods && this.ruleForm.taskPeriods.length <= 0)) {
        this.$message.error('请选择任务有效期')
        return false
      }

      return true
    },
    // 删除
    delte(row) {
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ruleDelete({ id: row.id }).then(res => {
          if (+res.code === 200) {
            this.$message.success(res.msg)
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    addView() {
      this.title = '新增智能币规则'
      this.dialogVisible = true
      this.taskTypeRights = this.taskType1Rights
      this.ruleForm = {
        taskName: '',
        taskTypeLeft: 1,
        taskTypeRight: 1,
        isSameSmartCoins: 1,
        dailySmartCoins: undefined,
        everyContinueSignFullDays: undefined,
        extraReward: undefined,
        extraRewardMemberDays: undefined,
        taskPeriods: [],
        dailyCap: undefined,
        dailyCapType: 1,
        isExtraReward: 2,
        checkedExtraReward: false,
        taskPeriodType: 1,
        clearRuleType: 1,
        dailySmartCoins1: undefined,
        dailySmartCoins2: undefined,
        dailySmartCoins3: undefined,
        dailySmartCoins4: undefined,
        dailySmartCoins5: undefined,
        dailySmartCoins6: undefined,
        dailySmartCoins7: undefined,
        loginRewardSmartCoins: undefined,
        inviteRewardSmartCoins: undefined,
        registerRewardSmartCoins: undefined,
        completePersonRewardSmartCoins: undefined,
        completeAccountRewardSmartCoins: undefined
      }
    },
    // 编辑
    editView(row) {
      this.title = '编辑智能币规则'
      // 获取详情数据
      detail({ id: row.id }).then(res => {
        this.loading = false
        if (+res.code === 200) {
          this.dialogVisible = true
          this.ruleForm = res.data
          // 组装日期
          if (this.ruleForm.taskPeriodType == 2 && this.ruleForm.taskPeriod) {
            const taskPeriodSplit = this.ruleForm.taskPeriod.split('~')
            this.ruleForm.taskPeriods = taskPeriodSplit
          }

          this.$set(this.ruleForm, 'checkedExtraReward', this.ruleForm.isExtraReward == 1 ? true : false)
          // if (this.ruleForm.isExtraReward == 1) {
          //   this.ruleForm.checkedExtraReward = true
          // } else {
          //   this.ruleForm.checkedExtraReward = false
          // }
          if (this.ruleForm.taskPeriodType == 1) {
            this.taskPeriodTypeDisabled = true
            // this.ruleForm.taskPeriods = []
            this.$set(this.ruleForm, 'taskPeriods', [])
          } else {
            this.taskPeriodTypeDisabled = false
          }
          if (this.ruleForm.taskTypeLeft == 1) {
            this.taskTypeRights = this.taskType1Rights
          } else {
            this.taskTypeRights = this.taskType2Rights
          }
          this.getTitle()
        } else {
          this.$message.error(res.msg)
        }
      }).catch(() => {
        this.loading = false
      })
    },
    handleClose(done) {
      done()
    }
  }
}
</script>

<style lang='scss' scoped></style>
