<template>
  <simple-list-page @query='handleQuery' @reset='handleReset'>
    <div slot='search'>
      <el-form :inline='true' :model='form'>
        <el-form-item label='商品名称：'>
          <el-input v-model='form.goodsName'></el-input>
        </el-form-item>
        <el-form-item label='兑换用户名称：'>
          <el-input v-model='form.realName'></el-input>
        </el-form-item>
        <el-form-item label='联系方式：'>
          <el-input v-model='form.phone'></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot='bottom-options'>
    </div>
    <simple-table :columns='columns' :data='list' :page='page' @load='loadData'>
      <div slot='orderStatus' slot-scope="scope">
        {{scope.row.orderStatus == 1 ? '兑换成功' : '兑换失败'}}
      </div>
       <div slot='goodsType' slot-scope="scope">
        {{scope.row.goodsType == 1 ? '会员' : '优惠券'}}
      </div>
      <div slot='options' slot-scope='scope'>
        <el-button type='text' size='mini' icon='el-icon-view'  @click='details(scope.row)'>订单详情</el-button>
      </div>
    </simple-table>
  </simple-list-page>
</template>

<script>
import {pageList ,detail } from '@/api/modules/order.js'
export default {
  data() {
    return {
      loading: false,
      form: {
        goodsName: '',
        realName: '',
        phone: '',
      },
      columns: [
        {
          label: '订单编号',
          prop: 'orderNo'
        },
        {
          label: '商品名称',
          prop: 'goodsName'
        },
        {
          label: '商品类型',
          prop: 'goodsType',
          slot: 'goodsType'
        },
        {
          label: '智能币价格',
          prop: 'smartCoinsPrice'
        },
        {
          label: '兑换数量',
          prop: 'exchangeQuantity'
        },
        {
          label: '实付',
          prop: 'paidIn'
        },
        {
          label: '兑换用户名称',
          prop: 'realName'
        },
        {
          label: '联系方式',
          prop: 'phone'
        },
        {
          label: '账号',
          prop: 'accountNumber'
        },
        {
          label: '状态',
          prop: 'orderStatus',
          slot: 'orderStatus'
        },
        {
          label: '兑换时间',
          prop: 'exchangeTime'
        },
      ],
      list: [],
      page: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      let data={
        ...this.form,
        current: this.page.current,
        size: this.page.size
      }
      pageList(data).then(res => {
        this.list = res.data.records
        this.page.total = res.data.total
      })
    },
    handleQuery() {
      this.page={
        total: 0,
        current: 1,
        size: 10
      }
      this.loadData()
    },
    handleReset() {
      this.form = {}
      this.handleQuery()
    },
    details(row) {
      this.$router.push({
        path: '/smartCoinsManagement/order/detail',
        query: {
          orderNo: row.orderNo
        }
      })
    },
    handleClose(done) {
      done();
    }
  }
}
</script>

<style lang="scss" scoped></style>
