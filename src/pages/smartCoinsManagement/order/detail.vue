<template>
  <simple-page title='查看兑换商品详情' @back='handleBack'>
    <simple-line-card header='查看详情'>
      <simple-detail-panel :columns='columns' :data='detail' :labelWidth='100'></simple-detail-panel>
    </simple-line-card>
    <simple-line-card header='兑换用户信息'>
      <simple-detail-panel :columns='dColumns' :data='detail' :labelWidth='100'></simple-detail-panel>
    </simple-line-card>
    <simple-line-card header='商品详情'>


      <el-form  label-width="120px" class="demo-ruleForm" v-loading="loading">
         <el-row>
            <el-col :span="12">
                <!-- <el-form-item label="展示图：" >
                  <el-image
                        style="width: 100px; height: 100px"
                        :src="detail.showPic"></el-image>
                </el-form-item> -->
                <el-form-item label="展示图：" v-if="detail.showPics && detail.showPics.length > 0">
                  <div  class="block"  >
                      <el-image
                        v-for="item in detail.showPics" :key="item"
                        style="margin:5px;width: 100px; height: 100px"
                        :src="item.url"
                        :fit="fit"></el-image>
                    </div>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="商品名称：" >
                    {{detail.goodsName}}
                  </el-form-item>
              <el-form-item label="智能币：" >
                    {{detail.smartCoinsPrice}} 智能币
                  </el-form-item>
              <el-form-item label="兑换会员天数：" >
                    {{detail.memberDays}} 天
                  </el-form-item>
              <el-form-item label="库存数量：" >
                    {{detail.inventoryQuantity}}
                  </el-form-item>
              <el-form-item label="商品简介：" >
                    {{detail.goodsIntroduction}}
                  </el-form-item>
                <el-form-item label="售卖时间：">
                  {{detail.saleTimeType == 1 ? '不限' : detail.saleTime }}
                </el-form-item>
              <el-form-item label="限购次数：" >
                  {{detail.everyOneCapType == 1 ? '不限' : detail.everyOneCap }}
              </el-form-item>
            </el-col>
          </el-row>
      </el-form>
    </simple-line-card>
  </simple-page>
</template>

<script>
import {detail } from '@/api/modules/order.js'

export default {
  data() {
    return {
      id: '',
      loading: false,
      columns: [
        {
          label: '订单编号',
          prop: 'orderNo'
        },
        {
          label: '商品名称',
          prop: 'goodsName'
        },
        {
          label: '智能币价格',
          prop: 'smartCoinsPrice'
        },
        {
          label: '实付',
          prop: 'paidIn'
        },
        {
          label: '兑换数量',
          prop: 'exchangeQuantity'
        },
        {
          label: '兑换时间',
          prop: 'exchangeTime'
        }
      ],
      dColumns: [
        {
          label: '用户姓名',
          prop: 'realName'
        },
        {
          label: '联系方式',
          prop: 'phone'
        },
        {
          label: '账号',
          prop: 'accountNumber'
        }
      ],
      detail: {}
    }
  },
  mounted() {
    this.orderNo = this.$route.query.orderNo
    this.loadDetail()
  },
  methods: {
    loadDetail() {
      this.loading  = true
      detail({orderNo: this.orderNo}).then(res => {
        this.detail = res.data
        this.loading  = false
        const list = this.detail.showPic.split(';')
        this.detail.showPics = [];
        this.detail.zsPric = list[0]
        for (var i =0;i<list.length ; i++) {
          this.detail.showPics.push({
            name: 'file',
            url:list[i]
          })
        }
      }).catch(() => {
        this.loading = false
      })
    },
    handleBack() {
    }
  }
}
</script>

<style lang="scss" scoped></style>
