/**
 * 中国省份配置数据
 * @description 包含中国所有省级行政区域数据
 */
export const provinces = [
  { label: '北京市', value: '北京' },
  { label: '天津市', value: '天津' },
  { label: '上海市', value: '上海' },
  { label: '重庆市', value: '重庆' },
  { label: '河北省', value: '河北' },
  { label: '山西省', value: '山西' },
  { label: '辽宁省', value: '辽宁' },
  { label: '吉林省', value: '吉林' },
  { label: '黑龙江省', value: '黑龙江' },
  { label: '江苏省', value: '江苏' },
  { label: '浙江省', value: '浙江' },
  { label: '安徽省', value: '安徽' },
  { label: '福建省', value: '福建' },
  { label: '江西省', value: '江西' },
  { label: '山东省', value: '山东' },
  { label: '河南省', value: '河南' },
  { label: '湖北省', value: '湖北' },
  { label: '湖南省', value: '湖南' },
  { label: '广东省', value: '广东' },
  { label: '海南省', value: '海南' },
  { label: '四川省', value: '四川' },
  { label: '贵州省', value: '贵州' },
  { label: '云南省', value: '云南' },
  { label: '陕西省', value: '陕西' },
  { label: '甘肃省', value: '甘肃' },
  { label: '青海省', value: '青海' },
  { label: '台湾省', value: '台湾' },
  { label: '内蒙古自治区', value: '内蒙古' },
  { label: '广西壮族自治区', value: '广西' },
  { label: '西藏自治区', value: '西藏' },
  { label: '宁夏回族自治区', value: '宁夏' },
  { label: '新疆维吾尔自治区', value: '新疆' },
  { label: '香港特别行政区', value: '香港' },
  { label: '澳门特别行政区', value: '澳门' }
];

/**
 * 检查输入的字符串是否为有效的省份
 * @param {string} province - 需要检查的省份字符串
 * @returns {boolean} - 如果是有效的省份则返回true，否则返回false
 */
export function isValidProvince(province) {
  if (!province) return true;
  
  const provinceValues = provinces.map(p => p.value);
  const provinceLabels = provinces.map(p => p.label);
  
  return provinceValues.includes(province) || provinceLabels.includes(province);
}

export default provinces;