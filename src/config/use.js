import { parseTime, formatDate } from '@/utils'
import '@/style/element-variables.scss'
import ElementUI from 'element-ui';
import Components from '@/components'
import $config from '@/config'
import '@/style/index.scss'
import '@/assets/icons'; // icon

export default function(Vue){
  Vue.prototype.parseTime = parseTime
  Vue.prototype.formatDate = formatDate
  Vue.use(ElementUI)
  Vue.use(Components) // 全局注册自定义组件
  Vue.use($config) // 注册全局配置文件
}
