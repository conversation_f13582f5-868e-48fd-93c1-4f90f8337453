/**
 * @des: 全局配置文件
 * @author: xve
 * @time: 2022-04-06 05:59:43
 */

// 获取环境变量，并处理可能的引号和空格问题
const getEnvVar = (key, defaultValue) => {
  const value = process.env[key];
  if (!value) return defaultValue;

  // 移除可能存在的引号、空格和注释
  // 注释通常以 # 开始，我们需要提取 # 之前的内容
  let cleanValue = value.replace(/["']/g, "").trim();

  // 如果有#号，只取#号之前的部分（处理同行注释）
  const hashIndex = cleanValue.indexOf("#");
  if (hashIndex !== -1) {
    cleanValue = cleanValue.substring(0, hashIndex).trim();
  }

  return cleanValue;
};

// 确保URL格式正确
const formatUrl = (url) => {
  if (!url) return "";

  // 清理URL，确保没有空格或其他非法字符
  let cleanUrl = url.trim();

  // 确保URL以http://或https://开头
  if (!cleanUrl.startsWith("http://") && !cleanUrl.startsWith("https://")) {
    cleanUrl = `http://${cleanUrl}`;
  }

  try {
    // 检查URL是否有效
    new URL(cleanUrl);
    return cleanUrl;
  } catch (e) {
    console.error("无效的URL:", cleanUrl, e);
    return "";
  }
};

// 根据当前环境确定API基础URL
const API_BASE_URL = (() => {
  // 优先从环境变量中读取API基础URL
  const envApiUrl = getEnvVar("VUE_APP_BASE_API", "");
  if (envApiUrl) {
    const formattedUrl = formatUrl(envApiUrl);
    console.log("使用环境变量中的API基础URL:", formattedUrl);
    return formattedUrl;
  }

  // 环境变量不存在时使用备用逻辑
  // 开发环境
  if (process.env.NODE_ENV === "development") {
    return "http://192.168.110.185:9998"; // 默认开发环境API
  }

  // 测试环境
  if (process.env.MODE === "test") {
    return "http://172.18.142.38:9998"; // 测试环境API
  }

  // 生产环境
  if (process.env.MODE === "prod" || process.env.NODE_ENV === "production") {
    return "https://cloud.cyznzs.com"; // 生产环境API
  }

  // 默认返回开发环境API
  return "http://192.168.110.185:9998";
})();

// API URL工厂函数
export const getApiUrl = (path = "") => {
  if (!path) return API_BASE_URL;
  if (path.startsWith("/")) {
    return `${API_BASE_URL}${path}`;
  }
  return `${API_BASE_URL}/${path}`;
};

// 登录API地址
export const LOGIN_API_URL = (() => {
  // 优先从环境变量中读取登录API URL
  const envLoginUrl = getEnvVar("VUE_APP_LOGIN_API", "");
  if (envLoginUrl) {
    const formattedUrl = formatUrl(envLoginUrl);
    console.log("使用环境变量中的登录API URL:", formattedUrl);
    return formattedUrl;
  }

  if (process.env.NODE_ENV === "development") {
    return `${API_BASE_URL}/api/auth/login`;
  }

  if (process.env.MODE === "test") {
    return `${API_BASE_URL}/api/auth/login`;
  }

  if (process.env.MODE === "prod" || process.env.NODE_ENV === "production") {
    return `${API_BASE_URL}/auth/login`;
  }

  return `${API_BASE_URL}/api/auth/login`;
})();

export const config = {
  appId: "wxb270d6933acd9d9d",
  baseURL: API_BASE_URL,
  uploadURL: getApiUrl("/common/uploadOSS"),
  loginUrl: LOGIN_API_URL,
};

export default function (Vue) {
  Vue.prototype.$config = config;
  // 将API配置全局暴露，方便直接访问
  Vue.prototype.$api = {
    baseURL: API_BASE_URL,
    loginUrl: LOGIN_API_URL,
    getApiUrl,
  };
}

// 高德地图API开发者Key
// export const AMAP_DEVELOPMENT_KEY = 'ea6837ce21ca589d08aa50cd0e7d34a4';
export const AMAP_DEVELOPMENT_KEY = "dd5061b54b6b701caac989f2d7080c5f";

// 高德天气API开发者Key
export const WEBSEVICE_KEY = "eb3ade84d6bd2076f6ad21dddc03b706";

//天气api的请求头
export const weatherApiHeader =
  "https://api.openweathermap.org/data/2.5/weather";

// 高德ip的请求头
export const gaoDeIpHeader = "https://restapi.amap.com/v3/ip";

// 高德Weather的请求头
export const gaoDeWeatherHeader =
  "https://restapi.amap.com/v3/weather/weatherInfo";
