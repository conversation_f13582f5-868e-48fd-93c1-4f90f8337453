/**
 * 中国省市区数据模块
 * @description 提供完整的中国省份、城市、区县级联数据，供各业务模块使用
 */
// 由于pcaa.js使用的是CommonJS的导出方式，需要使用require导入
const pcaa = require("./pcaa");

/**
 * 省市区完整数据
 * 数据格式：
 * [
 *   {
 *     value: '省份代码',
 *     label: '省份名称',
 *     children: [
 *       {
 *         value: '城市代码',
 *         label: '城市名称',
 *         children: [
 *           {
 *             value: '区县代码',
 *             label: '区县名称'
 *           }
 *         ]
 *       }
 *     ]
 *   }
 * ]
 */
export const regionData = [
  {
    value: "110000",
    label: "北京市",
    children: [
      {
        value: "110100",
        label: "北京市",
        children: [
          { value: "110101", label: "东城区" },
          { value: "110102", label: "西城区" },
          { value: "110105", label: "朝阳区" },
          { value: "110106", label: "丰台区" },
          { value: "110107", label: "石景山区" },
          { value: "110108", label: "海淀区" },
          { value: "110109", label: "门头沟区" },
          { value: "110111", label: "房山区" },
          { value: "110112", label: "通州区" },
          { value: "110113", label: "顺义区" },
          { value: "110114", label: "昌平区" },
          { value: "110115", label: "大兴区" },
          { value: "110116", label: "怀柔区" },
          { value: "110117", label: "平谷区" },
          { value: "110118", label: "密云区" },
          { value: "110119", label: "延庆区" },
        ],
      },
    ],
  },
  {
    value: "330000",
    label: "浙江省",
    children: [
      {
        value: "330100",
        label: "杭州市",
        children: [
          { value: "330102", label: "上城区" },
          { value: "330103", label: "下城区" },
          { value: "330104", label: "江干区" },
          { value: "330105", label: "拱墅区" },
          { value: "330106", label: "西湖区" },
          { value: "330108", label: "滨江区" },
          { value: "330109", label: "萧山区" },
          { value: "330110", label: "余杭区" },
          { value: "330111", label: "富阳区" },
          { value: "330112", label: "临安区" },
          { value: "330122", label: "桐庐县" },
          { value: "330127", label: "淳安县" },
          { value: "330182", label: "建德市" },
        ],
      },
      {
        value: "330200",
        label: "宁波市",
        children: [
          { value: "330203", label: "海曙区" },
          { value: "330205", label: "江北区" },
          { value: "330206", label: "北仑区" },
          { value: "330211", label: "镇海区" },
          { value: "330212", label: "鄞州区" },
          { value: "330213", label: "奉化区" },
          { value: "330225", label: "象山县" },
          { value: "330226", label: "宁海县" },
          { value: "330281", label: "余姚市" },
          { value: "330282", label: "慈溪市" },
        ],
      },
      {
        value: "330300",
        label: "温州市",
        children: [
          { value: "330302", label: "鹿城区" },
          { value: "330303", label: "龙湾区" },
          { value: "330304", label: "瓯海区" },
          { value: "330305", label: "洞头区" },
          { value: "330324", label: "永嘉县" },
          { value: "330326", label: "平阳县" },
          { value: "330327", label: "苍南县" },
          { value: "330328", label: "文成县" },
          { value: "330329", label: "泰顺县" },
          { value: "330381", label: "瑞安市" },
          { value: "330382", label: "乐清市" },
          { value: "330383", label: "龙港市" },
        ],
      },
      // 更多浙江省城市...
    ],
  },
  {
    value: "310000",
    label: "上海市",
    children: [
      {
        value: "310100",
        label: "上海市",
        children: [
          { value: "310101", label: "黄浦区" },
          { value: "310104", label: "徐汇区" },
          { value: "310105", label: "长宁区" },
          { value: "310106", label: "静安区" },
          { value: "310107", label: "普陀区" },
          { value: "310109", label: "虹口区" },
          { value: "310110", label: "杨浦区" },
          { value: "310112", label: "闵行区" },
          { value: "310113", label: "宝山区" },
          { value: "310114", label: "嘉定区" },
          { value: "310115", label: "浦东新区" },
          { value: "310116", label: "金山区" },
          { value: "310117", label: "松江区" },
          { value: "310118", label: "青浦区" },
          { value: "310120", label: "奉贤区" },
          { value: "310151", label: "崇明区" },
        ],
      },
    ],
  },
  // 可根据需要扩展更多省份数据...
];

/**
 * 获取省份列表
 * @returns {Array} 省份列表
 */
export function getProvinces() {
  const provinces = pcaa["86"];
  return Object.keys(provinces).map((code) => ({
    value: code,
    label: provinces[code],
  }));
}

/**
 * 根据省份获取城市列表
 * @param {string} provinceCode 省份代码
 * @returns {Array} 城市列表
 */
export function getCities(provinceCode) {
  // 判断省份代码是否存在
  if (!provinceCode || !pcaa[provinceCode]) {
    return [];
  }

  const cities = pcaa[provinceCode];
  return Object.keys(cities).map((code) => ({
    value: code,
    label: cities[code],
  }));
}

/**
 * 根据省份和城市获取区县列表
 * @param {string} provinceCode 省份代码
 * @param {string} cityCode 城市代码
 * @returns {Array} 区县列表
 */
export function getDistricts(provinceCode, cityCode) {
  // 判断城市代码是否存在
  if (!cityCode || !pcaa[cityCode]) {
    return [];
  }

  const districts = pcaa[cityCode];
  return Object.keys(districts).map((code) => ({
    value: code,
    label: districts[code],
  }));
}

/**
 * 根据代码获取省市区名称
 * @param {string} code 区域代码
 * @returns {string|null} 区域名称
 */
export function getRegionName(code) {
  if (!code) {
    return null;
  }

  // 省级(以0000结尾)
  if (/^\d{2}0000$/.test(code)) {
    return pcaa["86"][code] || null;
  }

  // 市级(以00结尾)
  if (/^\d{4}00$/.test(code)) {
    // 获取所属省份代码
    const provinceCode = code.substring(0, 2) + "0000";
    return pcaa[provinceCode] ? pcaa[provinceCode][code] || null : null;
  }

  // 区县级
  if (/^\d{6}$/.test(code)) {
    // 获取所属城市代码
    const cityCode = code.substring(0, 4) + "00";
    return pcaa[cityCode] ? pcaa[cityCode][code] || null : null;
  }

  return null;
}

/**
 * 获取完整的省市区数据(级联格式)
 * @returns {Array} 级联格式的省市区数据
 */
export function getRegionData() {
  const provinces = getProvinces();

  return provinces.map((province) => {
    const cities = getCities(province.value);

    return {
      ...province,
      children: cities.map((city) => {
        const districts = getDistricts(province.value, city.value);

        return {
          ...city,
          children: districts,
        };
      }),
    };
  });
}

/**
 * 通过地区名称查找地区代码
 * @param {string} name 地区名称
 * @returns {string|null} 地区代码
 */
export function getRegionCodeByName(name) {
  if (!name) return null;

  // 查找省级
  const provinces = pcaa["86"];
  for (const code in provinces) {
    if (provinces[code] === name) {
      return code;
    }
  }

  // 查找市级
  for (const provinceCode in pcaa) {
    if (provinceCode === "86") continue;

    const cities = pcaa[provinceCode];
    for (const cityCode in cities) {
      if (cities[cityCode] === name) {
        return cityCode;
      }
    }
  }

  // 查找区县级
  for (const cityCode in pcaa) {
    if (cityCode === "86" || /^\d{2}0000$/.test(cityCode)) continue;

    const districts = pcaa[cityCode];
    for (const districtCode in districts) {
      if (districts[districtCode] === name) {
        return districtCode;
      }
    }
  }

  return null;
}

export default {
  regionData,
  getProvinces,
  getCities,
  getDistricts,
  getRegionName,
  getRegionData,
  getRegionCodeByName,
};
