import {getQueryObject} from '@/utils'
import {getToken, setToken} from '@/utils/auth'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import Vue from 'vue'
import VueRouter from 'vue-router'
import RouterConfig from './router.config'

Vue.use(VueRouter)

const router = new VueRouter({
  mode: 'hash',
  base: process.env.BASE_URL,
  routes: RouterConfig,
  scrollBehavior: () => ({y: 0})
})
// 进度条设置
NProgress.configure({showSpinner: false})

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  const params = getQueryObject()
  let token = params.token
  if (token) {
    token = token.replace('#/', '')
    setToken(token)
    setTimeout(() => {
      let href = window.location.href
      const url = href.slice(0, href.lastIndexOf('?'))
      window.location.href = url
    })
    return
  }
  const {path, meta} = to
  next()
  // NProgress.done();

  // // 已登录
  if (getToken()) {
    if (path === '/login') {
      next({path: '/', replace: true})
      NProgress.done()
    } else {
      next()
      NProgress.done()
    }
  } else {
    // 未登录
    if (path === '/login' || path === '/report' || path === '/jump' || path.indexOf('large-') !== -1) {
      next()
    } else {
      next('/login')
    }
    NProgress.done()
  }
})

// 设置页面title
router.afterEach((to, from, next) => {
  document.title = to.meta.title || ''
  NProgress.done()
  // next()
})

export default router
