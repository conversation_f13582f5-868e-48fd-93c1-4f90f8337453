/**
 * @des: 路由相关配置
 * @author: xve
 * @time: 2022-04-06 06:33:47
 */

import Layout from "@/pages/layout";
import LargeLayout from "@/large/pages/layout";
import SimpleLayout from "@/large/pages/layout/simple.vue";
import Login from "@/pages/login";
import Cookies from "js-cookie";

const usetType = Cookies.get("userType");

/**
 * @param title 用于设置页面标题
 * @param keepAlive 是否缓存页面
 */
export default [
  {
    path: "/",
    component: Layout,
    name: "Layout",
    menu: true,
    children: [
      {
        path: "",
        component: () => import("@/pages/home/<USER>"),
        name: "home",
        meta: { title: "首页" },
        menu: true,
      },
    ],
  },
  {
    path: "/learnPublish",
    component: Layout,
    name: "learnPublish",
    menu: true,
    userType: 2,
    meta: { title: "学习发布" },
    children: [
      {
        path: "/learnPublish/samplingRegulation",
        name: "learnPublish-samplingRegulation",
        menu: true,
        meta: { title: "抽样细则" },
        component: () =>
          import("@/pages/learnPublish/samplingRegulation/index"),
      },
      {
        path: "/learnPublish/samplingSpecification",
        name: "learnPublish-samplingSpecification",
        menu: true,
        meta: { title: "抽样规范" },
        component: () =>
          import("@/pages/learnPublish/samplingSpecification/index"),
      },
      {
        path: "/learnPublish/commonProblem",
        name: "learnPublish-commonProblem",
        menu: true,
        meta: { title: "常见问题" },
        component: () => import("@/pages/learnPublish/commonProblem/index"),
      },
      {
        path: "/learnPublish/commonProblem/add",
        name: "learnPublish-commonProblem-add",
        menu: false,
        meta: { title: "常见问题新增" },
        component: () => import("@/pages/learnPublish/commonProblem/add"),
      },
      {
        path: "/learnPublish/commonProblem/edit",
        name: "learnPublish-commonProblem-edit",
        menu: false,
        meta: { title: "常见问题编辑" },
        component: () => import("@/pages/learnPublish/commonProblem/edit"),
      },
    ],
  },
  // 抽样计划管理
  {
    path: "/samplePlan",
    component: Layout,
    name: "samplePlanUser1",
    menu: true,
    userType: 1,
    meta: { title: "抽样计划管理" },
    children: [
      {
        path: "/samplePlan/sampling",
        name: "sampling-index",
        menu: true,
        meta: { title: "抽检数据查询" },
        component: () => import("@/pages/sampling/index"),
      },
      {
        path: "/samplePlan/entrustNew",
        name: "entrust-index-new",
        menu: true,
        meta: { title: "计划维护" },
        component: () => import("@/pages/entrustNew/index"),
      },
      {
        path: "/samplePlan/entrustAdd",
        name: "entrust-index-add",
        menu: false,
        meta: { title: "新增计划" },
        component: () => import("@/pages/entrustNew/plan/edit"),
      },
      {
        path: "/samplePlan/entrustEdit",
        name: "entrust-index-edit",
        menu: false,
        meta: { title: "编辑计划" },
        component: () => import("@/pages/entrustNew/plan/edit"),
      },
      {
        path: "/samplePlan/progress",
        name: "sampleProgress-index",
        menu: true,
        meta: { title: "进度跟踪查询" },
        component: () => import("@/pages/sampleProgress/index"),
      },
    ],
  },
  // 系统管理
  {
    path: "/sys",
    component: Layout,
    name: "sys",
    menu: true,
    meta: { title: "系统管理" },
    children: [
      {
        path: "/sys/organization",
        name: "organization-index",
        menu: true,
        meta: { title: "机构管理" },
        component: () => import("@/pages/organization/index"),
      },
      {
        path: "/sys/user",
        name: "user-index",
        menu: true,
        meta: { title: "用户管理" },
        component: () => import("@/pages/sys/user/index"),
      },
      {
        path: "/sys/role",
        name: "role-index",
        menu: true,
        meta: { title: "角色管理" },
        component: () => import("@/pages/sys/role/index"),
      },
      {
        path: "/sys/menu",
        name: "menu-index",
        menu: true,
        meta: { title: "菜单管理" },
        component: () => import("@/pages/sys/menu/index"),
      },
      // {
      //   path: "/sya/menu/detail",
      //   name: "menu-detail",
      //   menu: false,
      //   meta: { title: "菜单详情" },
      //   component: () => import("@/pages/sys/menu/detail"),
      // },
      // {
      //   path: "/sya/menu/add",
      //   name: "menu-add",
      //   menu: false,
      //   meta: { title: "新增菜单信息" },
      //   component: () => import("@/pages/sys/menu/add"),
      // },
      // {
      //   path: "/sya/menu/edit",
      //   name: "menu-edit",
      //   menu: false,
      //   meta: { title: "编辑菜单信息" },
      //   component: () => import("@/pages/sys/menu/edit"),
      // },
      // {
      //   path: "/sys/rules",
      //   name: "sys-rules",
      //   menu: true,
      //   meta: { title: "规则配置" },
      //   component: () => import("@/pages/sys/rules/index"),
      // },
      // {
      //   path: "/sya/foodCategory",
      //   name: "fc-index",
      //   menu: true,
      //   meta: { title: "食品分类管理" },
      //   component: () => import("@/pages/sys/foodCategory/index"),
      // },
      // {
      //   path: "/sya/foodCategory/detail",
      //   name: "fc-detail",
      //   menu: false,
      //   meta: { title: "食品分类详情" },
      //   component: () => import("@/pages/sys/foodCategory/detail"),
      // },
      // {
      //   path: "/sya/foodCategory/add",
      //   name: "fc-add",
      //   menu: false,
      //   meta: { title: "新增食品分类" },
      //   component: () => import("@/pages/sys/foodCategory/add"),
      // },
      // {
      //   path: "/sya/foodCategory/edit",
      //   name: "fc-edit",
      //   menu: false,
      //   meta: { title: "编辑食品分类" },
      //   component: () => import("@/pages/sys/foodCategory/edit"),
      // },
      // {
      //   path: "/sya/sampleMethod",
      //   name: "sm-index",
      //   menu: true,
      //   meta: { title: "抽样办法管理" },
      //   component: () => import("@/pages/sys/sampleMethod/index"),
      // },
      // {
      //   path: "/sya/sampleMethod/detail",
      //   name: "sm-detail",
      //   menu: false,
      //   meta: { title: "抽样办法详情" },
      //   component: () => import("@/pages/sys/sampleMethod/detail"),
      // },
      // {
      //   path: "/sya/sampleMethod/add",
      //   name: "sm-add",
      //   menu: false,
      //   meta: { title: "新增抽样办法" },
      //   component: () => import("@/pages/sys/sampleMethod/add"),
      // },
      // {
      //   path: "/sya/sampleMethod/edit",
      //   name: "sm-edit",
      //   menu: false,
      //   meta: { title: "编辑抽样办法" },
      //   component: () => import("@/pages/sys/sampleMethod/edit"),
      // },
      // {
      //   path: "/sys/message",
      //   name: "sys-message",
      //   menu: true,
      //   meta: { title: "消息管理" },
      //   component: () => import("@/pages/sys/message/index"),
      // },
      // {
      //   path: "/sys/message/add",
      //   name: "sys-message-add",
      //   menu: false,
      //   meta: { title: "消息新增" },
      //   component: () => import("@/pages/sys/message/add"),
      // },
      // {
      //   path: "/sys/message/edit",
      //   name: "sys-message-edit",
      //   menu: false,
      //   meta: { title: "消息编辑" },
      //   component: () => import("@/pages/sys/message/edit"),
      // },
      // {
      //   path: "/sys/version",
      //   name: "sys-version",
      //   menu: true,
      //   meta: { title: "版本管理" },
      //   component: () => import("@/pages/sys/version/index"),
      // },
      // {
      //   path: "/sys/version/add",
      //   name: "sys-version-add",
      //   menu: false,
      //   meta: { title: "版本新增" },
      //   component: () => import("@/pages/sys/version/add"),
      // },
      // {
      //   path: "/sys/version/edit",
      //   name: "sys-version-edit",
      //   menu: false,
      //   meta: { title: "版本编辑" },
      //   component: () => import("@/pages/sys/version/edit"),
      // },
      // {
      //   path: "/sys/feedback",
      //   name: "sys-feedback",
      //   menu: true,
      //   meta: { title: "意见反馈" },
      //   component: () => import("@/pages/sys/feedback/index"),
      // },
      // {
      //   path: "/sys/feedback/detail",
      //   name: "sys-feedback-detail",
      //   menu: false,
      //   meta: { title: "意见反馈详情" },
      //   component: () => import("@/pages/sys/feedback/detail"),
      // },
      // {
      //   path: "/sys/chickenSoup",
      //   name: "sys-chickenSoup",
      //   menu: true,
      //   meta: { title: "每日鸡汤" },
      //   component: () => import("@/pages/sys/chickenSoup/index"),
      // },
      // {
      //   path: "/sys/authenticationCodeManage",
      //   name: "sys-authenticationCodeManage",
      //   menu: true,
      //   meta: { title: "认证码管理" },
      //   component: () => import("@/pages/sys/authenticationCodeManage/index"),
      // },
      // {
      //   path: "/sys/helper",
      //   name: "sys-helper",
      //   menu: true,
      //   meta: { title: "抽样助手" },
      //   component: () => import("@/pages/sys/helper/index"),
      // },
      // {
      //   path: "/sys/appbanner",
      //   name: "sys-appbanner",
      //   menu: true,
      //   meta: { title: "app首页banner" },
      //   component: () => import("@/pages/sys/banner/index"),
      // },
    ],
  },
  // 任务管理
  {
    path: "/task",
    component: Layout,
    name: "task",
    menu: true,
    meta: { title: "任务管理" },
    children: [
      {
        path: "/task/hall",
        name: "task-hall",
        menu: true,
        meta: { title: "任务大厅" },
        component: () => import("@/pages/task/index"),
      },
      {
        path: "/task/pending",
        name: "task-pending",
        menu: true,
        meta: { title: "待执行任务" },
        component: () => import("@/pages/task/pending"),
      },
      {
        path: "/task/completed",
        name: "task-completed",
        menu: true,
        meta: { title: "已完成任务" },
        component: () => import("@/pages/task/completed"),
      },
      {
        path: "/task/detail/:id",
        name: "task-detail",
        menu: false,
        meta: { title: "任务详情" },
        component: () => import("@/pages/task/detail"),
      },
    ],
  },
  {
    path: "/community",
    component: Layout,
    name: "community",
    menu: true,
    userType: 2,
    meta: { title: "社区" },
    children: [
      {
        path: "/community/community",
        name: "community-community",
        menu: true,
        meta: { title: "社区" },
        component: () => import("@/pages/community/community/index"),
      },
      {
        path: "/community/community/detail",
        name: "community-community-detail",
        menu: false,
        meta: { title: "社区详情" },
        component: () => import("@/pages/community/community/detail"),
      },
      {
        path: "/community/topic",
        name: "community-topic",
        menu: true,
        meta: { title: "话题管理" },
        component: () => import("@/pages/community/topic/index"),
      },
    ],
  },
  {
    path: "/sampleVideo",
    component: Layout,
    name: "sampleVideo",
    menu: true,
    userType: 2,
    meta: { title: "抽样教学视频" },
    children: [
      {
        path: "/sampleVideo/sampleVideo",
        name: "sampleVideo-sampleVideo",
        menu: true,
        meta: { title: "抽样教学视频" },
        component: () => import("@/pages/sampleVideo/sampleVideo/index"),
      },
      {
        path: "/sampleVideo/order",
        name: "sampleVideo-order",
        menu: true,
        meta: { title: "订单信息" },
        component: () => import("@/pages/sampleVideo/order/index"),
      },
    ],
  },
  {
    path: "/qa",
    component: Layout,
    name: "qa",
    menu: true,
    userType: 2,
    meta: { title: "常见问题-问答" },
    children: [
      {
        path: "/qa/qa",
        name: "qa-qa",
        menu: true,
        meta: { title: "常见问题-问答" },
        component: () => import("@/pages/qa/qa/index"),
      },
      {
        path: "/qa/qa/detail",
        name: "qa-qa-detail",
        menu: false,
        meta: { title: "问答详情" },
        component: () => import("@/pages/qa/qa/detail"),
      },
    ],
  },
  {
    path: "/large",
    component: LargeLayout,
    name: "large",
    menu: true,
    children: [
      {
        path: "/large-sample",
        menu: true,
        meta: { title: "抽样监控" },
        component: () => import("@/large/pages/sample/index"),
      },
    ],
  },
  {
    path: "/simple",
    component: SimpleLayout,
    name: "simple",
    menu: false,
    children: [
      {
        path: "/simple-sample",
        menu: false,
        meta: { title: "抽样监控" },
        component: () => import("@/large/pages/sample/index"),
      },
    ],
  },
  // 内部管理
  {
    path: "/internal",
    component: Layout,
    name: "internal",
    menu: true,
    meta: { title: "内部管理" },
    children: [
      {
        path: "/internal/employee",
        name: "internal-employee",
        menu: true,
        meta: { title: "员工管理" },
        component: () => import("@/pages/internal/employee/index.vue"),
      },
      {
        path: "/internal/orgInfo",
        name: "internal-org-info",
        menu: true,
        meta: { title: "机构信息维护" },
        component: () => import("@/pages/internal/orgInfo/index.vue"),
      },
    ],
  },
  // 抽检课程
  {
    path: "/inspectionCourse",
    component: Layout,
    name: "inspectionCourse",
    menu: true,
    meta: { title: "抽检课程" },
    children: [
      {
        path: "/inspectionCourse/index",
        name: "inspectionCourse-index",
        menu: true,
        meta: { title: "抽检课程" },
        component: () => import("@/pages/inspectionCourse/index.vue"),
      },
    ],
  },
  // 知识库
  {
    path: "/knowledgeBase",
    component: Layout,
    name: "knowledgeBase",
    menu: true,
    meta: { title: "知识库" },
    children: [
      {
        path: "/knowledgeBase/index",
        name: "knowledgeBase-index",
        menu: true,
        meta: { title: "抽检课程管理" },
        component: () => import("@/pages/knowledgeBase/index.vue"),
      },
      {
        path: "/knowledgeBase/playbackList",
        name: "knowledgeBase-playbackList",
        menu: true,
        meta: { title: "回放列表" },
        component: () => import("@/pages/knowledgeBase/playbackList.vue"),
      },
    ],
  },
  // 集中抽样
  {
    path: "/concentrated",
    component: Layout,
    name: "concentrated",
    menu: true,
    meta: { title: "集中抽样" },
    children: [
      {
        path: "/concentrated/index",
        name: "concentrated-index",
        menu: true,
        meta: { title: "集中抽样" },
        component: () => import("@/pages/sampleCheck/concentrated/index"),
      },
    ],
  },
  // 重复抽样
  {
    path: "/repeated",
    component: Layout,
    name: "repeated",
    menu: true,
    meta: { title: "重复抽样" },
    children: [
      {
        path: "/repeated/index",
        name: "repeated-index",
        menu: true,
        meta: { title: "重复抽样" },
        component: () => import("@/pages/sampleCheck/repeated/index"),
      },
    ],
  },
  // 非底部tab路由
  {
    path: "/login",
    meta: { title: "登录", keepAlive: false },
    component: Login,
  },
  {
    path: "/report",
    component: () => import("@/pages/reporth5/index"),
    meta: { title: "转发", keepAlive: false },
  },
  {
    path: "/jump",
    component: () => import("@/pages/jump/index"),
    meta: { title: "跳板", keepAlive: false },
  },
  {
    path: "/404",
    component: () => import("@/pages/error/404"),
    meta: { title: "提示", keepAlive: false },
  },
  {
    path: "/dataVerify",
    component: Layout,
    name: "dataVerify",
    menu: true,
    userType: 2,
    children: [
      {
        path: "/dataVerify/index",
        name: "dataVerify-index",
        menu: true,
        meta: { title: "数据抽查校验" },
        component: () => import("@/pages/dataVerify/index"),
      },
    ],
  },
  {
    path: "/rules",
    component: Layout,
    name: "rules",
    menu: true,
    userType: 2,
    children: [
      {
        path: "/rules/index",
        name: "rules-index",
        menu: true,
        meta: { title: "规则配置" },
        component: () => import("@/pages/sys/rules/index"),
      },
    ],
  },
  {
    path: "/dataVerify/reflex",
    component: Layout,
    name: "dataVerifyReflex",
    menu: true,
    userType: 2,
    meta: { title: "映射表" },
    children: [
      {
        path: "/dataVerify/reflex/index",
        name: "dataVerify-reflex-index",
        menu: true,
        meta: { title: "映射" },
        component: () => import("@/pages/dataVerify/reflex/index"),
      },
    ],
  },
  {
    path: "/dataMaintenance",
    component: Layout,
    name: "dataMaintenance",
    menu: true,
    meta: { title: "数据维护" },
    children: [
      {
        path: "/dataMaintenance/index",
        name: "dataMaintenance-index",
        menu: true,
        meta: { title: "数据维护" },
        component: () => import("@/pages/dataMaintenance/index"),
      },
    ],
  },
  {
    path: "*",
    redirect: "/",
  },
];
