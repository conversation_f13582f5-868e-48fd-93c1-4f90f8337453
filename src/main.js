import Instance from "@/config/use";
import { UTable, UTableColumn } from "umy-ui";
import "umy-ui/lib/theme-chalk/index.css"; // 引入样式
import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "./utils/directive/int.js"; // 权限
Vue.use(Instance);
Vue.component(UTable.name, UTable);
Vue.component(UTableColumn.name, UTableColumn);
Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app");
