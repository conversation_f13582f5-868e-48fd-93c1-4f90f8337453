export function fetchAndCacheData(callback) {
    return new Promise((resolve, reject) => {
        const cachedData = localStorage.getItem("progressArr");
        if (cachedData) {
          // 如果存在，则解析数据
          const parsedData = JSON.parse(cachedData);
    
          // 使用setTimeout来确保回调异步执行
          setTimeout(() => {
            callback(parsedData);
          }, 0); // 0 表示立即执行，但保证在当前事件循环结束后执行
    
          resolve(parsedData);
        } else {
          callback([]);
          resolve([]);
        }
    });
  }