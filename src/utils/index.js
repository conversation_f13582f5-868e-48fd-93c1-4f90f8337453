/**
 * @des: 工具方法
 * @author: xve
 * @time: 2022-04-06 09:55:10
 */

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

/**
 * Parse the time to string
 * @param {(Object|string|number)} date
 * @param {string} time
 * @returns {string}
 */
export function formatDate(time, fmt) {
  if (arguments.length === 0) {
    return null
  }
  let date = null
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  let regObj = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    'S': date.getMilliseconds() // 毫秒
  };
  if (/(y+)/.test(fmt)){
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').slice(0, RegExp.$1.length))
  }
  for(let reg in regObj){
    if(new RegExp('(' + reg + ')').test(fmt)){
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (regObj[reg]) : (('00' + regObj[reg]).substr(('' + regObj[reg]).length)))
    }
  }
  return fmt;
}
/**
 * 将秒转换为时分秒
 * @param {*} value 待转换的秒数
 * @param {*} isZH  是否转换为中文
 * @param {*} isShowH 是否显示小时
 */
export const formatSecondsToTime = (value, isZH = false, isShowH = true) => {
  let result = parseInt(value)
  if(Number.isNaN(result)) {
    return ''
  }
  let h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600);
  let m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result / 60 % 60));
  let s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60));

  let res = '';
  if(isShowH){
    res += `${h}${isZH ? '时' : ':'}`
  }
  res += `${m}${isZH ? '分' : ':'}`
  res += `${s}${isZH ? '秒' : ''}`
  return res;
}

/**
 * 获取url中参数
 * @param {*} name 参数名
 */
export function getUrlParam(name, url) {
  if(!url){
    url = window.location.search
  }
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
  const r = url.substr(1).match(reg)
  if (r != null) return decodeURI(r[2])
  return null
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * 判断当前环境是否微信浏览器
 * @returns {boolean}
 */
export function isMicroMessenger() {
  let result = false;
  let userAgent = window.navigator.userAgent;
  if(userAgent.indexOf('MicroMessenger') > -1) {
    result = true;
  }
  return result;
}

/**
 * 过滤图片标签
 * @param {*} html
 */
export function filterImg(html){
  return html.replace(/<img.*?(?:>|\/>)/gi, '')
}

/**
 * 判断手机号
 * @param {*} val
 */
export function isPhone(val){
  const reg = /^1\d{10}/g
  return reg.test(val)
}

/**
 * 判断邮箱
 * @param {*} val
 */
export function isEmail(val){
  const reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/g
  return reg.test(val)
}

export function filterCode(){
  let search = window.location.search
  const code = getUrlParam('code')
  if(code){
    search = search.replace(/(^|&|\?)code=([^&]*)(&|$)/g,'')
  }
  return window.location.protocol + '//' + window.location.host + window.location.hash + search
}
