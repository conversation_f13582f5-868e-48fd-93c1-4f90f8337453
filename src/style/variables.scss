// base color
$primary: rgb(49, 108, 114);
$blue: #2170d9;
$red: #f12525;
$green: #22ba90;
$yellow: #ffba00;
$gray: #8c8c8c;

// sidebar
$menuText: #303133;
$menuActiveText: rgb(49, 108, 114);
$subMenuActiveText: rgb(49, 108, 114); // https://github.com/ElemeFE/element/issues/12951

$menuBg: #ffffff;
$menuHover: #ffffff;

$subMenuBg: #ffffff;
$subMenuHover: #ffffff;

$sideBarWidth: 200px;

$border-color: #dfe3e8;

$title-color: #262626;
$text-color: #8c8c8c;

$background-light: #fbfbfc;
$background-color: #fafafa;
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
