/*
! tailwindcss v3.3.6 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
*/

html {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.collapse {
  visibility: collapse;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.-right-\[10px\] {
  right: -10px;
}

.-right-\[9px\] {
  right: -9px;
}

.-top-\[10px\] {
  top: -10px;
}

.-top-\[9px\] {
  top: -9px;
}

.bottom-5 {
  bottom: 1.25rem;
}

.bottom-\[20px\] {
  bottom: 20px;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-6 {
  left: 1.5rem;
}

.right-0 {
  right: 0px;
}

.right-4 {
  right: 1rem;
}

.right-6 {
  right: 1.5rem;
}

.top-0 {
  top: 0px;
}

.top-1\/2 {
  top: 50%;
}

.top-5 {
  top: 1.25rem;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.\!my-0 {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}

.mx-\[2px\] {
  margin-left: 2px;
  margin-right: 2px;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-\[2px\] {
  margin-bottom: 2px;
}

.mb-\[6px\] {
  margin-bottom: 6px;
}

.mb-\[8px\] {
  margin-bottom: 8px;
}

.ml-auto {
  margin-left: auto;
}

.mr-\[2px\] {
  margin-right: 2px;
}

.mr-\[4px\] {
  margin-right: 4px;
}

.mr-\[8px\] {
  margin-right: 8px;
}

.mt-\[4px\] {
  margin-top: 4px;
}

.mt-\[6px\] {
  margin-top: 6px;
}

.mt-\[8px\] {
  margin-top: 8px;
}

.ml-\[10px\] {
  margin-left: 10px;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.\!h-\[42px\] {
  height: 42px !important;
}

.\!h-\[48px\] {
  height: 48px !important;
}

.h-3 {
  height: 0.75rem;
}

.h-\[12px\] {
  height: 12px;
}

.h-\[140px\] {
  height: 140px;
}

.h-\[14px\] {
  height: 14px;
}

.h-\[150px\] {
  height: 150px;
}

.h-\[18px\] {
  height: 18px;
}

.h-\[197px\] {
  height: 197px;
}

.h-\[20px\] {
  height: 20px;
}

.h-\[24px\] {
  height: 24px;
}

.h-\[26px\] {
  height: 26px;
}

.h-\[28px\] {
  height: 28px;
}

.h-\[300px\] {
  height: 300px;
}

.h-\[32px\] {
  height: 32px;
}

.h-\[342px\] {
  height: 342px;
}

.h-\[38px\] {
  height: 38px;
}

.h-\[42px\] {
  height: 42px;
}

.h-\[442\] {
  height: 442;
}

.h-\[442px\] {
  height: 442px;
}

.h-\[45px\] {
  height: 45px;
}

.h-\[46px\] {
  height: 46px;
}

.h-\[50px\] {
  height: 50px;
}

.h-\[60px\] {
  height: 60px;
}

.h-\[65px\] {
  height: 65px;
}

.h-\[66px\] {
  height: 66px;
}

.h-\[70px\] {
  height: 70px;
}

.h-\[720px\] {
  height: 720px;
}

.h-\[74px\] {
  height: 74px;
}

.h-\[75vh\] {
  height: 75vh;
}

.h-\[78px\] {
  height: 78px;
}

.h-\[80px\] {
  height: 80px;
}

.h-\[8px\] {
  height: 8px;
}

.h-\[90\%\] {
  height: 90%;
}

.h-\[99\%\] {
  height: 99%;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.h-\[36px\] {
  height: 36px;
}

.\!w-full {
  width: 100% !important;
}

.w-3 {
  width: 0.75rem;
}

.w-8 {
  width: 2rem;
}

.w-\[104px\] {
  width: 104px;
}

.w-\[112px\] {
  width: 112px;
}

.w-\[115px\] {
  width: 115px;
}

.w-\[116px\] {
  width: 116px;
}

.w-\[120px\] {
  width: 120px;
}

.w-\[122px\] {
  width: 122px;
}

.w-\[128px\] {
  width: 128px;
}

.w-\[148px\] {
  width: 148px;
}

.w-\[14px\] {
  width: 14px;
}

.w-\[15\%\] {
  width: 15%;
}

.w-\[150px\] {
  width: 150px;
}

.w-\[18px\] {
  width: 18px;
}

.w-\[20\%\] {
  width: 20%;
}

.w-\[200px\] {
  width: 200px;
}

.w-\[20px\] {
  width: 20px;
}

.w-\[214px\] {
  width: 214px;
}

.w-\[22px\] {
  width: 22px;
}

.w-\[234px\] {
  width: 234px;
}

.w-\[25\%\] {
  width: 25%;
}

.w-\[26px\] {
  width: 26px;
}

.w-\[278px\] {
  width: 278px;
}

.w-\[280px\] {
  width: 280px;
}

.w-\[28px\] {
  width: 28px;
}

.w-\[2px\] {
  width: 2px;
}

.w-\[300px\] {
  width: 300px;
}

.w-\[30px\] {
  width: 30px;
}

.w-\[35px\] {
  width: 35px;
}

.w-\[378px\] {
  width: 378px;
}

.w-\[396px\] {
  width: 396px;
}

.w-\[400px\] {
  width: 400px;
}

.w-\[40px\] {
  width: 40px;
}

.w-\[42px\] {
  width: 42px;
}

.w-\[46px\] {
  width: 46px;
}

.w-\[496px\] {
  width: 496px;
}

.w-\[55px\] {
  width: 55px;
}

.w-\[59px\] {
  width: 59px;
}

.w-\[60px\] {
  width: 60px;
}

.w-\[64px\] {
  width: 64px;
}

.w-\[65px\] {
  width: 65px;
}

.w-\[68px\] {
  width: 68px;
}

.w-\[6px\] {
  width: 6px;
}

.w-\[72px\] {
  width: 72px;
}

.w-\[75px\] {
  width: 75px;
}

.w-\[82px\] {
  width: 82px;
}

.w-full {
  width: 100%;
}

.w-\[36px\] {
  width: 36px;
}

.max-w-fit {
  max-width: -moz-fit-content;
  max-width: fit-content;
}

.flex-1 {
  flex: 1 1 0%;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[1px\] {
  --tw-translate-y: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-move {
  cursor: move;
}

.cursor-pointer {
  cursor: pointer;
}

.resize {
  resize: both;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-\[1fr_1fr_1fr\] {
  grid-template-columns: 1fr 1fr 1fr;
}

.grid-rows-\[124px_111px_186px_1fr\] {
  grid-template-rows: 124px 111px 186px 1fr;
}

.grid-rows-\[124px_196px_186px_1fr\] {
  grid-template-rows: 124px 196px 186px 1fr;
}

.grid-rows-\[32px_146px_1fr\] {
  grid-template-rows: 32px 146px 1fr;
}

.grid-rows-\[32px_1fr\] {
  grid-template-rows: 32px 1fr;
}

.grid-rows-\[42px_1fr\] {
  grid-template-rows: 42px 1fr;
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.break-keep {
  word-break: keep-all;
}

.rounded {
  border-radius: 0.25rem;
}

.border {
  border-width: 1px;
}

.border-\[0\] {
  border-width: 0;
}

.border-\[2px\] {
  border-width: 2px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-solid {
  border-style: solid;
}

.border-\[\#52CC92\] {
  --tw-border-opacity: 1;
  border-color: rgb(82 204 146 / var(--tw-border-opacity));
}

.border-\[\#81D7FC\] {
  --tw-border-opacity: 1;
  border-color: rgb(129 215 252 / var(--tw-border-opacity));
}

.border-\[\#FFD86D\] {
  --tw-border-opacity: 1;
  border-color: rgb(255 216 109 / var(--tw-border-opacity));
}

.border-\[\#FFFFFF\] {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.border-\[rgba\(255\2c 255\2c 255\2c 0\.24\)\] {
  border-color: rgba(255,255,255,0.24);
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.bg-\[\#08183066\] {
  background-color: #08183066;
}

.bg-\[\#81D7FCFF\] {
  background-color: #81D7FCFF;
}

.bg-\[rgba\(0\2c 0\2c 0\2c \.01\)\] {
  background-color: rgba(0,0,0,.01);
}

.bg-\[rgba\(0\2c 0\2c 0\2c 0\.6\)\] {
  background-color: rgba(0,0,0,0.6);
}

.bg-\[rgba\(12\2c 33\2c 64\2c 0\.6\)\] {
  background-color: rgba(12,33,64,0.6);
}

.bg-\[rgba\(76\2c 135\2c 255\2c 0\.24\)\] {
  background-color: rgba(76,135,255,0.24);
}

.bg-\[rgba\(76\2c 135\2c 255\2c 0\.6\)\] {
  background-color: rgba(76,135,255,0.6);
}

.bg-\[rgba\(8\2c 24\2c 48\2c 0\.4\)\] {
  background-color: rgba(8,24,48,0.4);
}

.bg-\[rgba\(8\2c 24\2c 48\2c 0\.52\)\] {
  background-color: rgba(8,24,48,0.52);
}

.bg-\[rgba\(8\2c 24\2c 48\2c 0\.5216\)\] {
  background-color: rgba(8,24,48,0.5216);
}

.bg-\[rgba\(9\2c 20\2c 36\2c 0\.4\)\] {
  background-color: rgba(9,20,36,0.4);
}

.bg-\[url\(\'\.\.\/assets\/images\/bg-optimized\.webp\'\)\] {
  background-image: url('../assets/images/bg-optimized.webp');
}

.bg-\[url\(\'\.\.\/assets\/images\/item-title-bg\.webp\'\)\] {
  background-image: url('../assets/images/item-title-bg.webp');
}

.bg-\[url\(\'\.\.\/assets\/images\/sample-plan-today-number-bg\.webp\'\)\] {
  background-image: url('../assets/images/sample-plan-today-number-bg.webp');
}

.bg-\[url\(\'\.\.\/assets\/images\/top-bg\.webp\'\)\] {
  background-image: url('../assets/images/top-bg.webp');
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.from-\[\#81D7FC\] {
  --tw-gradient-from: #81D7FC var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(129 215 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#F18529\] {
  --tw-gradient-from: #F18529 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(241 133 41 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-\[rgba\(241\2c 133\2c 41\2c 0\)\] {
  --tw-gradient-to: rgba(241,133,41,0) var(--tw-gradient-to-position);
}

.to-\[rgba\(58\2c 130\2c 255\2c 0\)\] {
  --tw-gradient-to: rgba(58,130,255,0) var(--tw-gradient-to-position);
}

.bg-cover {
  background-size: cover;
}

.p-1 {
  padding: 0.25rem;
}

.p-1\.5 {
  padding: 0.375rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-\[6px\] {
  padding: 6px;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-\[10px\] {
  padding-left: 10px;
  padding-right: 10px;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-\[16px\] {
  padding-bottom: 16px;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-\[10px\] {
  padding-left: 10px;
}

.pl-\[23px\] {
  padding-left: 23px;
}

.pr-0 {
  padding-right: 0px;
}

.pr-1 {
  padding-right: 0.25rem;
}

.pr-\[15px\] {
  padding-right: 15px;
}

.pr-\[8px\] {
  padding-right: 8px;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.font-\[OswaldBold\] {
  font-family: OswaldBold;
}

.font-\[OswaldRegular\] {
  font-family: OswaldRegular;
}

.font-\[SourceHanSansCNBold\] {
  font-family: SourceHanSansCNBold;
}

.font-\[SourceHanSansCNMedium\] {
  font-family: SourceHanSansCNMedium;
}

.text-\[12px\] {
  font-size: 12px;
}

.text-\[13px\] {
  font-size: 13px;
}

.text-\[14px\] {
  font-size: 14px;
}

.text-\[16px\] {
  font-size: 16px;
}

.text-\[18px\] {
  font-size: 18px;
}

.text-\[20px\] {
  font-size: 20px;
}

.text-\[26px\] {
  font-size: 26px;
}

.text-\[30px\] {
  font-size: 30px;
}

.text-\[56px\] {
  font-size: 56px;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.font-\[14px\] {
  font-weight: 14px;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.italic {
  font-style: italic;
}

.leading-\[12px\] {
  line-height: 12px;
}

.leading-\[22px\] {
  line-height: 22px;
}

.leading-\[24px\] {
  line-height: 24px;
}

.leading-\[30px\] {
  line-height: 30px;
}

.leading-\[32px\] {
  line-height: 32px;
}

.leading-none {
  line-height: 1;
}

.text-\[\#000000E0\] {
  color: #000000E0;
}

.text-\[\#52CC92FF\] {
  color: #52CC92FF;
}

.text-\[\#52CC92\] {
  --tw-text-opacity: 1;
  color: rgb(82 204 146 / var(--tw-text-opacity));
}

.text-\[\#81D7FCFF\] {
  color: #81D7FCFF;
}

.text-\[\#81D7FC\] {
  --tw-text-opacity: 1;
  color: rgb(129 215 252 / var(--tw-text-opacity));
}

.text-\[\#FFD86DFF\] {
  color: #FFD86DFF;
}

.text-\[\#FFD86D\] {
  --tw-text-opacity: 1;
  color: rgb(255 216 109 / var(--tw-text-opacity));
}

.text-\[\#FFFFFFB2\] {
  color: #FFFFFFB2;
}

.text-\[\#FFFFFF\] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-gray-100 {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-\[\#00B1F4\] {
  --tw-text-opacity: 1;
  color: rgb(0 177 244 / var(--tw-text-opacity));
}

.underline {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[inset_0px_0px_40px_0px_\#3571EB\] {
  --tw-shadow: inset 0px 0px 40px 0px #3571EB;
  --tw-shadow-colored: inset 0px 0px 40px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline {
  outline-style: solid;
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.transition {
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.\[writing-mode\:vertical-lr\] {
  writing-mode: vertical-lr;
}
