/**
* I think element-ui's default theme color is too light for long-term use.
* So I modified the default color and you can modify it to your liking.
**/

/* theme color */
// $--color-primary: $primary;
$--color-primary: rgb(49, 108, 114);
$--color-success: #22ba90;
$--color-warning: #ffba00;
$--color-danger: #f12525;
$--color-info: #2170d9;

$--button-font-weight: 400;

// $--color-text-regular: #1f2d3d;

$--border-color-light: #dfe3e8;
$--border-color-lighter: #e6ebf5;

$--table-border: 1px solid#dfe6ec;

/* icon font path, required */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import '~element-ui/packages/theme-chalk/src/index';

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  theme: $--color-primary;
}
