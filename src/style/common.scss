/**
 * @des: 全局通用样式
 * @author: xve
 * @time: 2022-04-06 09:55:10
 */
@import './variables.scss';
 html,body {
  height: 100%;
}
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 16px;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Microsoft YaHei , Helvetica Neue, Source Han Sans SC,Helvetica, PingFang SC, Hiragino Sans GB, Arial, sans-serif;
}
* {
	margin: 0;
	padding: 0;
}
label {
  font-weight: normal;
}

html {
  height: 100%;
  box-sizing: border-box;
}
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.page-layout{
  height: 100vh;
  padding-bottom: 50px;
  background-color: $background-color;
}

.page{
  height: 100%;
  overflow-y: auto;
}
.mt5{
  margin-top: 5px;
}
.mt10{
  margin-top: 10px;
}
.mt20{
  margin-top: 20px;
}
.mb5{
  margin-bottom: 5px;
}
.mb10{
  margin-bottom: 10px;
}
.mb15{
  margin-bottom: 15px;
}
.mb20{
  margin-bottom: 20px;
}
.m10{
  margin: 10px;
}
.mr5{
  margin-right: 5px;
}
.mr10{
  margin-right: 10px;
}
.mr20{
  margin-right: 20px;
}
.mr30{
  margin-right: 30px;
}
.ml5{
  margin-left: 5px;
}
.ml10{
  margin-left: 10px;
}
.ml20{
  margin-left: 20px;
}

.p10{
  padding: 10px;
}
.p20{
  padding: 20px;
}

.flex{
  display: flex;
}
.flex-column{
  flex-direction: column;
}

.justify-between{
  justify-content: space-between!important;
}

.justify-end{
  justify-content: flex-end!important;
}

.justify-center{
  justify-content: center;
}

.align-center{
  align-items: center;
}

.flex-1{
  flex: 1;
}

.flex-2{
  flex: 2;
}

.primary{
  color: $primary;
}

.primary{
  color: $primary!important;
}
.red{
  color: $red;
}
.green{
  color: $green;
}
.blue{

}
.col{
  &-24{
    width: 100%;
  }
  &-18{
    width: 75%;
  }
  &-12{
    width: 50%;
  }
  &-8{
    width: 33%
  }
  &-6{
    width: 24%;
  }
  &-4{
    width: 16%;
  }
}

.text-normal{
  font-style: normal;
}

.text-link{
  color: $blue;

}

.signs{
  display: flex;
  font-size: 12px;
  .signBox{
    margin-left: 20px;
    text-align: center;
    img{
      width: 50px;
      height: 50px;
    }
  }
}
