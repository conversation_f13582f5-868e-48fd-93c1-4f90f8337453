<template>
  <div class="w-full h-full relative flex flex-col overflow-hidden">
    <LHeader ref="header"/>
    <LMain ref="main"/>
  </div>
</template>

<script>
import LHeader from '@/large/components/LHeader/index'
import LMain from './LargeMain.vue'

export default {
  name: 'LargeLayout',
  components: {
    LHeader, LMain
  },
  methods: {
    showModel(val) {
      this.$refs.header.showModel(val)
    }
  }
}
</script>
<style src="@/style/output.css" scoped></style>
<style lang="scss">
.bg-full {
  background-size: 100% 100% !important;
}
</style>
