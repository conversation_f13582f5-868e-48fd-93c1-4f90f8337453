<template>
  <div class="flex-1 h-screen relative bg-[url('../assets/images/bg-optimized.webp')] bg-cover">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews" exclude="addPersonnel">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
</style>
