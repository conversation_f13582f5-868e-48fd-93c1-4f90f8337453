<template>
  <div class="relative  font-[SourceHanSansCNMedium]"
       :style="'width:'+dyamicWrapperWidth+'px'">

    <div v-if="loading" class="w-full h-[442px] flex flex-row flex-nowrap justify-center items-center">
      <l-loading/>
    </div>

    <div v-else class="w-full h-[442]">
      <div
          class="w-full h-[80px] border-b border-solid border-[rgba(255,255,255,0.24)] grid grid-cols-[1fr_1fr_1fr] gap-4 px-3 py-4">
        <div class="w-full truncate text-white" :title="listData ? listData[0]?.spotedComName : null">
          <span class="text-[#FFFFFFB2]">被抽样单位名称: </span>
          <span>{{ listData ? listData[0]?.spotedComName : null }}</span>
        </div>
        <div class="w-full truncate text-white" :title="listData ? listData[0]?.spotAddress : null">
          <span class="text-[#FFFFFFB2]">所在地: </span>
          <span>{{ listData ? listData[0]?.spotAddress : null }}</span>
        </div>
        <div class="w-full truncate text-white" :title="listData ? listData[0]?.spotedComAddress : null">
          <span class="text-[#FFFFFFB2]">单位地址: </span>
          <span>{{ listData ? listData[0]?.spotedComAddress : null }}</span>
        </div>
        <div class="w-full truncate text-white" :title="listData ? listData[0]?.spotedSiteType : null">
          <span class="text-[#FFFFFFB2]">区域类型: </span>
          <span>{{ listData ? listData[0]?.spotedSiteType : null }}</span>
        </div>
        <div class="w-full truncate text-white" :title="listData ? listData[0]?.spotStep : null">
          <span class="text-[#FFFFFFB2]">抽样环节: </span>
          <span>{{ listData ? listData[0]?.spotStep : null }}</span>
        </div>
        <div class="w-full truncate text-white" :title="listData ? listData[0]?.samplingLocation : null">
          <span class="text-[#FFFFFFB2]">抽样地点: </span>
          <span>{{ listData ? listData[0]?.samplingLocation : null }}</span>
        </div>
      </div>

      <div class="w-full h-[342px] grid grid-rows-[42px_1fr]">
        <div
            class="flex flex-row flex-nowrap items-center text-[#FFFFFFB2] border-b border-solid border-[rgba(255,255,255,0.24)]">
          <div class="w-[46px] text-center">序号</div>
          <div class="w-[25%] pl-[10px]">抽样单编号</div>
          <div class="w-[20%] pl-[10px]">样品名称</div>
          <div class="w-[15%] pl-[10px]">抽样日期</div>
          <div class="w-[15%] pl-[10px]">抽样类型</div>
          <div class="w-[15%] pl-[10px]">食品细类</div>
        </div>
        <div class="w-full h-[300px]">
          <swiper v-if="listData.length" ref="swiper" :options="swiperOption" class="overflow-hidden w-full h-full">
            <swiper-slide v-for="(item,index) in listData" :key="index" class="!w-full !my-0">
              <div
                   class="w-full h-[60px] flex flex-row flex-nowrap items-center text-[#FFFFFF] border-b border-solid border-[rgba(255,255,255,0.24)]">
                <div class="w-[46px] text-center">{{ index + 1 }}</div>
                <div class="w-[25%] pl-[10px] text-[#81D7FCFF]" :title="item.spotNo">
                  {{ item?.spotNo }}
                </div>
                <div class="w-[20%] pl-[10px] truncate" :title="item.sampleName">{{ item?.sampleName }}</div>
                <div class="w-[15%] pl-[10px] truncate text-[#FFD86DFF]" :title="item.spotTime.split(' ')[0]">
                  {{ item?.spotTime.split(' ')[0] }}
                </div>
                <div class="w-[15%] pl-[10px] truncate text-[#FFFFFFB2]" :title="item.spotType">
                  {{ item?.spotType }}
                </div>
                <div class="w-[15%] pl-[10px] truncate text-[#FFFFFFB2]" :title="item.cate4">
                  {{ item?.cate4 }}
                </div>
              </div>
            </swiper-slide>
          </swiper>
        </div>

        <div class="absolute -right-[9px] -top-[9px] cursor-pointer" @click="handleClose">
          <l-btn-close/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import {getSamplingOrg} from "@/api/modules/large/sample";
import LLoading from "@/large/components/LLoading/index.vue";
import {Swiper as SwiperClass, Pagination, Mousewheel, Autoplay} from 'swiper/js/swiper.esm'
import getAwesomeSwiper from 'vue-awesome-swiper/dist/exporter'

SwiperClass.use([Pagination, Mousewheel, Autoplay])
const {Swiper, SwiperSlide} = getAwesomeSwiper(SwiperClass)
import 'swiper/css/swiper.css'
import LBtnClose from "@/large/components/LBtnClose/index.vue";

export default {
  name: 'CenterOrganization',
  components: {LBtnClose, LLoading, Swiper, SwiperSlide},
  props: {
    curSelectedDate: {type: Number},
    curShowCategory: {type: String},
    curSelectedOrganization: {type: Object},
    curSelectedcat4: {type: Object},
    curSelectedPlan: {type: Object}
  },
  data() {
    return {
      loading: false,
      dyamicWrapperWidth: window.innerWidth - 378 * 2 - 24 * 2 - 12 * 2,
      listData: [],
      swiperOption: {
        direction: 'vertical',
        spaceBetween: 0,
        loop: true,
        slidesPerView: 5,
        autoplay: {
          delay: 3000, // 每个幻灯片之间的延迟时间（以毫秒为单位）
          disableOnInteraction: false // 用户交互时是否停止自动播放
        },
        speed: 5000,
        reverseDirection: true
      }
    }
  },
  mounted() {
    this.loadData()
    window.addEventListener('resize', this.calculateAreaWidth, false);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateAreaWidth)
  },
  methods: {
    calculateAreaWidth() {
      this.dyamicWrapperWidth = window.innerWidth - 378 * 2 - 24 * 2 - 12 * 2
      this.loading = true
      setTimeout(() => {
        this.loading = false
      })
    },
    loadData() {
      if (!this.curSelectedDate || !this.curSelectedOrganization) {
        return;
      }
      this.loading = true
      let data = {
        timeType: this.curSelectedDate,
        enterpriseLicenseNumber: this.curSelectedOrganization.enterpriseLicenseNumber
      }

      if (this.curShowCategory === 'sampleNum') {
        data.cat4 = this.curSelectedcat4.name;
      }

      if (this.curShowCategory === 'samplePlan') {
        data.reportClassb = this.curSelectedPlan.planName;
      }
      this.listData = []
      getSamplingOrg(data).then(res => {
        if (res.data.length) {
          this.swiperOption.loop = res.data.length >= 5
          this.listData = res.data
        }
      }).catch(res => {
        if (res.msg)
        this.$message.error(res.msg)
      }).finally(() => {
        this.loading = false
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
</style>
