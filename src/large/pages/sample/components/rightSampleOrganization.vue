<template>
  <div class="grid grid-rows-[124px_111px_186px_1fr] gap-4">
    <!--抽样人员信息-->
    <div class="w-[378px] font-[SourceHanSansCNMedium] text-white">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/sampled-org-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">被抽样单位</span>
        </div>
      </h1>

      <div class="w-[378px] h-[78px] bg-[rgba(8,24,48,0.4)] rounded flex flex-row flex-nowrap items-center px-4 gap-4">
        <div>
          <img src="@/assets/images/sample-organization-img.webp" alt=""/>
        </div>
        <div class="w-full text-sm">
          <p class="w-[280px] mb-2 truncate">
            {{ curSelectedOrganization?.enterpriseName || curSelectedOrganization?.unitName }}
          </p>
          <p class="w-[280px] truncate">
            {{ curSelectedOrganization?.enterpriseAddress }}
          </p>
        </div>
      </div>
    </div>

    <!--抽样数量-->
    <div class="w-[378px] text-white font-[SourceHanSansCNMedium]">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/sampling-total-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">{{ getSelectedDate(curSelectedDate) }}抽样数量</span>
        </div>
      </h1>

      <div class="w-full h-[65px] bg-[rgba(8,24,48,0.4)] rounded flex flex-row flex-nowrap items-center pl-4 gap-4">
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
              {{ getSelectedDate(curSelectedDate) }}抽样数量/批
            </div>
            <div class="text-[26px] font-[OswaldRegular] ">
              <l-scroll-number :num="currentSamplingTotal"/>
            </div>
          </div>
        </div>

        <div class="flex-1  flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-green-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">环比</div>
            <div v-if="chainRatio>0"
                 class="text-[26px] font-[OswaldRegular] text-[#52CC92FF] flex items-center gap-1">
              <span>{{ chainRatio + '%' }}</span>
              <img src="@/assets/images/up-icon.webp" alt=""/>
            </div>
            <div v-else-if="chainRatio===0" class="text-[26px] font-[OswaldRegular] text-[#52CC92FF]">-</div>
            <div v-else class="text-[26px] font-[OswaldRegular] text-[#FFD86DFF] flex items-center gap-1">
              <span>{{ chainRatio + '%' }}</span>
              <img src="@/assets/images/down-icon.webp" alt=""/>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--年度抽样概况-->
    <div class="w-[378px] text-white font-[SourceHanSansCNMedium]">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/link-ratio-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">年度抽样概况</span>
        </div>
      </h1>
      <div class="w-full h-[70px] bg-[rgba(8,24,48,0.4)] rounded flex flex-row flex-nowrap items-center pl-4 gap-4">
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div>
              <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
                总抽样数量/批
              </div>
              <div class="text-[26px] font-[OswaldRegular] ">
                <l-scroll-number :num="orgOverview?orgOverview.samplingCount:0"/>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
              已完成检验/批
            </div>
            <div class="text-[26px] font-[OswaldRegular] ">
              <l-scroll-number :num="orgOverview?orgOverview.doneNumber:0"/>
            </div>
          </div>
        </div>
      </div>
      <div class="w-full h-[70px] bg-[rgba(8,24,48,0.4)] rounded flex flex-row flex-nowrap items-center pl-4 gap-4">
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
              待检样品/批
            </div>
            <div class="text-[26px] font-[OswaldRegular] ">
              <l-scroll-number :num="orgOverview?orgOverview.waitNumber:0"/>
            </div>
          </div>
        </div>
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-yellow-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
              已检样品合格率
            </div>
            <div class="text-[26px] font-[OswaldRegular] text-[#FFD86DFF]">
              {{ completionRate }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--年度抽样趋势-->
    <div class="w-[378px] text-white font-[SourceHanSansCNMedium]">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/sample-trend-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">年度抽样趋势</span>
        </div>
      </h1>

      <div
          class="w-full rounded bg-[rgba(8,24,48,0.4)]"
          :style="'height:'+barAreaHeight+'px'">
        <div class="flex flex-row flex-nowrap justify-between items-center px-3 pt-3">
          <span>批</span>
          <div class="flex items-center">
            <span class="inline-block w-3 h-3 bg-[#81D7FCFF] mr-[8px]"></span>
            <span>月度抽样</span>
          </div>
        </div>
        <div id="echartsBar" class="w-full h-full"></div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getChartOrg,
  getNumOrg,
  getOverviewOrg
} from "@/api/modules/large/sample";
import LScrollNumber from "@/large/components/LScrollNumber/index.vue";
import echarts from "@/components/Echart/echarts";
import Cookies from "js-cookie";

export default {
  name: 'RightSampleOrganization',
  components: {LScrollNumber},
  props: {
    curSelectedDate: {type: Number},
    curSelectedOrganization: {type: Object}
  },
  data() {
    return {
      currentSamplingTotal: 0,
      chainRatio: 0,
      orgOverview: {},
      completionRate: '',
      barAreaHeight: window.innerHeight - 620,
      chartData: [],//当前选择用户的echarts对象
      echartsDomObject: null,
      org: localStorage.getItem('stb-orgInfo') ? JSON.parse(localStorage.getItem('stb-orgInfo')) : {},
    }
  },
  mounted() {
    this.loadData()
    window.addEventListener('resize', this.getBarAreaHeight, false);
    this.initChart()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getBarAreaHeight)
  },
  methods: {
    getBarAreaHeight() {
      this.barAreaHeight = window.innerHeight - 620
    },
    loadData() {
      if (this.curSelectedOrganization && this.curSelectedOrganization.enterpriseName) {
        this.loadDataNum()
        this.loadDataOverView()
        this.loadDataChart()
      }
    },
    loadDataNum() {
      getNumOrg({
        timeType: this.curSelectedDate,
        unitName: this.curSelectedOrganization.enterpriseName
      }).then(res => {
        this.currentSamplingTotal = res.data.samplingNumber
        this.chainRatio = 0
        if (+res.data.samplingChain) {
          this.chainRatio = Math.round(((res.data.samplingNumber - res.data.samplingChain) / res.data.samplingChain) * 100)
        }
      }).catch(res => {
        if (res.msg)
          this.$message.error(res.msg)
      })
    },
    loadDataOverView() {
      getOverviewOrg({
        timeType: this.curSelectedDate,
        unitName: this.curSelectedOrganization.enterpriseName
      }).then(res => {
        this.orgOverview = res.data
        if (!this.orgOverview) return;
        if (this.orgOverview.passNumber === 0 || this.orgOverview.doneNumber === 0) {
          this.completionRate = '0.00%'
        } else {
          this.completionRate = ((this.orgOverview?.passNumber / this.orgOverview?.doneNumber) * 100).toFixed(2) + '%'
        }
      }).catch(res => {
        if (res.msg)
          this.$message.error(res.msg)
      })
    },
    loadDataChart() {
      getChartOrg({
        orgId: this.org.orgId,
        unitName: this.curSelectedOrganization.enterpriseName
      }).then(res => {
        this.chartData = res.data
        this.initChart()
      }).catch(res => {
        if (res.msg)
          this.$message.error(res.msg)
      })
    },
    getSelectedDate(val) {
      if (val < 13) {
        if (new Date().getMonth() !== (val - 1)) {
          return val + '月'
        } else {
          return '本月'
        }
      } else if (val === 13) return '今日'
      else if (val === 14) return '本周'
      return ''
    },
    initChart() {
      if (this.chartData) {
        this.echartsDomObject = echarts.init(document.getElementById('echartsBar'));
        const list = [];
        this.chartData.map(item => list.push(item.samplingNumber))
        this.echartsDomObject.setOption({
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(50,50,50,0.7)',
            textStyle: {color: '#fff'},
            borderWidth: 0,
            formatter: function (params) {
              return '<div><div>时间：' + params[0].name + '月</div><div>批次数量：' + params[0].value + '</div></div>'
            },
          },
          xAxis: {
            type: 'category',
            data: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
            axisLine: {lineStyle: {color: '#FFFFFF'}},
            axisTick: {show: false}
          },
          yAxis: {
            type: 'value',
            axisLine: {lineStyle: {color: '#FFFFFF'}},
            splitLine: {lineStyle: {type: 'dashed', color: '#FFFFFF66'}, show: true},
          },
          grid: {
            left: '13%',
            top: '10%',
            right: '5%',
            bottom: '15%',
            containLabel: false,
          },
          series: [
            {
              data: list,
              type: 'bar',
              barWidth: 6,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{offset: 0, color: '#385B99'},
                    {offset: 1, color: '#81D7FC'}], false),
                },
              },
            },
          ],
        });
      }
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
</style>
