<template>
  <div class="grid grid-rows-[124px_196px_186px_1fr] gap-4">
    <!--抽样人员信息-->
    <div class="w-[378px] font-[SourceHanSansCNMedium] text-white">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/sample-plan-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">抽样计划</span>
        </div>
      </h1>

      <div class="w-[378px] h-[78px] bg-[rgba(8,24,48,0.4)] rounded flex flex-row flex-nowrap items-center px-4 gap-4">
        <div>
          <img src="@/assets/images/sample-plan-img.webp" alt="" class="max-w-fit"/>
        </div>
        <div class="w-full text-sm">
          <p class="w-[280px] mb-2 truncate">
            {{ curSelectedPlanDetails?.planName }}
          </p>
          <p class="w-[280px] truncate">
            {{ curSelectedPlanDetails?.planUnit }}
          </p>
        </div>
      </div>
    </div>

    <!--抽样数量-->
    <div class="w-[378px] text-white font-[SourceHanSansCNMedium]">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/sampling-total-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">{{ getSelectedDate(curSelectedDate) }}抽样计划数量</span>
        </div>
      </h1>

      <div
          class="w-full h-[150px] bg-[url('../assets/images/sample-plan-today-number-bg.webp')] bg-cover rounded flex flex-row flex-nowrap justify-center items-center text-[26px] font-[OswaldRegular]">
        <l-scroll-number :num="curSelectedPlan?.currentBatch"/>
      </div>
    </div>

    <!--计划概况-->
    <div class="w-[378px] text-white font-[SourceHanSansCNMedium]">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/link-ratio-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">计划概况</span>
        </div>
      </h1>
      <div class="w-full h-[70px] bg-[rgba(8,24,48,0.4)] rounded flex flex-row flex-nowrap items-center pl-4 gap-4">
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div>
              <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
                已计划任务/批
              </div>
              <div class="text-[26px] font-[OswaldRegular] ">
                <l-scroll-number :num="planOverview?planOverview.planCount:0"/>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
              已完成任务/批
            </div>
            <div class="text-[26px] font-[OswaldRegular] ">
              <l-scroll-number :num="planOverview?planOverview.doneCount:0"/>
            </div>
          </div>
        </div>
      </div>
      <div class="w-full h-[70px] bg-[rgba(8,24,48,0.4)] rounded flex flex-row flex-nowrap items-center pl-4 gap-4">
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-green-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
              完成率
            </div>
            <div class="text-[26px] font-[OswaldRegular] ">
              {{ completionRate }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--年度抽样趋势-->
    <div class="w-[378px] text-white font-[SourceHanSansCNMedium]">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/sample-trend-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">年度抽样趋势</span>
        </div>
      </h1>

      <div
          class="w-full rounded bg-[rgba(8,24,48,0.4)]"
          :style="'height:'+barAreaHeight+'px'">
        <div class="flex flex-row flex-nowrap justify-between items-center px-3 pt-3">
          <span>批</span>
          <div class="flex items-center">
            <span class="inline-block w-3 h-3 bg-[#81D7FCFF] mr-[8px]"></span>
            <span>月度抽样</span>
          </div>
        </div>
        <div id="echartsBar" class="w-full h-full"></div>
      </div>
    </div>
  </div>
</template>

<script>
import {getChartPlan, getDetailPlan, getOverviewPlan} from "@/api/modules/large/sample";
import LScrollNumber from "@/large/components/LScrollNumber/index.vue";
import echarts from "@/components/Echart/echarts";
import Cookies from "js-cookie";

export default {
  name: 'RightSamplePlan',
  components: {LScrollNumber},
  props: {
    curSelectedDate: {type: Number},
    curSelectedPlan: {type: Object}
  },
  data() {
    return {
      curSelectedPlanDetails: {},
      planOverview: {},
      completionRate: '-',
      barAreaHeight: window.innerHeight - 750,
      chartData: [],//当前选择用户的echarts对象
      echartsDomObject: null,
      org: localStorage.getItem('stb-orgInfo') ? JSON.parse(localStorage.getItem('stb-orgInfo')) : {},
    }
  },
  mounted() {
    this.loadData()
    window.addEventListener('resize', this.getBarAreaHeight, false);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getBarAreaHeight)
  },
  methods: {
    getBarAreaHeight() {
      this.barAreaHeight = window.innerHeight - 750
    },
    loadData() {
      if (this.curSelectedPlan && this.curSelectedPlan.planName) {
        this.loadDataDetail()
        this.loadDataOverView()
        this.loadDataChart()
      }
    },
    loadDataDetail() {
      getDetailPlan({
        planName: this.curSelectedPlan.planName,
        timeType: this.curSelectedDate
      }).then(res => {
        this.curSelectedPlanDetails = res.data
      }).catch(res => {
        if (res.msg)
        this.$message.error(res.msg)
      })
    },
    loadDataOverView() {
      getOverviewPlan({
        planName: this.curSelectedPlan.planName,
        orgId: this.org.orgId
      }).then(res => {
        this.planOverview = res.data
        if (!this.planOverview) return;
        if (+this.planOverview.doneCount === 0 || +this.planOverview.planCount === 0) {
          this.completionRate = '0.00%'
        } else {
          this.completionRate = ((this.planOverview?.doneCount / this.planOverview?.planCount) * 100).toFixed(2) + '%'
        }
      }).catch(res => {
        if (res.msg)
        this.$message.error(res.msg)
      })
    },
    loadDataChart() {
      getChartPlan({
        planName: this.curSelectedPlan.planName,
        orgId: this.org.orgId
      }).then(res => {
        this.chartData = res.data
        this.initChart()
      }).catch(res => {
        if (res.msg)
        this.$message.error(res.msg)
      })
    },
    getSelectedDate(val) {
      if (val < 13) {
        if (new Date().getMonth() !== (val - 1)) {
          return val + '月'
        } else {
          return '本月'
        }
      } else if (val === 13) return '今日'
      else if (val === 14) return '本周'
      return ''
    },
    initChart() {
      if (this.chartData) {
        this.echartsDomObject = echarts.init(document.getElementById('echartsBar'));
        const list = [];
        this.chartData.map(item => list.push(item.samplingNumber))
        this.echartsDomObject.setOption({
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(50,50,50,0.7)',
            textStyle: {color: '#fff'},
            borderWidth: 0,
            formatter: function (params) {
              return '<div><div>时间：' + params[0].name + '月</div><div>批次数量：' + params[0].value + '</div></div>'
            },
          },
          xAxis: {
            type: 'category',
            data: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
            axisLine: {lineStyle: {color: '#FFFFFF'}},
            axisTick: {show: false}
          },
          yAxis: {
            type: 'value',
            axisLine: {lineStyle: {color: '#FFFFFF'}},
            splitLine: {lineStyle: {type: 'dashed', color: '#FFFFFF66'}, show: true},
          },
          grid: {
            left: '13%',
            top: '10%',
            right: '5%',
            bottom: '15%',
            containLabel: false,
          },
          series: [
            {
              data: list,
              type: 'bar',
              barWidth: 6,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{offset: 0, color: '#385B99'},
                    {offset: 1, color: '#81D7FC'}], false),
                },
              },
            },
          ],
        });
      }
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
</style>
