<template>
  <el-dialog
      :visible='dialogVisible'
      width='80%'
      :close-on-click-modal="false"
      destroy-on-close
      @close='close'>
    <div class="w-full h-[75vh] rounded relative">
      <div id="mapcontainer_lj" class="map"/>
      <div class="w-full h-[45px] absolute top-0 left-0 bg-[rgba(0,0,0,.01)] p-2">
        <el-date-picker v-model="date" type="date" placeholder="选择日期" :disabledDate="disabledDate"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="handleDateChange()"/>
      </div>
      <div v-if="listData.length"
           class="absolute top-1/2 right-4 text-white transform -translate-y-1/2 w-[400px] h-[90%] p-3 bg-[rgba(0,0,0,0.6)] rounded shadow-md">
        <swiper ref="swiper" :options="swiperOption" class="overflow-hidden w-full h-full">
          <swiper-slide v-for="(item,index) in listData" class="!w-full !my-0" :key="index">
            <div class="w-full flex flex-row flex-nowrap ga-2">
              <div class="w-[30px]">{{ index + 1 }}</div>
              <div class="w-[150px]">{{ item.createTime }}</div>
              <div class="flex-1">{{ item.place }}</div>
            </div>
          </swiper-slide>
        </swiper>
      </div>

      <span class="absolute w-[20px] h-[20px] -top-[10px] -right-[10px]" @click="close">
        <l-btn-close style="font-size: 20px;cursor: pointer"/>
      </span>
    </div>
  </el-dialog>
</template>

<script>

import LBtnClose from "@/large/components/LBtnClose/index.vue";
import dayjs, {Dayjs} from 'dayjs';
import 'dayjs/locale/zh-cn';
import {Swiper as SwiperClass, Pagination, Mousewheel, Autoplay} from 'swiper/js/swiper.esm'
import getAwesomeSwiper from 'vue-awesome-swiper/dist/exporter'

SwiperClass.use([Pagination, Mousewheel, Autoplay])
const {Swiper, SwiperSlide} = getAwesomeSwiper(SwiperClass)
import 'swiper/css/swiper.css'
import * as AMapLoader from "@amap/amap-jsapi-loader";
import {AMAP_DEVELOPMENT_KEY} from "@/config";
import {getTrajectory} from "@/api/modules/large/sample";
import Cookies from "js-cookie";

export default {
  name: 'Trajectory',
  components: {LBtnClose, Swiper, SwiperSlide},
  props: {
    curSelectedDate: {type: Number},
  },
  data() {
    return {
      dialogVisible: false,
      selectedUser: {},
      loading: false,
      listData: [],
      date: dayjs().format('YYYY-MM-DD'),
      mapInstanceT: '',//map 地图实例
      AMapInstanceT: '',//AMap 实例对象，用于生成地图
      AMapUIInstanceT: '',//AMap UI 实例对象
      pathSimplifierIns: '',
      swiperOption: {
        direction: 'vertical',
        loop: true,
        slidesPerView: 10,
        autoplay: {
          delay: 3000, // 每个幻灯片之间的延迟时间（以毫秒为单位）
          disableOnInteraction: false // 用户交互时是否停止自动播放
        },
        speed: 5000,
        reverseDirection: true
      },
      org: localStorage.getItem('stb-orgInfo') ? JSON.parse(localStorage.getItem('stb-orgInfo')) : {},
      lat: +Cookies.get('lat'),
      lng: +Cookies.get('lng')
    }
  },
  mounted() {
  },
  methods: {
    open(data) {
      this.selectedUser = JSON.parse(JSON.stringify(data))
      this.dialogVisible = true
      this.initMap()
      this.loadData()
    },
    close() {
      this.$emit('close')
      this.dialogVisible = false
      this.selectedUser = {}
      this.mapInstance = null
      this.date = dayjs().format('YYYY-MM-DD')
    },
    loadData() {
      let data = {
        userId: this.selectedUser.userId,
        timeType: this.curSelectedDate,
      }
      if (this.date) {
        data.uploadDate = this.date
      }
      this.loading = true
      this.listData = []
      getTrajectory(data).then(res => {
        this.swiperOption.loop = res.data.trajectoryVOS.length > 10
        this.listData = res.data.trajectoryVOS

        if (this.mapInstanceT) {
          // 清除所有覆盖物
          this.mapInstanceT.clearMap();
          if (this.pathSimplifierIns) {
            // 清除之前渲染的轨迹
            this.pathSimplifierIns.setData(null);
            // 重新渲染轨迹
            this.drawPath();
            // 绘制起点和终点
            this.drawMarker();
          }
        }
      }).catch(res => {
        if (res.msg)
        this.$message.error(res.msg)
      }).finally(() => {
        this.loading = false
      })
    },
    initMap() {
      AMapLoader.load({
        key: AMAP_DEVELOPMENT_KEY, // 需要设置您申请的key
        version: '2.0',
        plugins: ['AMap.ToolBar', 'AMap.Driving', 'AMap.DistrictSearch'],
        AMapUI: {version: '1.1', plugins: [],},
        Loca: {version: '2.0.0',},
      }).then((AMap) => {
        this.AMapInstanceT = AMap
        const _mapInstanceT = new AMap.Map('mapcontainer_lj', {
          zoom: 8,
          // rotation: -15,
          center: [this.lng, this.lat]
        });
        this.mapInstanceT = _mapInstanceT
        this.AMapUIInstanceT = AMapUI
        this.initPathSimplifier()
      }).catch((e) => {
        console.log(e);
      });
    },
    initPathSimplifier() {
      if (!this.AMapUIInstanceT) return
      let that = this
      this.AMapUIInstanceT.load(
          ['ui/misc/PathSimplifier', 'lib/$'],
          function (PathSimplifier, _$) {
            if (!PathSimplifier.supportCanvas) {
              alert('当前环境不支持 Canvas!');
              return;
            }
            const _pathSimplifierIns = new PathSimplifier({
              zIndex: 100,
              map: that.mapInstanceT, //所属的地图实例
              getPath: function (pathData, _pathIndex) {
                return pathData.path;
              },
              renderOptions: {
                renderAllPointsIfNumberBelow: -1, //绘制路线节点，如不需要可设置为-1
                // 轨迹线的样式
                pathLineStyle: {
                  strokeStyle: 'green',
                  lineWidth: 8,
                  dirArrowStyle: true,
                },
              },
              // 鼠标悬停时显示的信息
              getHoverTitle: function (pathData, pathIndex, pointIndex) {
                // TODO
              },
            });

            that.pathSimplifierIns = _pathSimplifierIns
            that.loadData()
          }
      );
    },
    drawPath() {
      if (!this.listData?.length || !this.AMapUIInstanceT) return;
      const _path = [];
      // 过滤数据
      this.listData?.length &&
      this.listData?.forEach(_item => {
        _path?.push([parseFloat(_item.longitude), parseFloat(_item.latitude),]);
      })

      //设置数据
      if (_path?.length) {
        this.pathSimplifierIns.setData([{name: '路线0', path: _path,}]);
      } else {
        this.pathSimplifierIns.setData(null);
      }
    },
    drawMarker() {
      if (!this.listData?.length || !this.AMapUIInstanceT) return;
      new this.AMapInstanceT.Marker({
        map: this.mapInstanceT,
        content: `<div class="w-[35px] h-[50px] text-white text-center pt-1 bg-gradient-to-b from-[#81D7FC] to-[rgba(58,130,255,0)] rounded font-SourceHanSansCNBold">
                    起点
                  </div>`,
        position: [parseFloat(this.listData[0]?.longitude), parseFloat(this.listData[0]?.latitude)],
        offset: new this.AMapInstanceT.Pixel(-18, -50),
      })
      new this.AMapInstanceT.Marker({
        map: this.mapInstanceT,
        content: `<div class="w-[35px] h-[50px] text-white text-center pt-1 bg-gradient-to-b from-[#F18529] to-[rgba(241,133,41,0)] rounded font-SourceHanSansCNBold">
                    终点
                  </div>`,
        position: [
          parseFloat(this.listData[this.listData.length - 1]?.longitude),
          parseFloat(this.listData[this.listData.length - 1]?.latitude),],
        offset: new this.AMapInstanceT.Pixel(-18, -50)
      })
    },
    disabledDate(current) {
      return current && current > dayjs().endOf('day');
    },
    handleDateChange() {
      this.loadData()
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang='scss' scoped>
.map {
  width: 100%;
  height: 75vh
}

::v-deep {
  .el-dialog {
    margin: 13vh auto 0 !important;
  }

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
    position: relative;
  }
}
</style>
