<template>
  <div class="w-[378px] h-full grid grid-rows-[32px_1fr] gap-1 font-[SourceHanSansCNMedium] text-white">
    <h1 class="w-[378px] flex flex-row flex-nowrap items-center bg-[url('../assets/images/item-title-bg.webp')] bg-cover bg-full pl-[23px]">
      <div class="flex flex-row flex-nowrap items-center gap-1">
        <img src="@/assets/images/customer-sample-sort-icon.webp" alt="" class="w-[26px] h-[26px]"/>
        <span class="text-lg font-[SourceHanSansCNBold]">细类排行</span>
      </div>
    </h1>

    <div v-if="loading" class="w-full bg-[rgba(8,24,48,0.52)]">
      <l-loading/>
    </div>
    <div v-else
         class="w-full bg-[rgba(8,24,48,0.52)]"
         :style="'height:'+scrollAreaHeight+'px'">
      <swiper ref="swiper" :options="swiperOption" class="overflow-hidden w-full h-full" @tap="onSlideChange">
        <swiper-slide v-for="(item,index) in listData" class="!w-full !my-0" :key="index" :data-real-index="index">
          <div class="w-[378px] !h-[48px] cursor-pointer flex items-center px-2"
               :class="isChecked(item)?'border-[2px] border-solid border-white rounded':''">
            <div class="w-full">
              <div v-if="item.name" class="flex flex-row flex-nowrap justify-between items-center">
                <div class="w-[300px] flex flex-row flex-nowrap items-center truncate gap-1">
                  <div
                      class="w-[18px] h-[18px] flex justify-center items-center border border-solid"
                      :class="index===0?'border-[#FFD86D] text-[#FFD86D]'
                              :index===1?'border-[#52CC92] text-[#52CC92]'
                              :index===2?'border-[#81D7FC] text-[#81D7FC]'
                              :'border-white text-white'">
                    {{ index + 1 }}
                  </div>
                  <div class="w-[278px] truncate">
                    {{ item.name }}
                  </div>
                </div>
                <div
                    class="flex-1 text-right"
                    :class="index===0?'text-[#FFD86D]'
                              :index===1?'text-[#52CC92]'
                              :index===2?'text-[#81D7FC]'
                              :'border-white text-white'">
                  {{ item.number }}批
                </div>
              </div>
              <el-progress v-if="item.name" class="h-[8px] mt-[8px]"
                           :percentage="Math.ceil((item.number / percentBase) * 100)"
                           color="linear-gradient(to right, rgb(56, 91, 153) 0%, rgb(129, 215, 252) 100%)"
                           :show-text="false" define-back-color="#FFFFFF66"/>
            </div>
          </div>
        </swiper-slide>
      </swiper>
    </div>
  </div>
</template>

<script>

import LLoading from "@/large/components/LLoading/index.vue";
import {Swiper as SwiperClass, Pagination, Mousewheel, Autoplay} from 'swiper/js/swiper.esm'
import getAwesomeSwiper from 'vue-awesome-swiper/dist/exporter'

SwiperClass.use([Pagination, Mousewheel, Autoplay])
const {Swiper, SwiperSlide} = getAwesomeSwiper(SwiperClass)
import 'swiper/css/swiper.css'

import {pageSubClassRank} from "@/api/modules/large/sample";
import Cookies from "js-cookie";

export default {
  name: 'LeftSampleNum',
  components: {LLoading, Swiper, SwiperSlide},
  props: {
    curSelectedDate: {type: Number}
  },
  data() {
    return {
      loading: true,
      isSearch: false,
      scrollAreaHeight: window.innerHeight - 370,
      listData: [],
      swiperOption: {
        direction: 'vertical',
        spaceBetween: 0,
        loop: true,
        slidesPerView: 12,
        autoplay: {
          delay: 3000, // 每个幻灯片之间的延迟时间（以毫秒为单位）
          disableOnInteraction: false // 用户交互时是否停止自动播放
        },
        speed: 5000,
        reverseDirection: true,
      },
      percentBase: 100,
      org: localStorage.getItem('stb-orgInfo') ? JSON.parse(localStorage.getItem('stb-orgInfo')) : {},
      interval: null,
    }
  },
  computed: {
    isPlay() {
      return (this.$store.getters.showModel && (this.$store.getters.model === 1))
    }
  },
  watch: {
    isPlay() {
      this.doPlay()
    }
  },
  mounted() {
    this.loadData()
    window.addEventListener('resize', this.calculateScrollAreaHeight, false);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateScrollAreaHeight)
    this.clearPlay()
  },
  methods: {
    calculateScrollAreaHeight() {
      this.scrollAreaHeight = window.innerHeight - 370
      this.swiperOption.slidesPerView = Math.floor((window.innerHeight - 370) / 48)
      this.loading = true
      setTimeout(() => {
        this.loading = false
      })
    },
    loadData() {
      this.loading = true
      pageSubClassRank({
        timeType: this.curSelectedDate,
        orgId: this.org.orgId,
        currentPage: 1,
        pageSize: 999,
      }).then(res => {
        if (res.data.rows.length) {
          this.listData = res.data.rows
          this.swiperOption.loop = res.data.rows.length >= this.swiperOption.slidesPerView
          this.setCurSelectedcat4(res.data.rows[0])
          this.percentBase = Math.ceil(res.data.rows[0]?.number * 1.1)
          this.doPlay()
        } else {
          this.listData = []
          this.swiperOption.loop = false
          this.setCurSelectedcat4(null)
          this.percentBase = 100
        }
      }).catch(res => {
        if (res.msg)
          this.$message.error(res.msg)
      }).finally(() => {
        this.loading = false
      })
    },
    loadDataSocket(socketData) {
      this.loading = true
      setTimeout(() => {
        if (socketData.subclassMonitor && socketData.subclassMonitor.rows && socketData.subclassMonitor.rows.length) {
          this.listData = socketData.subclassMonitor.rows
          this.swiperOption.loop = this.listData.length >= this.swiperOption.slidesPerView
          this.setCurSelectedcat4(this.listData[0])
          this.percentBase = Math.ceil(this.listData[0]?.number * 1.1)
          this.doPlay()
        } else {
          this.listData = []
          this.swiperOption.loop = false
          this.setCurSelectedcat4(null)
          this.percentBase = 100
        }
        this.loading = false
      }, 100)
    },
    isChecked(item) {
      const cur = this.$parent.curSelectedcat4
      return cur && item.name === cur.name
    },
    setCurSelectedcat4(val) {
      console.log("这是点击选中", val);
      this.$emit('setCurSelectedcat4', val)
    },
    doPlay() {
      this.clearPlay()
      if (this.isPlay) {
        this.startPlay()
      }
    },
    startPlay() {
      let i = 0
      this.interval = setInterval(() => {
        this.setCurSelectedcat4(this.listData[i++ % this.listData.length]);
      }, 5000);
    },
    clearPlay() {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = null
      }
    },
    // swiper点击事件
    onSlideChange(swiper) {
      // 向上冒泡查找 class 为 swiper-slide 的元素
      const clickedSlide = swiper.target.closest('.swiper-slide');
      // console.log("元素", clickedSlide);
      if (clickedSlide) {
        const realIndex = clickedSlide.getAttribute('data-real-index');
        if (realIndex !== null) {
          const idx = parseInt(realIndex, 10);
          this.setCurSelectedcat4(this.listData[idx]);
        }
        // console.log("索引", realIndex);
      }
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
::v-deep {
  .el-progress-bar__inner {
    background-image: linear-gradient(to right, rgb(56, 91, 153) 0%, rgb(129, 215, 252) 100%);
  }

  .el-progress-bar__outer {
    height: 8px;
  }
}
</style>
