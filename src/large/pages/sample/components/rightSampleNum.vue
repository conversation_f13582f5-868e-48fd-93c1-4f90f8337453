<template>
  <div class="grid grid-row-[317px_186px_1fr] gap-4">
    <!--抽样人员执行-->
    <div class="w-[378px] font-[SourceHanSansCNMedium] text-white">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')] bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/sample-person-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">抽样人员执行</span>
        </div>
      </h1>
      <div class="w-full h-[197px] bg-[rgba(8,24,48,0.4)] mb-2 pb-2 rounded">
        <swiper v-if="listDataPerson.length && listDataPerson.some(item => item.samplingBatch > 0)" :options="swiperOptionPerson" class="overflow-hidden w-full h-full">
          <swiper-slide v-for="(item,index) in listDataPerson" :key="index">
            <div class="w-full h-full text-center flex items-end">
              <div class="w-full h-full">
                <div class="w-full h-full flex flex-row flex-nowrap justify-center items-end gap-1">
                  <div class="w-[20px] [writing-mode:vertical-lr] pb-[10px] text-sm ">
                    {{ item.name }}
                  </div>
                  <div class="flex-1 flex flex-col h-full justify-end gap-2">
                    <div class="w-[30px] h-[30px] ml-[5px] text-[#81D7FC] text-left">{{ item.samplingBatch }}批</div>
                    <div class="w-[6px] "
                      :style="{height:+mapNumFrom30To90(item.samplingBatch,0,baseNumPerson)+'%', backgroundImage: 'linear-gradient(180deg,#81D7FCFF,#385B99FF)'}"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="w-full h-[66px] bg-[rgba(8,24,48,0.4)] rounded py-2">
        <swiper v-if="listDataPlace.length" :options="swiperOptionPlace" class="overflow-hidden w-full h-full">
          <swiper-slide v-for="(item,index) in listDataPlace" :key="index">
            <div class="flex flex-row flex-nowrap mt-[6px]">
              <div class="w-[2px] h-[46px] mr-[4px]">
                <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
              </div>
              <div class="truncate">
                <div class="flex-1 text-[13px] text-[#FFFFFFB2] mr-[2px]">
                  {{ item.place }}
                </div>
                <div class="text-[26px]">{{ item.batch }}</div>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>

    <!--年度抽样概况-->
    <div class="w-[378px] text-white font-[SourceHanSansCNMedium]">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')] bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/link-ratio-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">年度抽样概况</span>
        </div>
      </h1>

      <div class="w-full h-[140px] bg-[#08183066] rounded p-4">
        <div class="flex flex-row flex-nowrap items-center mb-4">
          <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
            <div class="w-[2px] h-[46px]">
              <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
            </div>
            <div>
              <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
                被抽样单位/家
              </div>
              <div class="text-[26px] font-[OswaldRegular]">
                <l-scroll-number :num="listDataYear.number"/>
              </div>
            </div>
          </div>
          <div class="flex-1  flex flex-row flex-nowrap items-center gap-2">
            <div class="w-[2px] h-[46px]">
              <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
            </div>
            <div>
              <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
                抽样细类品种/种
              </div>
              <div class="text-[26px] font-[OswaldRegular]">
                <l-scroll-number :num="listDataYear.countSub"/>
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-row flex-nowrap items-center mb-4">
          <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
            <div class="w-[2px] h-[46px]">
              <img src="@/assets/images/dash-yellow-gap-icon.webp" alt="" class="w-full h-full"/>
            </div>
            <div>
              <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
                样品采购金额/元
              </div>
              <div class="text-[26px] font-[OswaldRegular] text-[#FFD86DFF]">
                <l-scroll-number :num="listDataYear.price"/>
              </div>
            </div>
          </div>
          <div class="flex-1  flex flex-row flex-nowrap items-center gap-2">
            <div class="w-[2px] h-[46px]">
              <img src="@/assets/images/dash-green-gap-icon.webp" alt="" class="w-full h-full"/>
            </div>
            <div>
              <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
                合格率
              </div>
              <div class="text-[26px] font-[OswaldRegular] text-[#52CC92FF]">
                {{ passRate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--抽样计划进度-->
    <div class="w-[378px] text-white font-[SourceHanSansCNMedium]">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')] bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/sample-plan-progress-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">抽样计划进度</span>
        </div>
      </h1>
      <div class="w-full bg-[#08183066] rounded">
        <div
            class="w-full h-[42px] flex flex-row flex-nowrap items-center text-[13px] text-[#FFFFFFB2] text-center bg-[rgba(8,24,48,0.4)]">
          <div class="w-[46px]">排名</div>
          <div class="flex-1 text-left pl-[10px]">抽样计划</div>
          <div class="w-[59px]">完成率</div>
          <div class="w-[65px]">任务数</div>
        </div>
        <div v-if="showPlan&&listDataPlan.length" class="w-full" :style="'height:'+scrollAreaHeight+'px'">
          <swiper :data="listDataPlan" :options="swiperOptionPlan" class="overflow-hidden w-full h-full">
            <swiper-slide v-for="(item,index) in listDataPlan" class="!w-full !my-0" :key="index">
              <div class="w-full h-[60px] flex flex-row flex-nowrap items-center text-center"
                   :class="(index % 2 === 0 )?'bg-[rgba(8,24,48,0.5216)]':'bg-[rgba(8,24,48,0.4)]'">
                <div class="w-[46px] flex justify-center items-center">
                  <div
                      class="w-[18px] h-[18px] flex justify-center items-center border border-solid"
                      :class="index===0?'border-[#FFD86D] text-[#FFD86D]'
                              :index===1?'border-[#52CC92] text-[#52CC92]'
                              :index===2?'border-[#81D7FC] text-[#81D7FC]'
                              :'border-white text-white'">
                    {{ index + 1 }}
                  </div>
                </div>
                <div class="flex-1 text-left pl-[10px]">{{ item.planName }}</div>
                <div class="w-[59px] text-[#52CC92FF]">
                  {{ item.number !== 0 ? (((item.doneNumber / item.number) * 100).toFixed(2) + '%') : '0.00%' }}
                </div>
                <div class="w-[65px]">{{ item.number }}</div>
              </div>
            </swiper-slide>
          </swiper>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import {Swiper as SwiperClass, Pagination, Mousewheel, Autoplay} from 'swiper/js/swiper.esm'
import getAwesomeSwiper from 'vue-awesome-swiper/dist/exporter'

SwiperClass.use([Pagination, Mousewheel, Autoplay])
const {Swiper, SwiperSlide} = getAwesomeSwiper(SwiperClass)
import 'swiper/css/swiper.css'
import {listPersonNum, listPlaceNum, listPlanNum, listYearNum} from "@/api/modules/large/sample";
import LScrollNumber from "@/large/components/LScrollNumber/index.vue";
import Cookies from "js-cookie";

export default {
  name: 'RightSampleNum',
  components: {LScrollNumber, Swiper, SwiperSlide},
  props: {
    curSelectedDate: {type: Number}
  },
  data() {
    return {
      listDataPerson: [],
      swiperOptionPerson: {
        direction: 'horizontal',
        spaceBetween: 30,
        loop: true,
        slidesPerView: 6,
        autoplay: {
          delay: 3000, // 每个幻灯片之间的延迟时间（以毫秒为单位）
          disableOnInteraction: false // 用户交互时是否停止自动播放
        },
        speed: 5000,
        reverseDirection: true
      },
      baseNumPerson: 0,
      listDataPlace: [],
      swiperOptionPlace: {
        direction: 'horizontal',
        spaceBetween: 0,
        loop: true,
        slidesPerView: 3,
        autoplay: {
          delay: 3000, // 每个幻灯片之间的延迟时间（以毫秒为单位）
          disableOnInteraction: false // 用户交互时是否停止自动播放
        },
        speed: 5000,
        reverseDirection: true
      },
      listDataYear: {},
      passRate: '-',
      listDataPlan: [],
      swiperOptionPlan: {
        direction: 'vertical',
        spaceBetween: 0,
        loop: true,
        slidesPerView: 12,
        autoplay: {
          delay: 3000, // 每个幻灯片之间的延迟时间（以毫秒为单位）
          disableOnInteraction: false // 用户交互时是否停止自动播放
        },
        speed: 5000,
        reverseDirection: true
      },
      scrollAreaHeight: window.innerHeight - 720,
      showPlan: true,
      org: localStorage.getItem('stb-orgInfo') ? JSON.parse(localStorage.getItem('stb-orgInfo')) : {},
    }
  },
  mounted() {
    this.calculateScrollAreaHeight()
    this.loadData()
    window.addEventListener('resize', this.calculateScrollAreaHeight, false);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateScrollAreaHeight)
  },
  methods: {
    calculateScrollAreaHeight() {
      this.scrollAreaHeight = window.innerHeight - 720
      this.swiperOptionPlan.slidesPerView = Math.floor(this.scrollAreaHeight / 60)
      this.showPlan = false
      setTimeout(() => {
        this.showPlan = true
      })
    },
    loadData() {
      this.loadDataPerson()
      this.loadDataPlace()
      this.loadDataYear()
      this.loadDataPlan()
    },
    loadDataPerson() {
      this.listDataPerson = []
      listPersonNum({
        timeType: this.curSelectedDate,
        orgId: this.org.orgId
      }).then(res => {
        let tmp = 0
        res.data.map(item => {
          if (item.samplingBatch > tmp) tmp = item.samplingBatch
        })
        this.baseNumPerson = tmp
        this.swiperOptionPerson.loop = res.data.length >= 6
        this.listDataPerson = res.data
      }).catch(res => {
        if (res.msg)
        this.$message.error(res.msg)
      })
    },
    loadDataPlace() {
      this.listDataPlace = []
      listPlaceNum({
        timeType: this.curSelectedDate,
        orgId: this.org.orgId
      }).then(res => {
        this.swiperOptionPlace.loop = res.data.length >= 3
        this.listDataPlace = res.data
      }).catch(res => {
        if (res.msg)
        this.$message.error(res.msg)
      })
    },
    loadDataYear() {
      listYearNum({
        orgId: this.org.orgId
      }).then(res => {
        this.listDataYear = res.data
        if (+this.listDataYear.sampleNumber)
          this.passRate = Math.ceil(((this.listDataYear.sampleNumber - this.listDataYear.unqualified) / this.listDataYear.sampleNumber) * 100) + '%'
        else
          this.passRate = '-'
      }).catch(res => {
        if (res.msg)
        this.$message.error(res.msg)
      })
    },
    loadDataPlan() {
      listPlanNum({
        orgId: this.org.orgId,
        pageSize: 999,
        pageNum: 1,
      }).then(res => {
        this.swiperOptionPlan.loop = res.data.length >= this.swiperOptionPlan.slidesPerView
        this.listDataPlan = res.data
      }).catch(res => {
        if (res.msg)
        this.$message.error(res.msg)
      })
    },
    mapNumFrom30To90(num, minNum, maxNum) {
      num = Math.max(minNum, Math.min(num, maxNum));
      return Math.ceil(((num - minNum) / (maxNum - minNum)) * 60 + 30);
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
</style>
