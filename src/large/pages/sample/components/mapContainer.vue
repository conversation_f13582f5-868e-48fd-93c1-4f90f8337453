<template>
  <div class="home_div relative" id="home_div">
    <div id="mapcontainer" class="map" style="height: 100%"/>
    <div id="panel"></div>
    <trajectory v-if="show" ref="trajectory" :cur-selected-date="curSelectedDate" @close="show=false"></trajectory>
    <div v-if="loading" class="w-full h-full absolute top-0 right-0">
      <l-loading/>
    </div>
  </div>
</template>

<script>

import * as AMapLoader from "@amap/amap-jsapi-loader";
import {AMAP_DEVELOPMENT_KEY} from "@/config";
import {getOrgMap} from "@/api/modules/large/sample";

import personMapIconSelected from '@/assets/images/person-map-icon-selected.webp';
import personMapIcon from '@/assets/images/person-map-icon.webp';
import placeMapIconSelected from '@/assets/images/place-map-icon-selected.webp';
import placeMapIcon from '@/assets/images/place-map-icon.webp';
import placeMapGrayIcon from '@/assets/images/place-map-gray-icon.webp';
import Trajectory from "@/large/pages/sample/components/trajectory.vue";
import Cookies from "js-cookie";
import LLoading from "@/large/components/LLoading/index.vue";

export default {
  name: 'mapContainer',
  components: {Trajectory, LLoading},
  props: {
    curSelectedDate: {type: Number},
    curShowCategory: {type: String},
    curSelectedcat4: {type: Object},
    curSelectedUser: {type: Object},
    curSelectedOrganization: {type: Object},
    curSelectedPlan: {type: Object},
    listDataPerson: {type: Array},
    listDataOrg: {type: Array}
  },
  data() {
    return {
      mapInstance: '',//map 地图实例
      AMapInstance: '',//AMap 实例对象，用于生成地图
      AMapUIInstance: '',//AMap UI 实例对象
      listDataCate: [],
      listDataPlan: [],
      markers: [],
      isPlaceMarkerClick: false,
      isPersonMarkerClick: false,
      show: false,
      org: localStorage.getItem('stb-orgInfo') ? JSON.parse(localStorage.getItem('stb-orgInfo')) : {},
      lat: +Cookies.get('lat') || '39.906217',
      lng: +Cookies.get('lng') || '116.3912757',
      loading: false,
    }
  },
  mounted() {
    this.initMapInstance()
  },
  methods: {
    initMapInstance() {
      AMapLoader.load({
        key: AMAP_DEVELOPMENT_KEY, // 需要设置您申请的key
        version: '2.0',
        plugins: ['AMap.ToolBar', 'AMap.Driving', 'AMap.DistrictSearch'],
        AMapUI: {version: '1.1', plugins: []},
        Loca: {version: '2.0.0'}
      }).then((AMap) => {
        this.AMapInstance = AMap
        this.mapInstance = new AMap.Map('mapcontainer', {
          zoom: 8,
          // rotation: -15,
          center: [this.lng, this.lat]
        })
        // this.AMapUIInstance = AMapUI
        this.drawBoundary()
      }).catch((e) => {
        console.log(e);
      });
    },
    loadMarker() {
      if (this.curShowCategory === 'sampleNum') {
        this.loading = true
        getOrgMap({
          orgId: this.org.orgId,
          timeType: this.curSelectedDate,
          cateName: this.curSelectedcat4?.name
        }).then(res => {
          if (res.data.length) {
            this.listDataCate = res.data
            this.createOrgMarker(this.listDataCate)
          }
        }).catch(res => {
          if (res.msg)
          this.$message.error(res.msg)
        }).finally(() => {
          this.loading = false
        })
      } else if (this.curShowCategory === 'samplePerson') {
        this.createPersonMarker()
      } else if (this.curShowCategory === 'sampleOrganization') {
        this.createOrgMarker(this.listDataOrg)
      } else if (this.curShowCategory === 'samplePlan') {
        this.loading = true
        getOrgMap({
          orgId: this.org.orgId,
          timeType: this.curSelectedDate,
          reportClassb: this.curSelectedPlan?.planName,
        }).then(res => {
          if (res.data.length) {
            this.listDataPlan = res.data
            this.createOrgMarker(this.listDataPlan)
          }
        }).catch(res => {
          if (res.msg)
          this.$message.error(res.msg)
        }).finally(() => {
          this.loading = false
        })
      }
    },
    createOrgMarker(listOrg) {
      if (!this.AMapInstance) {
        console.error(`Error: 地图实例未初始化`);
        return;
      }
      console.log("这是机构数据", listOrg);

      // mapInstance.clearMap();
      // mapInstance.remove(this.markers)
      this.markers.length &&
      this.markers.map((_marker) => _marker.setMap(null));
      if (!listOrg?.length) return;
      const maxNum = listOrg.length > 400 ? 400: listOrg.length
      for (let i = 0, marker; i < maxNum; i++) {
        // console.log('index->', i)
        // console.log((listOrg[i].longitude && listOrg[i].latitude))
        if (listOrg[i].longitude && listOrg[i].latitude) {
          const imgUrl = listOrg[i].type == 0 || !listOrg[i].type ? placeMapIcon : placeMapGrayIcon;
          
          marker = new this.AMapInstance.Marker({
            map: this.mapInstance,
            content: `<div class="place-marker" title="${listOrg[i]?.unitName || listOrg[i]?.enterpriseName}">
                          <img src="${imgUrl}" alt="" />
                          <span>${listOrg[i]?.samplingBatch}</span>
                    </div>`,
            position: [
              parseFloat(listOrg[i].longitude),
              parseFloat(listOrg[i].latitude),
            ],
            offset: new this.AMapInstance.Pixel(-21, -70),
            title: JSON.stringify(listOrg[i]),
            extData: {
                type: 0,
                // type: listOrg[i].type,
            }
          });
          // if (listOrg[i].type == 0) {
            
            marker.on('click', (event) => {
              console.log('event',event)
              console.log('listOrg[i].type',listOrg[i].type)
              
              this.isPlaceMarkerClick = true;
              // 查找已激活的点，如果找到则该节点到默认状态
              this.markers.forEach((_marker) => {
                // console.log('marker点击事件',  _marker.getExtData().type)
                if (_marker.getContent().includes('place-marker__selected')) {
                  // 修复点击下一个标记时 上一个标记的samplingBatch和title会变成点击标记的数据的问题
                  // 可使用正则处理
                  _marker.setContent(`<div class="place-marker" title="${
                      JSON.parse(_marker.getTitle())?.enterpriseName ||
                      JSON.parse(_marker.getTitle())?.unitName
                  }">
                            <img src="${imgUrl}" alt="" />
                            <span>${
                      JSON.parse(_marker.getTitle())?.samplingBatch
                  }</span>
                          </div>`);
                  _marker.setPosition([
                    parseFloat(_marker?.getPosition()?.lng),
                    parseFloat(_marker?.getPosition()?.lat),
                  ]);
                  _marker.setOffset(new this.AMapInstance.Pixel(-21, -70));
                }
              });
              // 如果当前为默认状态，设置为激活状态, 否则恢复默认状态
              const _target = event.target;
              if (_target.getContent().includes('place-marker__selected')) {
                _target.setContent(
                    `<div class="place-marker" title="${listOrg[i]?.unitName || listOrg[i]?.enterpriseName}">
                                  <img src="${imgUrl}" alt="" />
                                  <span>${listOrg[i]?.samplingBatch}</span>
                                </div>`
                );
                _target.setPosition([
                  parseFloat(listOrg[i].longitude),
                  parseFloat(listOrg[i].latitude),
                ]);
                _target.setOffset(new this.AMapInstance.Pixel(-21, -70));
              } else {
                _target.setContent(
                    `<div class="place-marker__selected" title="${
                        listOrg[i]?.unitName || listOrg[i]?.enterpriseName
                    }">
                            <img src="${placeMapIconSelected}" alt="" />
                            <span>${listOrg[i]?.samplingBatch}</span>
                          </div>`
                );
                _target.setPosition([
                  parseFloat(listOrg[i].longitude),
                  parseFloat(listOrg[i].latitude),
                ]);
                _target.setOffset(new this.AMapInstance.Pixel(-45, -70));
              }
              // 设置当前选择的被抽样单位
              this.setCurSelectedOrganization(listOrg[i]);
            });
          // }

          this.markers.push(marker);
        }
      }
      console.log('markers--->', this.markers.length)
    },
    createPersonMarker() {
      if (!this.AMapInstance) {
        console.error(`Error: 地图实例未初始化`);
        return;
      }

      if (!this.listDataPerson?.length) return;

      this.markers.length &&
      this.markers.map((_marker) => _marker.setMap(null));

      for (let i = 0, marker; i < this.listDataPerson.length; i++) {
        if (this.listDataPerson[i].longitude && this.listDataPerson[i].latitude) {
          // 如果当前渲染的人员节点时当前选中的节点
          // const imgUrl = this.listDataPerson[i].type == 0 ? placeMapIcon : placeMapGrayIcon;

          const _content =
              this.curSelectedUser?.userId === this.listDataPerson[i]?.userId
                  ? `<div class="person-marker__selected" title="${this.listDataPerson[i]?.userName}">
              <img src="${personMapIcon}" alt="" />
              <span>${this.listDataPerson[i]?.doneBatch}</span>
            </div>`
                  : `<div class="person-marker" title="${this.listDataPerson[i]?.userName}">
              <img src="${personMapIcon}" alt="" />
              <span>${this.listDataPerson[i]?.doneBatch}</span>
            </div>`;

          marker = new this.AMapInstance.Marker({
            map: this.mapInstance,
            content: _content,
            position: [
              parseFloat(this.listDataPerson[i].longitude),
              parseFloat(this.listDataPerson[i].latitude),
            ],
            offset: new this.AMapInstance.Pixel(-21, -70),
            title: JSON.stringify(this.listDataPerson[i]),
          });

          marker.on('click', (event) => {
            this.isPersonMarkerClick = true;
            // 查找已激活的点，如果找到则该节点到默认状态
            this.markers.forEach((_marker) => {
              // console.log('marker点击事件',  _marker.getExtData().type)
              if (_marker.getContent().includes('person-marker__selected')) {
                _marker.setContent(`<div class="person-marker" title="${
                    JSON.parse(_marker.getTitle())?.userName
                }">
                          <img src="${personMapIcon}" alt="" />
                          <span>${
                    JSON.parse(_marker.getTitle())?.doneBatch
                }</span>
                        </div>`);
                _marker.setPosition([
                  parseFloat(_marker?.getPosition()?.lng),
                  parseFloat(_marker?.getPosition()?.lat),
                ]);
                _marker.setOffset(new this.AMapInstance.Pixel(-21, -70));
              }
            });
            // 如果当前为默认状态，设置为激活状态, 否则恢复默认状态
            const _target = event.target;
            if (_target.getContent().includes('person-marker__selected')) {
              _target.setContent(
                  `<div class="person-marker" title="${
                      JSON.parse(_target.getTitle())?.userName
                  }">
                  <img src="${personMapIcon}" alt="" />
                  <span>${JSON.parse(_target.getTitle())?.doneBatch}</span>
                </div>`
              );
              _target.setPosition([
                parseFloat(JSON.parse(_target.getTitle()).longitude),
                parseFloat(JSON.parse(_target.getTitle()).latitude),
              ]);
              _target.setOffset(new this.AMapInstance.Pixel(-21, -70));
            } else {
              _target.setContent(
                  `<div class="person-marker__selected" title="${
                      JSON.parse(_target.getTitle())?.userName
                  }">
                          <img src="${personMapIconSelected}" alt="" />
                          <span>${
                      JSON.parse(_target.getTitle())?.doneBatch
                  }</span>
                        </div>`
              );
              _target.setPosition([
                parseFloat(JSON.parse(_target.getTitle()).longitude),
                parseFloat(JSON.parse(_target.getTitle()).latitude),
              ]);
              _target.setOffset(new this.AMapInstance.Pixel(-45, -70));
            }
            // 设置当前选择的人员
            this.setCurSelectedUser(this.listDataPerson[i]);
            this.showPath(this.listDataPerson[i])
            // curSelectedPersonForTrajectoryRef = this.listDataPerson[i];
            // setIsShowPersonTrajectoryModal(true);
          });

          this.markers.push(marker);
        }
      }
    },
    drawBoundary(level = 'province', name = this.org.orgProvince) {
      try {
        if (this.AMapInstance && this.mapInstance) {
          // 清除之前绘制的边界
          this.mapInstance.remove(this.mapInstance.getAllOverlays('polygon'));
          // 绘制边界
          const districtSearch = new this.AMapInstance.DistrictSearch({
            // 关键字对应的行政区级别，共有5种级别
            level,
            //  是否显示下级行政区级数，1表示返回下一级行政区
            subdistrict: 1,
            // 返回行政区边界坐标点
            extensions: 'all',
          });
          let self = this
          // 搜索所有省/直辖市信息
          console.log("搜索所有省/直辖市信息---->", name)
          districtSearch.search(name, function (status, result) {
            console.log("搜索所有省/直辖市信息--result-->",result)
            // 查询成功时，result即为对应的行政区信息
            let bounds = result?.districtList[0]?.boundaries;
            if (bounds) {
              for (let i = 0, l = bounds.length; i < l; i++) {
                //生成行政区划polygon
                new self.AMapInstance.Polygon({
                  map: self.mapInstance, // 指定地图对象
                  strokeWeight: 1, // 轮廓线宽度
                  path: bounds[i], //轮廓线的节点坐标数组
                  fillOpacity: 0.15, //透明度
                  fillColor: '#256edc', //填充颜色
                  strokeColor: '#256edc', //线条颜色
                });
              }
              // 地图自适应
              self.mapInstance.setFitView();
              // 设置地图中心
              self.mapInstance.setCenter(result.districtList[0].center);
            }
          });
        }
      } catch (error) {
        console.log("error", error);
      }
    },
    /**
     * @TODO 将地图上的单位选中状态取消
     */
    cancelOrgSelected() {
      this.markers.forEach((_marker) => {
        console.log('cancelOrgSelected')
        console.log('_marker',_marker.getExtData())
        const imgUrl = _marker.getExtData().type == 0 ? placeMapIcon : placeMapGrayIcon;

        if (_marker.getContent().includes('place-marker__selected')) {
          _marker.setContent(`<div class="place-marker" title="${
              JSON.parse(_marker.getTitle())?.enterpriseName ||
              JSON.parse(_marker.getTitle())?.unitName
          }">
                    <img src="${imgUrl}" alt="" />
                    <span>${
              JSON.parse(_marker.getTitle())?.samplingBatch
          }</span>
                  </div>`);
          _marker.setPosition([
            parseFloat(_marker?.getPosition()?.lng),
            parseFloat(_marker?.getPosition()?.lat),
          ]);
          _marker.setOffset(new this.AMapInstance.Pixel(-21, -70));
          return;
        }
      });
    },
    cancelPersonSelected() {
      this.markers.forEach((_marker) => {
        if (_marker.getContent().includes('person-marker__selected')) {
          _marker.setContent(`<div class="person-marker" title="${
              _marker?.userName
          }">
                    <img src="${personMapIcon}" alt="" />
                    <span>${JSON.parse(_marker.getTitle())?.doneBatch}</span>
                  </div>`);
          _marker.setPosition([
            parseFloat(_marker?.getPosition()?.lng),
            parseFloat(_marker?.getPosition()?.lat),
          ]);
          _marker.setOffset(new this.AMapInstance.Pixel(-21, -70));
          return;
        }
      });
    },
    clearMap() {
      this.mapInstance.clearMap()
      this.drawBoundary()
    },
    callBackOrg() {
      if (!this.curSelectedOrganization || this.curShowCategory !== 'sampleOrganization') return;
      if (this.curSelectedOrganization?.latitude && this.curSelectedOrganization?.longitude) {
        // 设置当前地图中心点
        this.mapInstance.setCenter([
          parseFloat(this.curSelectedOrganization?.longitude),
          parseFloat(this.curSelectedOrganization?.latitude),
        ]);
      }
      if (this.isPlaceMarkerClick) {
        // 如果curSelectedOrganization是由点击事件产生的 则不进行默认渲染(因为在点击事件中已经更改过选中图标了)
        this.isPlaceMarkerClick = false;
        return;
      }
      if (this.curSelectedOrganization?.id && this.markers) {
        // 先将之前选定的节点恢复默认
        this.cancelOrgSelected();
        // 再将当前选中单位更改节点样式
        this.markers.forEach((_marker) => {
          const enterpriseLicenseNumber = JSON.parse(_marker.getTitle()).enterpriseLicenseNumber;
          if (enterpriseLicenseNumber === this.curSelectedOrganization.enterpriseLicenseNumber) {
            _marker.setContent(_marker.getContent().replace('place-marker', 'place-marker__selected')
                .replace(placeMapIcon, placeMapIconSelected).replace('title="undefined"',
                    `title=${this.curSelectedOrganization?.enterpriseName || this.curSelectedOrganization?.unitName}`)
            );
          }
        });
      }
    },
    callBackPerson() {
      if (!this.curSelectedUser || this.curShowCategory !== 'samplePerson') return;
      if (this.curSelectedUser?.latitude && this.curSelectedUser?.longitude) {
        this.mapInstance.setCenter([parseFloat(this.curSelectedUser?.longitude), parseFloat(this.curSelectedUser?.latitude)]);
      }
      if (this.isPersonMarkerClick) {
        // 如果curSelectedUser是由点击事件产生的 则不进行默认渲染(因为在点击事件中已经更改过选中图标了)
        this.isPersonMarkerClick = false;
        return;
      }
      if (this.curSelectedUser?.userId && this.markers) {
        // 先将之前选定的节点恢复默认
        this.cancelPersonSelected();
        this.markers.forEach((_item) => {
          const userId = JSON.parse(_item.getTitle()).userId;
          const userName = JSON.parse(_item.getTitle()).userName;
          if (this.curSelectedUser.userId === userId && this.curSelectedUser.userName === userName) {
            _item.setContent(_item.getContent().replace('person-marker', 'person-marker__selected')
                .replace(personMapIcon, personMapIconSelected));
          }
        });
      }
    },
    setCurSelectedUser(val) {
      this.$emit('setCurSelectedUser', val)
    },
    setCurSelectedOrganization(val) {
      this.$emit('setCurSelectedOrganization', val)
    },
    showPath(val) {
      this.show = true
      setTimeout(() => {
        this.$refs.trajectory.open(val)
      })
    }
  },
  destroyed() {
    this.mapInstance = null
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss">
.home_div,
#map {
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 100%;
}

.map-title {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 50px;
  background-color: rgba(27, 25, 27, 0.884);
}

h3 {
  position: absolute;
  left: 10px;
  z-index: 2;
  color: white;
}

.person-marker {
  position: relative;

  span {
    position: absolute;
    top: 13px;
    left: 50%;
    transform: translateX(-50%);
    color: #52CC92FF;
    font-size: 16px;
  }

  .person-marker__header {
    display: inline-block;
    width: 60px;
    font-size: 13px;
    text-align: center;
    line-height: 1.4;
  }

}

.person-marker__selected {
  position: relative;

  span {
    position: absolute;
    top: 64px;
    left: 50%;
    transform: translateX(-50%);
    color: #52CC92FF;
    font-size: 16px;
  }
}

.place-marker {
  position: relative;

  span {
    position: absolute;
    top: 13px;
    left: 50%;
    transform: translateX(-50%);
    color: #6390EBFF;
    font-size: 16px;
  }
}

.place-marker__selected {
  position: relative;

  span {
    position: absolute;
    top: 64px;
    left: 50%;
    transform: translateX(-50%);
    color: #6390EBFF;
    font-size: 16px;
  }
}

// 起点样式
.place-marker__point {
  position: relative;
  width: 66px;
  height: 115px;
  background: linear-gradient(180deg, #81D7FC 0%, rgba(58, 130, 255, 0) 100%);
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  text-align: center;

  .start-point__text {
    width: 100%;
    color: #FFFFFF;
    font-size: 16px;
    padding-top: 10px;
  }

  .start-point__content {
    position: relative;

    span {
      position: absolute;
      top: 15px;
      left: 50%;
      transform: translateX(-50%);
      color: #6390EBFF;
    }
  }
}

.start-point__bg {
  background: linear-gradient(180deg, #81D7FC 0%, rgba(58, 130, 255, 0) 100%);
}

.end-point__bg {
  background: linear-gradient(180deg, #F18529 0%, rgba(241, 133, 41, 0) 100%);
}

// 隐藏路径规划中的起点标志和终点标志
.amap-lib-marker-from,
.amap-lib-marker-to {
  display: none !important;
}

.home_div {
  //.ant-modal-content {
  //  height: 100% !important;
  //
  //  .ant-modal-body {
  //    width: 100% !important;
  //    height: 100% !important;
  //  }
  //}
}
</style>
