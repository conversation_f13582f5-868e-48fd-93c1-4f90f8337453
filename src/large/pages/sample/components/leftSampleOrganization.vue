<template>
  <div class="w-[378px] h-full grid grid-rows-[32px_1fr] gap-1 font-[SourceHanSansCNMedium] text-white">
    <h1 class="w-[378px] flex flex-row flex-nowrap items-center bg-[url('../assets/images/item-title-bg.webp')] bg-cover bg-full pl-[23px]">
      <div class="w-[130px] flex flex-row flex-nowrap items-center gap-1">
        <img src="@/assets/images/sampled-org-icon.webp" alt="" class="w-[26px] h-[26px]"/>
        <!-- <span class="text-lg font-[SourceHanSansCNBold] break-keep">被抽样单位</span> -->
        <el-dropdown class="custom-dropdown" @command="handleCommand">
          <div class="flex flex-row flex-nowrap items-center gap-1 text-lg font-[SourceHanSansCNBold] text-[#FFFFFF]">
            <span class="break-keep">{{ typeTitle }}</span>
            <i class="el-icon-arrow-down el-icon--right"></i>
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="被抽样单位">被抽样单位</el-dropdown-item>
            <el-dropdown-item command="按地区展示">按地区展示</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div v-if="typeTitle === '被抽样单位'" class="w-full flex flex-row flex-nowrap justify-end items-center">
        <img src="@/assets/images/search-icon__81D7FC.webp" alt=""
             class="w-[26px] h-[26px] cursor-pointer transform translate-y-[1px]"
             @click="changeSearch"/>
        <el-input v-if="isSearch" v-model="searchKey" class="relative w-[200px]" @keyup.enter="doSearch"
                  ref="searchOrg" @keyup.esc="changeSearch">
          <i class="el-icon-close text-white" slot="suffix" @click="changeSearch">
          </i>
        </el-input>
      </div>
    </h1>

    <div v-if="loading" class="w-full bg-[rgba(8,24,48,0.52)]">
      <l-loading/>
    </div>
    <div v-else>
      <!-- 被抽样单位 -->
      <template v-if="!isTabsAres">
        <div
          class="w-[378px] h-[42px] flex flex-row flex-nowrap items-center text-[#FFFFFFB2] bg-[rgba(8,24,48,0.52)] px-[10px]">
          <div class="flex-1">被抽样单位名称</div>
          <div class="w-[75px] text-right">完成批次</div>
        </div>
        <div v-if="!isShowSearchResult"
            class="w-full bg-[rgba(8,24,48,0.52)]"
            :style="'height:'+scrollAreaHeight+'px'">
          <swiper :data="listData" :options="swiperOption" class="overflow-hidden w-full h-full" @tap="onSlideChange">
            <swiper-slide v-for="(item,index) in listData" :key="index" class="!w-full !my-0" :data-real-index="index">
              <div
                  class="flex flex-row flex-nowrap items-center w-[378px] !h-[48px] text-white px-[10px] cursor-pointer"
                  :class="isChecked(item)?'bg-[rgba(9,20,36,0.4)] shadow-[inset_0px_0px_40px_0px_#3571EB] border-[2px] border-solid border-white'
                  :(index % 2 === 0 ? '' : 'bg-[rgba(8,24,48,0.4)]')">
                <div class="flex-1 truncate" :title="item.unitName || item.enterpriseName">
                  {{ item.unitName || item.enterpriseName }}
                </div>
                <div class="w-[75px] text-right">
                  <span class="text-[18px] text-[#81D7FCFF] mx-[2px] ">
                    {{ item.samplingBatch }}
                  </span>
                  批
                </div>
              </div>
            </swiper-slide>
          </swiper>
        </div>
        <div v-else class="w-full bg-[rgba(8,24,48,0.52)] overflow-auto"
            :style="'height:'+scrollAreaHeight+'px'">
          <template v-for="(item,index) in searchResultList">
            <div :key="index"
                class="flex flex-row flex-nowrap items-center w-[378px] !h-[48px] text-white px-[10px] cursor-pointer"
                :class="isChecked(item)?'bg-[rgba(9,20,36,0.4)] shadow-[inset_0px_0px_40px_0px_#3571EB] border-[2px] border-solid border-white'
                  :(index % 2 === 0 ? '' : 'bg-[rgba(8,24,48,0.4)]')"
                @click="setCurSelectedOrganization(item)">
              <div class="flex-1 truncate">
                {{ item.unitName || item.enterpriseName }}
              </div>
              <div class="w-[75px] text-right">
                  <span class="text-[18px] text-[#81D7FCFF] mx-[2px] ">
                    {{ item.samplingBatch }}
                  </span>
                批
              </div>
            </div>
        </template>
      </div>
      </template>
      <!-- 按地区展示 -->
      <template v-else>
        <div
          class="w-[378px] h-[42px] flex flex-row flex-nowrap items-center text-[#FFFFFFB2] bg-[rgba(8,24,48,0.52)] px-[10px]">
          <div class="w-[100px]">地区</div>
          <div class="flex-1 text-center">完成数/总数</div>
          <div class="w-[75px] text-right">完成率</div>
        </div>
        <div class="w-full bg-[rgba(8,24,48,0.52)] overflow-auto"
            :style="'height:'+scrollAreaHeight+'px'">
          <template v-for="(item,index) in areasListData">
            <div :key="index"
                class="flex flex-row flex-nowrap items-center w-[378px] !h-[48px] text-white px-[10px] cursor-pointer"
                :class="isChecked(item)?'bg-[rgba(9,20,36,0.4)] shadow-[inset_0px_0px_40px_0px_#3571EB] border-[2px] border-solid border-white'
                  :(index % 2 === 0 ? '' : 'bg-[rgba(8,24,48,0.4)]')"
                @click="setCurSelectedOrganization(item)">
              <div class="w-[100px] truncate" :title="item.region">
                {{ item.region }}
              </div>
              <div class="flex-1 text-center">
                <span class="mx-[2px] ">
                  {{ item.count }}/{{item.finishedCount}}
                </span>
              </div>
              <div class="w-[75px] text-right">
                <span class=" mx-[2px] ">
                  {{ item.finishedCount && item.count ? ((item.finishedCount / item.count) * 100).toFixed(2) : '/' }}
                </span>
                %
              </div>
            </div>
        </template>
      </div>
      </template>

    </div>
  </div>
</template>

<script>

import LLoading from "@/large/components/LLoading/index.vue";
import {Swiper as SwiperClass, Pagination, Mousewheel, Autoplay} from 'swiper/js/swiper.esm'
import getAwesomeSwiper from 'vue-awesome-swiper/dist/exporter'

SwiperClass.use([Pagination, Mousewheel, Autoplay])
const {Swiper, SwiperSlide} = getAwesomeSwiper(SwiperClass)
import 'swiper/css/swiper.css'

import {listOrganizationRank, listAreasRank} from "@/api/modules/large/sample";
import Cookies from "js-cookie";

export default {
  name: 'LeftSampleOrganization',
  components: {LLoading, Swiper, SwiperSlide},
  props: {
    curSelectedDate: {type: Number}
  },
  data() {
    return {
      loading: true,
      scrollAreaHeight: window.innerHeight - 400,
      listData: [],
      swiperOption: {
        direction: 'vertical',
        spaceBetween: 0,
        loop: true,
        slidesPerView: 11,
        autoplay: {
          delay: 3000, // 每个幻灯片之间的延迟时间（以毫秒为单位）
          disableOnInteraction: false // 用户交互时是否停止自动播放
        },
        speed: 5000,
        reverseDirection: true
      },
      isSearch: false,
      searchKey: '',
      searchResultList: [],
      isShowSearchResult: false,
      org: localStorage.getItem('stb-orgInfo') ? JSON.parse(localStorage.getItem('stb-orgInfo')) : {},
      interval: null,
      typeTitle: '被抽样单位',
      areasListData: [], // 按地区查询列表
    }
  },
  computed: {
    isPlay() {
      return (this.$store.getters.showModel && (this.$store.getters.model === 1))
    },
    // 是否是按地区
    isTabsAres() {
      return this.typeTitle === '按地区展示'
    }
  },
  watch: {
    isPlay() {
      this.doPlay()
    }
  },
  mounted() {
    this.loadData()
    window.addEventListener('keyup', this.handleKeyUp);
    window.addEventListener('resize', this.calculateScrollAreaHeight, false);
  },
  beforeDestroy() {
    window.removeEventListener('keyup', this.handleKeyUp);
    window.removeEventListener('resize', this.calculateScrollAreaHeight)
    this.clearPlay()
  },
  methods: {
    calculateScrollAreaHeight() {
      this.scrollAreaHeight = window.innerHeight - 400
      this.swiperOption.slidesPerView = Math.floor(this.scrollAreaHeight / 48)
      this.loading = true
      setTimeout(()=>{
        this.loading = false
      })
    },
    async loadData() {
      this.loading = true
      try {
        if (this.typeTitle === '按地区展示') {
          const data1 = await this.queryAreasList() // 地区
          if (data1.length) {
            this.areasListData = data1
            this.swiperOption.loop = data1.length >= this.swiperOption.slidesPerView
            this.setCurSelectedOrganization(data1[0])
            this.doPlay()
          } else {
            this.areasListData = []
            this.swiperOption.loop = false
            this.setCurSelectedOrganization(null)
          }
        } else {
          const data2 = await this.queryOrgList() // 被抽样单位
          if (data2.length) {
            this.listData = data2
            this.swiperOption.loop = data2.length >= this.swiperOption.slidesPerView
            this.setCurSelectedOrganization(data2[0])
            this.doPlay()
          } else {
            this.listData = []
            this.swiperOption.loop = false
            this.setCurSelectedOrganization(null)
          }
        }
      } catch (error) {
        if (error) {
          this.$message.error(error)
        }
      } finally {
        this.loading = false
      }
    },
    // 地区列表
    async queryAreasList() {
      try {
        const { data } = await listAreasRank({
          timeType: this.curSelectedDate,
          orgId: this.org.orgId
        })
        if (data.length) {
          return data
        } else {
          return []
        }
      } catch (error) {
        throw(`${error}`)
      }
    },
    // 被抽样单位列表
    async queryOrgList(region = null) {
      try {
        const { data } = await listOrganizationRank({
          timeType: this.curSelectedDate,
          orgId: this.org.orgId,
          region: region
        })
        if (data.length) {
          return data
        } else {
          return []
        }
      } catch (error) {
        throw(`${error}`)
      }
    },
    loadDataSocket(socketData) {
      this.loading = true
      setTimeout(() => {
        if (socketData.entityMonitor && socketData.entityMonitor.length) {
          this.listData = socketData.entityMonitor
          this.swiperOption.loop = this.listData.length >= this.swiperOption.slidesPerView
          this.setCurSelectedOrganization(this.listData[0])
          this.doPlay()
          if (this.typeTitle === '按地区展示') { 
            this.areasListData = this.listData
          }
        } else {
          this.listData = []
          if (this.typeTitle === '按地区展示') { 
            this.areasListData = []
          }
          this.swiperOption.loop = false
          this.setCurSelectedOrganization(null)
        }
        this.loading = false
      }, 100)
    },
    isChecked(item) {
      if (this.typeTitle === '被抽样单位') {
        const cur = this.$parent.curSelectedOrganization
        return cur && item.id === cur.id
      } else {
        const cur2 = this.$parent.curSelectedArea
        return cur2 && item.region === cur2.region
      }
    },
    async setCurSelectedOrganization(val) {
      if (this.typeTitle === '按地区展示') {
        const data = await this.queryOrgList(val.region === '全省' ? null : val.region) // 当前地区被抽样单位
        this.$emit('loaded', data)
      } else {
        this.$emit('loaded', this.listData)
      }
      this.$emit('setCurSelectedOrganization', val)
    },
    handleKeyUp(event) {
      if (event.key === 'Enter') {
        this.doSearch()
      }
      if (event.key === 'Escape') {
        if (this.isSearch) {
          this.isSearch = false
          this.isShowSearchResult = false
          this.searchKey = ''
          this.searchResultList = []
        }
      }
    },
    changeSearch() {
      if (this.isSearch) {
        this.searchKey = ''
        this.searchResultList = []
        this.isSearch = false
        this.isShowSearchResult = false
      } else {
        this.isSearch = true
        this.$nextTick(()=>this.$refs.searchOrg.focus())
      }
    },
    doSearch() {
      if (!this.searchKey) return;
      if (!this.listData.length) return;

      let list = []
      this.listData.map(item => {
        if (item.enterpriseName.includes(this.searchKey))
          list.push(item)
      })
      this.searchResultList = list
      this.isShowSearchResult = true
    },
    doPlay() {
      this.clearPlay()
      if (this.isPlay) {
        this.startPlay()
      }
    },
    startPlay() {
      let i = 0
      this.interval = setInterval(() => {
        this.setCurSelectedOrganization(this.listData[i++ % this.listData.length]);
      }, 5000);
    },
    clearPlay() {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = null
      }
    },
    // 
    handleCommand(command) {
      if (this.typeTitle === command) {
        return
      }
      this.typeTitle = command
      if (command === '被抽样单位') {
        this.loadData()
        this.$emit("changeTypeTitle", 1)
      } else {
        this.loadData()
        this.$emit("changeTypeTitle", 2)
      }
    },
    // swiper点击事件
    onSlideChange(swiper) {
      // 向上冒泡查找 class 为 swiper-slide 的元素
      const clickedSlide = swiper.target.closest('.swiper-slide');
      // console.log("元素", clickedSlide);
      if (clickedSlide) {
        const realIndex = clickedSlide.getAttribute('data-real-index');
        if (realIndex !== null) {
          const idx = parseInt(realIndex, 10);
          this.setCurSelectedOrganization(this.listData[idx]);
        }
        // console.log("索引", realIndex);
      }
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
::v-deep {
  .el-input__inner {
    height: 26px;
    line-height: 26px;
    background-color: #08183099;
    border-color: white;
    color: white;
    padding: 0 10px;
  }

  .el-input__suffix {
    right: 5px;
    color: white;
    text-align: center;
    line-height: 26px;
  }
}
</style>
