<template>
  <div class="grid grid-rows-[124px_111px_186px_1fr] gap-4">
    <!--抽样人员信息-->
    <div class="w-[378px] font-[SourceHanSansCNMedium] text-white">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/user-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">抽样人员信息</span>
        </div>
      </h1>

      <div class="w-full h-[78px] bg-[rgba(8,24,48,0.4)] rounded flex flex-row flex-nowrap items-center pl-4 gap-4">
        <div class="w-[55px]">
          <img src="@/assets/images/sample-person-img.webp" alt=""/>
        </div>
        <div class="flex-1 text-sm">
          <p class="mb-2">{{ curSelectedUserDetails?.userName }}</p>
          <p>{{ curSelectedUserDetails?.phoneNumber }}</p>
        </div>
        <div v-if="curSelectedUser"
             class="w-[60px] cursor-pointer"
             title="发起音视频通话"
             @click="startVideo()">
          <span role="img" class="anticon" style="font-size: 40px; color: rgb(80, 118, 193);">
            <svg width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class="">
              <use xlink:href="#icon-yinshipin"></use>
            </svg>
          </span>
        </div>
      </div>
    </div>

    <!--抽样数量-->
    <div class="w-[378px] text-white font-[SourceHanSansCNMedium]">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/sampling-total-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">{{ getSelectedDate(curSelectedDate) }}抽样数量</span>
        </div>
      </h1>

      <div class="w-full h-[65px] bg-[rgba(8,24,48,0.4)] rounded flex flex-row flex-nowrap items-center pl-4 gap-4">
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
              {{ getSelectedDate(curSelectedDate) }}抽样数量/批
            </div>
            <div class="text-[26px] font-[OswaldRegular] ">
              <l-scroll-number :num="curSelectedUserDetails.samplingNumber"/>
            </div>
          </div>
        </div>

        <div class="flex-1  flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-green-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">环比</div>
            <div v-if="getIncrease()>0"
                 class="text-[26px] font-[OswaldRegular] text-[#52CC92FF] flex items-center gap-1">
              <span>{{ getIncrease() + '%' }}</span>
              <img src="@/assets/images/up-icon.webp" alt=""/>
            </div>
            <div v-else-if="getIncrease()===0" class="text-[26px] font-[OswaldRegular] text-[#52CC92FF]">-</div>
            <div v-else class="text-[26px] font-[OswaldRegular] text-[#FFD86DFF] flex items-center gap-1">
              <span>{{ getIncrease() + '%' }}</span>
              <img src="@/assets/images/down-icon.webp" alt=""/>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--年度抽样概况-->
    <div class="w-[378px] text-white font-[SourceHanSansCNMedium]">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/link-ratio-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">年度抽样概况</span>
        </div>
      </h1>
      <div class="w-full h-[70px] bg-[rgba(8,24,48,0.4)] rounded flex flex-row flex-nowrap items-center pl-4 gap-4">
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div>
              <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
                已分配任务/批
              </div>
              <div class="text-[26px] font-[OswaldRegular] ">
                <l-scroll-number :num="userOverview?userOverview.assignedNumber:0"/>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
              已完成任务/批
            </div>
            <div class="text-[26px] font-[OswaldRegular] ">
              <l-scroll-number :num="userOverview?userOverview.doneNumber:0"/>
            </div>
          </div>
        </div>
      </div>
      <div class="w-full h-[70px] bg-[rgba(8,24,48,0.4)] rounded flex flex-row flex-nowrap items-center pl-4 gap-4">
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-white-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
              待完成任务/批
            </div>
            <div class="text-[26px] font-[OswaldRegular] ">
              <l-scroll-number :num="userOverview?userOverview.waitNumber:0"/>
            </div>
          </div>
        </div>
        <div class="flex-1 flex flex-row flex-nowrap items-center gap-2">
          <div class="w-[2px] h-[46px]">
            <img src="@/assets/images/dash-yellow-gap-icon.webp" alt="" class="w-full h-full"/>
          </div>
          <div>
            <div class="text-[13px] text-[#FFFFFFB2] mb-[6px]">
              完成率
            </div>
            <div class="text-[26px] font-[OswaldRegular] text-[#FFD86DFF]">
              {{ completionRate }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--年度抽样趋势-->
    <div class="w-[378px] text-white font-[SourceHanSansCNMedium]">
      <h1 class="flex items-center w-full h-[38px] bg-[url('../assets/images/item-title-bg.webp')]  bg-full pl-[23px] pr-[8px] mb-[2px]">
        <div class="flex flex-row flex-nowrap items-center gap-1">
          <img src="@/assets/images/sample-trend-icon.webp" alt="" class="w-[26px] h-[26px]"/>
          <span class="text-lg font-[SourceHanSansCNBold]">年度抽样趋势</span>
        </div>
      </h1>

      <div
          class="w-full rounded bg-[rgba(8,24,48,0.4)]"
          :style="'height:'+barAreaHeight+'px'">
        <div class="flex flex-row flex-nowrap justify-between items-center px-3 pt-3">
          <span>批</span>
          <div class="flex items-center">
            <span class="inline-block w-3 h-3 bg-[#81D7FCFF] mr-[8px]"></span>
            <span>月度抽样</span>
          </div>
        </div>
        <div id="echartsBar" class="w-full h-full"></div>
      </div>
    </div>
  </div>
</template>

<script>
import {getOverviewPerson, getPersonChartDetail} from "@/api/modules/large/sample";
import LScrollNumber from "@/large/components/LScrollNumber/index.vue";
import echarts from "@/components/Echart/echarts";

export default {
  name: 'RightSamplePerson',
  components: {LScrollNumber},
  props: {
    curSelectedDate: {type: Number},
    curSelectedUser: {type: Object},
    curSelectedUserDetails: {type: Object}
  },
  data() {
    return {
      userOverview: {},
      completionRate: '-',
      barAreaHeight: window.innerHeight - 620,
      curSelectedUserEchartsDetails: [],//当前选择用户的echarts对象
      echartsDomObject: null
    }
  },
  mounted() {
    this.loadData()
    window.addEventListener('resize', this.getBarAreaHeight, false);
    this.initChart()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getBarAreaHeight)
  },
  methods: {
    getBarAreaHeight() {
      this.barAreaHeight = window.innerHeight - 620
    },
    loadData() {
      if (this.curSelectedUser && this.curSelectedUser.userName) {
        getOverviewPerson({userName: this.curSelectedUser.userName}).then(res => {
          this.userOverview = res.data
          if (!this.userOverview) return;
          if (+this.userOverview.doneNumber === 0 || +this.userOverview.assignedNumber === 0) {
            this.completionRate = '0.00%'
          } else {
            this.completionRate = ((this.userOverview?.doneNumber / this.userOverview?.assignedNumber) * 100).toFixed(2) + '%'
          }
        }).catch(res => {
          if (res.msg)
          this.$message.error(res.msg)
        })

        getPersonChartDetail({userName: this.curSelectedUser.userName}).then(res => {
          this.curSelectedUserEchartsDetails = res.data
          this.initChart()
        }).catch(res => {
          if (res.msg)
            this.$message.error(res.msg)
        })
      }
    },
    startVideo() {
      this.$emit('video')
    },
    getSelectedDate(val) {
      if (val < 13) {
        if (new Date().getMonth() !== (val - 1)) {
          return val + '月'
        } else {
          return '本月'
        }
      } else if (val === 13) return '今日'
      else if (val === 14) return '本周'
      return ''
    },
    getIncrease() {
      if (this.curSelectedUserDetails.samplingChain)
        return Math.round(((this.curSelectedUserDetails?.samplingNumber - this.curSelectedUserDetails?.samplingChain)
            / this.curSelectedUserDetails?.samplingChain) * 100)
      return 0
    },
    initChart() {
      if (this.curSelectedUserEchartsDetails) {
        this.echartsDomObject = echarts.init(document.getElementById('echartsBar'));
        const list = [];
        this.curSelectedUserEchartsDetails.map(item => list.push(item.samplingNumber))
        this.echartsDomObject.setOption({
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(50,50,50,0.7)',
            textStyle: {color: '#fff'},
            borderWidth: 0,
            formatter: function (params) {
              return '<div><div>时间：' + params[0].name + '月</div><div>批次数量：' + params[0].value + '</div></div>'
            },
          },
          xAxis: {
            type: 'category',
            data: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
            axisLine: {lineStyle: {color: '#FFFFFF'}},
            axisTick: {show: false}
          },
          yAxis: {
            type: 'value',
            axisLine: {lineStyle: {color: '#FFFFFF'}},
            splitLine: {lineStyle: {type: 'dashed', color: '#FFFFFF66'}, show: true},
          },
          grid: {
            left: '13%',
            top: '10%',
            right: '5%',
            bottom: '15%',
            containLabel: false,
          },
          series: [
            {
              data: list,
              type: 'bar',
              barWidth: 6,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{offset: 0, color: '#385B99'},
                    {offset: 1, color: '#81D7FC'}], false),
                },
              },
            },
          ],
        });
      }
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
</style>
