<template>
  <div class="w-[378px] h-full grid grid-rows-[32px_1fr] gap-1 font-[SourceHanSansCNMedium] text-white">
    <h1 class="w-[378px] flex flex-row flex-nowrap items-center bg-[url('../assets/images/item-title-bg.webp')] bg-cover bg-full pl-[23px]">
      <div class="flex flex-row flex-nowrap items-center gap-1">
        <img src="@/assets/images/sample-plan-icon.webp" alt="" class="w-[26px] h-[26px]"/>
        <span class="text-lg font-[SourceHanSansCNBold]">抽样计划</span>
      </div>
    </h1>

    <div v-if="loading" class="w-full bg-[rgba(8,24,48,0.52)]">
      <l-loading/>
    </div>
    <div v-else>
      <div
          class="w-full h-[42px] flex flex-row flex-nowrap items-center text-[#FFFFFFB2] bg-[rgba(8,24,48,0.52)] px-[10px]">
        <div class="w-[234px] truncate">抽样计划</div>
        <div class="w-[72px]">抽样批次</div>
        <div class="w-[72px]">总完成率</div>
      </div>
      <div
          class="w-full bg-[rgba(8,24,48,0.52)]"
          :style="'height:'+scrollAreaHeight+'px'">
        <swiper :data="listData" :options="swiperOption" class="overflow-hidden w-full h-full" @tap="onSlideChange">
          <swiper-slide v-for="(item,index) in listData" class="!w-full !my-0" :key="index" :data-real-index="index">
            <div
                 class="flex flex-row flex-nowrap items-center w-[378px] !h-[42px] text-white pl-[10px] cursor-pointer px-[10px]"
                 :class="isChecked(item)?'bg-[rgba(9,20,36,0.4)] shadow-[inset_0px_0px_40px_0px_#3571EB] border-[2px] border-solid border-white'
                 :(index % 2 === 0 ? '' : 'bg-[rgba(8,24,48,0.4)]')">
              <div class="w-[234px] truncate" :title="item.planName">{{ item.planName }}</div>
              <div class="w-[72px] text-[13px] text-[#81D7FCFF] text-right pr-[15px]">
                {{ item.currentBatch }}批
              </div>
              <div class="w-[72px] text-[13px] text-[#52CC92FF] text-right">
                {{ calculateCompletionRate(item) }}
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
  </div>
</template>

<script>

import LLoading from "@/large/components/LLoading/index.vue";
import {Swiper as SwiperClass, Pagination, Mousewheel, Autoplay} from 'swiper/js/swiper.esm'
import getAwesomeSwiper from 'vue-awesome-swiper/dist/exporter'

SwiperClass.use([Pagination, Mousewheel, Autoplay])
const {Swiper, SwiperSlide} = getAwesomeSwiper(SwiperClass)
import 'swiper/css/swiper.css'

import {listPlanRank} from "@/api/modules/large/sample";
import Cookies from "js-cookie";

export default {
  name: 'LeftSamplePlan',
  components: {LLoading, Swiper, SwiperSlide},
  props: {
    curSelectedDate: {type: Number}
  },
  data() {
    return {
      loading: true,
      scrollAreaHeight: window.innerHeight - 420,
      listData: [],
      swiperOption: {
        direction: 'vertical',
        spaceBetween: 0,
        loop: true,
        slidesPerView: 11,
        autoplay: {
          delay: 3000, // 每个幻灯片之间的延迟时间（以毫秒为单位）
          disableOnInteraction: false // 用户交互时是否停止自动播放
        },
        speed: 5000,
        reverseDirection: true
      },
      org: localStorage.getItem('stb-orgInfo') ? JSON.parse(localStorage.getItem('stb-orgInfo')) : {},
      interval: null
    }
  },
  computed: {
    isPlay() {
      return (this.$store.getters.showModel && (this.$store.getters.model === 1))
    }
  },
  watch: {
    isPlay() {
      this.doPlay()
    }
  },
  mounted() {
    this.loadData()
    window.addEventListener('resize', this.calculateScrollAreaHeight, false);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateScrollAreaHeight)
    this.clearPlay()
  },
  methods: {
    calculateScrollAreaHeight() {
      this.scrollAreaHeight = window.innerHeight - 420
      this.swiperOption.slidesPerView = Math.floor(this.scrollAreaHeight / 42)
      this.loading = true
      setTimeout(() => {
        this.loading = false
      })
    },
    loadData() {
      this.loading = true
      listPlanRank({
        timeType: this.curSelectedDate,
        orgId: this.org.orgId
      }).then(res => {
        if (res.data.length) {
          this.listData = res.data
          this.swiperOption.loop = res.data.length >= this.swiperOption.slidesPerView
          this.setCurSelectedPlan(res.data[0])
          this.doPlay()
        } else {
          this.listData = []
          this.swiperOption.loop = false
          this.setCurSelectedPlan(null)
        }
      }).catch(res => {
        if (res.msg)
          this.$message.error(res.msg)
      }).finally(() => {
        this.loading = false
      })
    },
    loadDataSocket(socketData) {
      this.loading = true
      setTimeout(() => {
        if (socketData.planMonitor && socketData.planMonitor.length) {
          this.listData = socketData.planMonitor
          this.swiperOption.loop = this.listData.length >= this.swiperOption.slidesPerView
          this.setCurSelectedPlan(this.listData[0])
          this.doPlay()
        } else {
          this.listData = []
          this.swiperOption.loop = false
          this.setCurSelectedPlan(null)
        }
        this.loading = false
      }, 100)
    },
    isChecked(item) {
      const cur = this.$parent.curSelectedPlan
      return cur && item.order === cur.order
    },
    calculateCompletionRate(item) {
      if (!item.planBatch) return;
      if (item.doneBatch === 0) return '0.00%';
      return ((item.doneBatch / item.planBatch) * 100).toFixed(2) + '%';
    },
    setCurSelectedPlan(val) {
      this.$emit('setCurSelectedPlan', val)
    },
    doPlay() {
      this.clearPlay()
      if (this.isPlay) {
        this.startPlay()
      }
    },
    startPlay() {
      let i = 0
      this.interval = setInterval(() => {
        this.setCurSelectedPlan(this.listData[i++ % this.listData.length]);
      }, 5000);
    },
    clearPlay() {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = null
      }
    },
    // swiper点击事件
    onSlideChange(swiper) {
      // 向上冒泡查找 class 为 swiper-slide 的元素
      const clickedSlide = swiper.target.closest('.swiper-slide');
      // console.log("元素", clickedSlide);
      if (clickedSlide) {
        const realIndex = clickedSlide.getAttribute('data-real-index');
        if (realIndex !== null) {
          const idx = parseInt(realIndex, 10);
          this.setCurSelectedPlan(this.listData[idx]);
        }
        // console.log("索引", realIndex);
      }
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
</style>
