<template>
  <div class="w-[378px] h-full grid grid-rows-[32px_1fr] gap-1 font-[SourceHanSansCNMedium] text-white">
    <h1 class="w-[378px] flex flex-row flex-nowrap items-center bg-[url('../assets/images/item-title-bg.webp')] bg-cover bg-full pl-[23px]">
      <div class="w-[115px] flex flex-row flex-nowrap items-center gap-1">
        <img src="@/assets/images/user-icon.webp" alt="" class="w-[26px] h-[26px]"/>
        <span class="text-lg font-[SourceHanSansCNBold] break-keep">抽样人员</span>
      </div>
      <div class="w-full flex flex-row flex-nowrap justify-end items-center">
        <img src="@/assets/images/search-icon__81D7FC.webp" alt=""
             class="w-[26px] h-[26px] cursor-pointer transform translate-y-[1px]"
             @click="changeSearch"/>
        <el-input v-if="isSearch" v-model="searchKey" class="relative w-[214px]" @keyup.enter="doSearch"
                  ref="searchPerson" @keyup.esc="changeSearch">
          <i class="el-icon-close text-white" slot="suffix" @click="changeSearch">
          </i>
        </el-input>
      </div>
    </h1>

    <div v-if="loading" class="w-full bg-[rgba(8,24,48,0.52)]">
      <l-loading/>
    </div>
    <div v-else
         class="w-full bg-[rgba(8,24,48,0.52)]"
         :style="'height:'+scrollAreaHeight+'px'">
      <swiper v-if="!isShowSearchResult" :data="listData" :options="swiperOption" class="overflow-hidden w-full h-full" @tap="onSlideChange">
        <swiper-slide v-for="(item,index) in listData" :key="index" :data-real-index="index">
          <div
               class="flex flex-row flex-nowrap items-center w-[378px] !h-[48px] text-white pl-[10px] cursor-pointer"
               :class="isChecked(item)?'bg-[rgba(9,20,36,0.4)] shadow-[inset_0px_0px_40px_0px_#3571EB] border-[2px] border-solid border-white'
               :(index % 2 === 0 ? '' : 'bg-[rgba(8,24,48,0.4)]')">
            <div class="w-[22px] text-[12px] text-left text-gray-100">
              {{ index + 1 }}
            </div>
            <div
                class="w-[42px] text-[13px]"
                :class="item.isOnline === 1 ? 'text-[#52CC92FF]' : 'text-[#FFFFFFB2]'">
              {{ item.isOnline === 1 ? '在线' : '离线' }}
            </div>
            <div class="w-[75px] pl-[10px]">{{ item.userName }}</div>
            <div class="w-[112px] pl-[10px]">
              完成<span class="text-[18px] text-[#81D7FCFF] mx-[2px]">{{ item.doneBatch }}</span>批
            </div>
            <div class="w-[116px] flex flex-row flex-nowrap items-center">
              <span>环比</span>
              <div
                  class="mr-[2px] flex-1 text-right"
                  :class="item.increase >= 0 ? 'text-[#52CC92FF]' : 'text-[#FFD86DFF]'">
                {{ item.increase ? item.increase + '%' : '-' }}
              </div>
              <div class="w-8 h-[14px] pr-1">
                <img v-if="item.increase>0" src="@/assets/images/up-icon.webp" alt=""
                     class="w-[14px] h-[14px] ml-auto"/>
                <img v-else-if="item.increase<0" src="@/assets/images/down-icon.webp" alt=""
                     class="w-[14px] h-[14px] ml-auto"/>
                <div v-else class="w-[14px] h-[14px] ml-auto text-center">-</div>
              </div>
            </div>
          </div>
        </swiper-slide>
      </swiper>
      <div v-else class="w-full overflow-auto"
           :style="'height:'+scrollAreaHeight+'px'">
        <template v-for="(item,index) in searchResultList">
          <div :key="index"
               class="flex flex-row flex-nowrap items-center w-[378px] !h-[48px] text-white pl-[10px] cursor-pointer"
               :class="isChecked(item)?'bg-[rgba(9,20,36,0.4)] shadow-[inset_0px_0px_40px_0px_#3571EB] border-[2px] border-solid border-white'
               :(index % 2 === 0 ? '' : 'bg-[rgba(8,24,48,0.4)]')"
               @click="setCurSelectedUser(item)">
            <div
                class="w-[46px] text-[13px]"
                :class="item.isOnline === 1 ? 'text-[#52CC92FF]' : 'text-[#FFFFFFB2]'">
              {{ item.isOnline === 1 ? '在线' : '离线' }}
            </div>
            <div class="w-[82px] pl-[10px]">{{ item.userName }}</div>
            <div class="w-[122px] pl-[10px]">
              完成<span class="text-[18px] text-[#81D7FCFF] mx-[2px]">{{ item.doneBatch }}</span>批
            </div>
            <div class="w-[128px] flex flex-row flex-nowrap items-center">
              <span>环比</span>
              <div
                  class="mr-[2px] flex-1 text-right"
                  :class="item.increase >= 0 ? 'text-[#52CC92FF]' : 'text-[#FFD86DFF]'">
                {{ item.increase ? item.increase + '%' : '-' }}
              </div>
              <div class="w-8 h-[14px] pr-1">
                <img v-if="item.increase>0" src="@/assets/images/up-icon.webp" alt=""
                     class="w-[14px] h-[14px] ml-auto"/>
                <img v-else-if="item.increase<0" src="@/assets/images/down-icon.webp" alt=""
                     class="w-[14px] h-[14px] ml-auto"/>
                <div v-else class="w-[14px] h-[14px] ml-auto text-center">-</div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>

import LLoading from "@/large/components/LLoading/index.vue";
import {Swiper as SwiperClass, Pagination, Mousewheel, Autoplay} from 'swiper/js/swiper.esm'
import getAwesomeSwiper from 'vue-awesome-swiper/dist/exporter'

SwiperClass.use([Pagination, Mousewheel, Autoplay])
const {Swiper, SwiperSlide} = getAwesomeSwiper(SwiperClass)
import 'swiper/css/swiper.css'

import {listPersonRank,} from "@/api/modules/large/sample";
import Cookies from "js-cookie";

export default {
  name: 'LeftSamplePerson',
  components: {LLoading, Swiper, SwiperSlide},
  props: {
    curSelectedDate: {type: Number}
  },
  data() {
    return {
      loading: true,
      scrollAreaHeight: window.innerHeight - 200,
      listData: [],
      swiperOption: {
        direction: 'vertical',
        spaceBetween: 0,
        loop: false,
        slidesPerView: 11,
        autoplay: {
          delay: 3000, // 每个幻灯片之间的延迟时间（以毫秒为单位）
          disableOnInteraction: false // 用户交互时是否停止自动播放
        },
        speed: 5000,
        reverseDirection: true
      },
      isSearch: false,
      searchKey: '',
      searchResultList: [],
      isShowSearchResult: false,
      org: localStorage.getItem('stb-orgInfo') ? JSON.parse(localStorage.getItem('stb-orgInfo')) : {},
      interval: null
    }
  },
  computed: {
    isPlay() {
      return (this.$store.getters.showModel && (this.$store.getters.model === 1))
    }
  },
  watch: {
    isPlay() {
      this.doPlay()
    }
  },
  mounted() {
    this.loadData()
    window.addEventListener('keyup', this.handleKeyUp);
    window.addEventListener('resize', this.calculateScrollAreaHeight, false);
  },
  beforeDestroy() {
    window.removeEventListener('keyup', this.handleKeyUp);
    window.removeEventListener('resize', this.calculateScrollAreaHeight)
    this.clearPlay()
  },
  methods: {
    calculateScrollAreaHeight() {
      this.scrollAreaHeight = window.innerHeight - 200
      this.swiperOption.slidesPerView = Math.floor((window.innerHeight - 200) / 48)
      this.loading = true
      setTimeout(()=>{
        this.loading = false
      })
    },
    loadData() {
      this.loading = true
      listPersonRank({
        timeType: this.curSelectedDate,
        orgId: this.org.orgId
      }).then(res => {
        if (res.data.length) {
          res.data.map(item => {
            item.increase = item.lastPeriod ? Math.round(((item.doneBatch - item.lastPeriod) / item.lastPeriod) * 100) : 0
          })
          this.listData = res.data
          this.swiperOption.slidesPerView = Math.floor((window.innerHeight - 200) / 48)
          this.swiperOption.loop = res.data.length >= this.swiperOption.slidesPerView
          this.setCurSelectedUser(res.data[0])
          this.doPlay()
        } else {
          this.listData = []
          this.swiperOption.loop = false
          this.setCurSelectedUser(null)
        }
      }).catch(res => {
        if (res.msg)
          this.$message.error(res.msg)
      }).finally(() => {
        this.loading = false
      })
    },
    loadDataSocket(socketData) {
      this.loading = true
      setTimeout(() => {
        if (socketData.userInfoMonitor && socketData.userInfoMonitor.length) {
          socketData.userInfoMonitor.map(item => {
            item.increase = item.lastPeriod ? Math.round(((item.doneBatch - item.lastPeriod) / item.lastPeriod) * 100) : 0
          })
          this.listData = socketData.userInfoMonitor
          this.swiperOption.slidesPerView = Math.floor((window.innerHeight - 200) / 48)
          this.swiperOption.loop = this.listData.length >= this.swiperOption.slidesPerView
          this.setCurSelectedUser(this.listData[0])
          this.doPlay()
        } else {
          this.listData = []
          this.swiperOption.loop = false
          this.setCurSelectedUser(null)
        }
        this.loading = false
      }, 100)
    },
    isChecked(item) {
      const cur = this.$parent.curSelectedUser
      return cur && item.userName === cur.userName
    },
    setCurSelectedUser(val) {
      this.$emit('loaded',this.listData)
      this.$emit('setCurSelectedUser', val)
    },
    handleKeyUp(event) {
      if (event.key === 'Enter') {
        this.doSearch()
      }
      if (event.key === 'Escape') {
        if (this.isSearch) {
          this.isSearch = false
          this.isShowSearchResult = false
          this.searchKey = ''
          this.searchResultList = []
        }
      }
    },
    changeSearch() {
      if (this.isSearch) {
        this.isSearch = false
        this.isShowSearchResult = false
        this.searchKey = ''
        this.searchResultList = []
      } else {
        this.isSearch = true
        this.$nextTick(()=>this.$refs.searchPerson.focus())
      }
    },
    doSearch() {
      if (!this.searchKey) return;
      if (!this.listData.length) return;

      let list = []
      this.listData.map(item => {
        if (item.userName.includes(this.searchKey))
          list.push(item)
      })
      this.searchResultList = list
      this.isShowSearchResult = true
    },
    doPlay() {
      this.clearPlay()
      if (this.isPlay) {
        this.startPlay()
      }
    },
    startPlay() {
      let i = 0
      this.interval = setInterval(() => {
        this.setCurSelectedUser(this.listData[i++ % this.listData.length]);
      }, 5000);
    },
    clearPlay() {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = null
      }
    },
    // swiper点击事件
    onSlideChange(swiper) {
      // 向上冒泡查找 class 为 swiper-slide 的元素
      const clickedSlide = swiper.target.closest('.swiper-slide');
      // console.log("元素", clickedSlide);
      if (clickedSlide) {
        const realIndex = clickedSlide.getAttribute('data-real-index');
        if (realIndex !== null) {
          const idx = parseInt(realIndex, 10);
          this.setCurSelectedUser(this.listData[idx]);
        }
        // console.log("索引", realIndex);
      }
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
::v-deep {
  .el-input__inner {
    height: 26px;
    line-height: 26px;
    background-color: #08183099;
    border-color: white;
    color: white;
    padding: 0 10px;
  }

  .el-input__suffix {
    right: 5px;
    color: white;
    text-align: center;
    line-height: 26px;
  }
}
</style>
