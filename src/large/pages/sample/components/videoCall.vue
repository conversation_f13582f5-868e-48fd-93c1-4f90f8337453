<template>
  <vue-drag-resize :isDraggable="isDrag" :isResizable="false"
                   class="w-full h-full">
    <div class="w-[396px] h-[75vh] ant-modal-content font-[SourceHanSansCNMedium]">
      <div class="relative w-full flex flex-row flex-nowrap text-center ant-modal-header"
           v-on:mouseover="setDrag(true)"
           v-on:mouseout="setDrag(false)">
        <el-popover
            placement="bottom-start"
            width="270"
            v-model="showSet">
          <p class="leading-[22px] mb-[8px] font-semibold text-[#000000E0]">设置该终端名称</p>
          <el-input ref="inputP" placeholder="请输入" v-model="pName" class="input-with-select" size="small">
            <el-button slot="append" size="small" type="primary" class="w-[64px]" @click="handleSave">{{ '保 存' }}
            </el-button>
          </el-input>
          <i slot="reference" class="el-icon-setting text-[18px] p-1.5"></i>
        </el-popover>
        <span class="flex-1 cursor-move text-[16px]">
            与{{ curSelectedUser.userName }}的视频通话
          </span>
        <i class="el-icon-close text-[18px] p-1.5" @click="handleClose"></i>
      </div>
      <div></div>
      <iframe
          v-if="aVideoUrl"
          ref="aVideoIframeRef"
          id="aVideo-iframe-wrapper"
          title="video-window"
          :src="aVideoUrl"
          frameBorder="0"
          width="100%"
          height="100%"
          allow="microphone;camera;fullscreen;"/>
    </div>
  </vue-drag-resize>
</template>

<script>
import VueDragResize from 'vue-drag-resize';

export default {
  name: 'VideoCall',
  components: {VueDragResize},
  props: {
    curSelectedUser: {type: Object}
  },
  data() {
    return {
      aVideoUrl: '',
      showSet: false,
      pName: localStorage.getItem('curTerminalDeviceName') || '',
      isDrag: false
    }
  },
  mounted() {
    setTimeout(() => {
      if (!this.pName) {
        this.showSet = true
        setTimeout(() => {
          this.$refs.inputP.focus({cursor: 'end'})
        })
      } else {
        this.call()
      }
    })
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleSave() {
      localStorage.setItem('curTerminalDeviceName', this.pName)
      this.showSet = false
      if (this.pName)
        this.call()
    },
    call() {
      this.aVideoUrl = "https://avideo.ejclims.com" +
          "?reqUserSigUrl=" +
          "https://cyfz.hnssj.org.cn" +
          "/largeScreen/getUserSigAndAppId&userID=" + this.pName +
          "&calledUserID=" + this.curSelectedUser.userId +
          "&authKey=GOI1lDb5mS5Tpu%Uf5fZ4pN%EiUY5mDO"
    },
    setDrag(val) {
      if (val === this.isDrag) return
      this.isDrag = val
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
.ant-modal-content {
  padding: 0;
  position: absolute;
  z-index: 999;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 0;
  border-radius: 8px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  pointer-events: auto;
  top: calc(13vh - 74px);
  left: calc(50vw - 200px);
}

.ant-modal-header {
  display: flex;
  align-items: center;
  width: 100%;
  height: 45px;
  padding: 0 10px 0 10px;
  margin: 0;
}
</style>
<style lang="scss">
.input-with-select .el-input-group__append {
  color: #fff;
  background-color: #4096ff;
  font-weight: 500;
  font-family: SourceHanSansCNMedium;
}

.vdr.active:before {
  outline: 0;
}
</style>
