<template>
  <div class="lsn-div" :style="'height:'+fontSize+'px'">
    <template v-for="(item,index) in data">
      <div v-if="item.num===-1" class="text-white font-[OswaldRegular]" :style="'font-size:'+fontSize+'px'">,</div>
      <l-single-number v-else :num="item.num" :delay="item.delay" :delay-base="delayBase" :duration="duration"
                       :font-size="fontSize" :font-weight="fontWeight" :width-scale="widthScale"
                       :click-refresh="clickRefresh"/>
    </template>
  </div>
</template>

<script>
import LSingleNumber from "@/large/components/LMulNumber/LSingleNumber/index.vue";

export default {
  name: 'LMulNumber',
  components: {LSingleNumber},
  props: {
    num: {type: [String,Number], default: 0},// 个位数
    fontSize: {type: Number, default: 55},// 字号
    fontWeight: {type: Number, default: 500},// 字重
    delayBase: {type: Number, default: 0.15},// 延迟时间的基数
    duration: {type: Number, default: 2000},// 持续时间
    widthScale: {type: Number, default: 0.5},// 宽度相对于字号大小的倍数
    thousandth: {type: Boolean, default: true},// 是否开启千分位
    clickEvent: {type: Boolean}// 点击事件开关
  },
  data() {
    return {
      clickRefresh: false,
      data: []
    }
  },
  watch: {
    num() {
      this.initialize()
    }
  },
  mounted() {
    this.initialize()
  },
  methods: {
    initialize() {
      this.data = []
      let list = []
      if (this.num || this.num === 0) {
        let numbers = this.num.toString().split('').reverse()
        numbers.map((item, index) => {
          if (this.thousandth && index > 0 && index % 3 === 0) {
            list.push({num: -1})
          }
          list.push({num: +item, delay: index + 1})
        })
      }
      setTimeout(() => {
        this.data = list.reverse()
      }, 10)
    }
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
.lsn-div {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  cursor: pointer;
  height: 100%;
  overflow: hidden;
}
</style>
