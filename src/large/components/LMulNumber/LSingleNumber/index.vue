<template>
  <span class="topDiv" :style="style">
  <ul class="scrollUl" ref="scrollUlRef">
    <li>0</li>
    <li>1</li>
    <li>2</li>
    <li>3</li>
    <li>4</li>
    <li>5</li>
    <li>6</li>
    <li>7</li>
    <li>8</li>
    <li>9</li>

    <li>0</li>
    <li>1</li>
    <li>2</li>
    <li>3</li>
    <li>4</li>
    <li>5</li>
    <li>6</li>
    <li>7</li>
    <li>8</li>
    <li>9</li>
  </ul>
  </span>
</template>

<script>
import {gaoDeIpHeader, gaoDeWeatherHeader, WEBSEVICE_KEY} from "@/config";
import {xHttp} from "@/api/xRequest";

export default {
  name: 'LSingleNumber',
  props: {
    num: {type: Number},// 个位数
    fontSize: {type: Number},// 字号
    fontWeight: {type: Number},// 字重
    delay: {type: Number},// 延迟时间的倍率
    delayBase: {type: Number},// 延迟时间的基数
    duration: {type: Number},// 持续时间
    widthScale: {type: Number},// 宽度相对于字号大小的倍数
    clickRefresh: {type: Boolean},// 刷新事件提示
  },
  data() {
    return {
      isRefresh: true,
      style: {}
    }
  },
  created() {
    this.style = {
      fontSize: this.fontSize + 'px',
      fontWeight: this.fontWeight,
      height: this.fontSize + 'px',
      width: this.fontSize * this.widthScale + 'px',
      lineHeight: '100%'
    }
  },
  mounted() {
    this.refresh()
    setTimeout(()=>{
      this.initialize()
    },10)
  },
  methods: {
    doRefresh(a, b) {
      if (this.num === a && this.isRefresh === b) {
        return
      }
      if (a < 0 || a > 9) {
        throw Error('个位数错误')
      }
      this.num = a
      this.isRefresh = b
      clearTimeout(this.timer)
      if (!this.isRefresh && this.$refs.scrollUlRef) {
        this.initialize();
      }
    },
    setIsCallBack(b) {
      if (this.clickRefresh !== b) {
        this.clickRefresh = b
        if (this.clickRefresh) {
          this.refresh()
        }
      }
    },
    refresh() {
      if (this.clickRefresh) {
        //刷新时间回调 待完成
      }
      this.isRefresh = true
      this.$refs.scrollUlRef.style.transition = ''
      this.$refs.scrollUlRef.style.transform = 'translateY(0px)'
      setTimeout(() => {
        this.isRefresh = false
      }, 0)
    },
    initialize() {
      if (this.$refs.scrollUlRef) {
        this.$refs.scrollUlRef.style.transition = 'all ' + (this.duration / 1000) + 's ease-out'
        this.$refs.scrollUlRef.style.transitionDelay = (this.delay * this.delayBase) + 's'
        this.$refs.scrollUlRef.style.transform = 'translateY(-' + ((this.num + 10) * this.fontSize) + 'px)'

        this.timer = setTimeout(() => {
          if (this.$refs.scrollUlRef?.style) {
            this.$refs.scrollUlRef.style.transition = ''
            this.$refs.scrollUlRef.style.transform = 'translateY(-' + (this.num * this.fontSize) + 'px)'
            this.timer = 0
          }
        }, this.duration + 400 + this.delay * this.delayBase * 1000)
      }
    },
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
.topDiv {
  color: white;
  text-align: center;
  overflow: hidden;
  font-family: OswaldRegular;

  .scrollUl {
    width: 100%;
  }
}
</style>
