<template>
  <div
    class="large-header w-full h-[74px] bg-[url('../assets/images/top-bg.webp')] bg-cover bg-full flex flex-row items-center justify-between pr-0"
  >
    <div></div>
    <div class="h-[42px] flex justify-end">
      <div
        class="flex justify-center items-center w-[148px] cursor-pointer"
        :class="showModel ? '' : 'hidden'"
        style="border-right: 1px solid white"
        @click="changeModel"
      >
        <img
          src="@/assets/images/playStart.webp"
          alt=""
          class="w-[36px] h-[36px]"
          :class="isModel1 ? 'animate-slowSpin' : ''"
        />
        <p
          class="ml-[10px] text-[14px] font-[SourceHanSansCNBold] font-bold"
          :class="isModel1 ? 'text-[#52CC92]' : 'text-[#00B1F4]'"
        >
          {{ isModel1 ? "演示模式" : "交互模式" }}
        </p>
      </div>
      <div class="w-[148px] text-center" style="border-right: 1px solid white">
        <p
          class="text-[20px] font-[OswaldBold] font-medium text-[#FFFFFF] h-[24px] leading-[24px]"
        >
          {{ currentTime }}
        </p>
        <p
          class="text-[12px] font-[SourceHanSansCNMedium] font-light text-[#FFFFFF] h-[12px] leading-[12px] mt-[4px]"
        >
          {{ formattedDate }}
        </p>
      </div>
      <div class="w-[148px] text-center">
        <p
          class="text-[20px] font-[OswaldBold] font-medium text-[#FFFFFF] h-[24px] leading-[24px]"
        >
          {{ weather?.temperature }}℃
        </p>
        <p
          class="text-[12px] font-[SourceHanSansCNMedium] font-light text-[#FFFFFF] h-[12px] leading-[12px] mt-[4px]"
        >
          {{ weather?.weather }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { gaoDeIpHeader, gaoDeWeatherHeader, WEBSEVICE_KEY } from "@/config";
import { xHttp } from "@/api/xRequest";

export default {
  data() {
    return {
      currentTime: "",
      formattedDate: "",
      weather: {},
      interval: "",
    };
  },
  components: {},
  created() {
    this.updateTime();
    this.addInterval();
    this.getWeather();
  },
  computed: {
    isModel1() {
      return this.$store.getters.model === 1;
    },
    showModel() {
      return this.$store.getters.showModel;
    },
  },
  methods: {
    updateTime() {
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, "0");
      const day = String(currentDate.getDate()).padStart(2, "0");
      const hours = String(currentDate.getHours()).padStart(2, "0");
      const minutes = String(currentDate.getMinutes()).padStart(2, "0");
      this.formattedDate = `${year}-${month}-${day}`;
      this.currentTime = `${hours}:${minutes}`;
    },
    addInterval() {
      this.interval = setInterval(() => {
        this.updateTime();
      }, 1000);
    },
    getWeather() {
      xHttp
        .get(`${gaoDeIpHeader}?output=JSON&key=${WEBSEVICE_KEY}`)
        .then((res) => {
          if (res.status === "1") {
            xHttp
              .get(
                `${gaoDeWeatherHeader}?key=${WEBSEVICE_KEY}&city=${res.adcode}`
              )
              .then((res) => {
                if (res.status === "1") {
                  this.weather = res.lives[0];
                } else {
                  if (res.msg) this.$message.error(res.msg);
                }
              });
          } else {
            if (res.msg) this.$message.error(res.msg);
          }
        });
    },
    changeModel() {
      if (this.$store.state.model.model === 1) {
        this.$store.dispatch("model/toggleModel", 2);
      } else {
        this.$store.dispatch("model/toggleModel", 1);
      }
    },
  },
  destroyed() {
    this.clearInterval(this.interval);
  },
};
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
.large-header {
  padding-left: 50px;
  color: rgba(0, 0, 0, 0.88);
  line-height: 64px;
  background-color: #001529;
}

slowSpin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes slowSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-slowSpin {
  animation: slowSpin 8s linear infinite;
}
</style>
