<template>
  <span ref="countUp">
  <count-up :endVal="+num" :options="options"/>
  </span>
</template>

<script>
import CountUp from 'vue-countup-v2';

export default {
  name: 'LScrollNumber',
  components: {
    CountUp
  },
  props: {
    num: {type: [String,Number], default: 0},// 个位数
  },
  data() {
    return {
      // delay: 10000,
      options: {
        useEasing: true,
        useGrouping: true,
        separator: ',',
        decimal: '.',
        prefix: '',
        suffix: '',
        duration:3,
        enableScrollSpy:true,
        scrollSpyDelay:1000
      }
    }
  },
  watch: {
    num() {
      // this.initialize()
    }
  },
  mounted() {
  },
  methods: {
  }
}
</script>

<style src="@/style/output.css" scoped></style>
<style lang="scss" scoped>
.lsn-div {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  cursor: pointer;
  height: 100%;
  overflow: hidden;
}
</style>
