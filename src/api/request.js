import { config } from "@/config";
import router from "@/router";
import { getToken, removeToken } from "@/utils/auth.js";
import axios from "axios";
import { Message } from "element-ui";
import Cookies from "js-cookie";
export class Http {
  constructor(url) {
    this.baseUrl = url || config.baseURL;
    this.instance = axios.create({
      baseURL: this.baseUrl,
      method: "get",
      timeout: 600000,
      withCredentials: false, // 跨域请求不发送cookies
      headers: {
        "Content-Type": "application/json",
        // 添加允许跨域访问的头部
        "Access-Control-Allow-Origin": "*",
      },
    });
    // 初始化时直接设置拦截器
    this.setupInterceptors();
  }

  put(url, data) {
    return this.request({ method: "put", url, data });
  }

  delete(url, params) {
    return this.request({ method: "delete", url, params });
  }

  post(url, data) {
    return this.request({ method: "post", url, data });
  }

  get(url, params, config = {}) {
    // 允许传递 responseType
    return this.request({ method: "get", url, params, ...config });
  }

  request(params = { method: "get" }) {
    return new Promise((resolve, reject) => {
      this.instance
        .request({
          ...params,
        })
        .then((response) => {
          // 这里直接处理响应数据，拦截器已经处理了状态码
          resolve(response.data);
        })
        .catch((err) => {
          // 这里无需额外处理，拦截器已经处理错误情况
          reject(err);
        });
    });
  }

  // 设置请求和响应拦截器
  setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      function (config) {
        const token = getToken();
        if (token) {
          config.headers["Authorization"] = "Bearer " + token; // 让每个请求携带Bearer token
        }

        // 在直接连接API模式下，确保请求头支持CORS
        config.headers["X-Requested-With"] = "XMLHttpRequest";

        return config;
      },
      function (error) {
        // 对请求错误做些什么
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        const res = response.data;
        const status = res.code;

        // 未登录状态
        if (status && +status === 401) {
          Message.error("当前未登录,请登录!");
          removeToken();
          // 使用setTimeout确保消息显示后再跳转
          setTimeout(() => {
            router.push({ path: "/login" });
          }, 100);
          return Promise.reject(new Error("未登录"));
        }

        // 其他错误状态
        if (status && +status !== 200) {
          if (res.msg) {
            Message.error(res.msg);
          }
          return Promise.reject(res);
        }

        // 正常返回
        return response;
      },
      (error) => {
        // HTTP错误（如404、500等）
        if (error.response) {
          const { status, data } = error.response;

          // 特殊处理401未授权错误
          if (status === 401) {
            Message.error("当前未登录或登录已过期，请重新登录!");
            removeToken();
            setTimeout(() => {
              router.push({ path: "/login" });
            }, 100);
            return Promise.reject(error);
          }

          // 跨域错误特殊处理
          if (status === 0 || error.message.includes("Network Error")) {
            Message.error(
              "网络请求失败，可能是跨域限制导致。请检查API服务器是否允许跨域请求。"
            );
            return Promise.reject(error);
          }

          // 其他错误显示错误信息
          if (data && data.msg) {
            Message.error(data.msg);
          } else {
            Message.error(`请求错误(${status})`);
          }
        } else {
          Message.error("网络异常,请重试!");
        }
        return Promise.reject(error);
      }
    );
  }
}

export const http = new Http();
