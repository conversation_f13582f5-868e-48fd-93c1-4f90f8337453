import {http} from '@/api/request'

/**
 * 实名认证分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function selectAuthenticationPage(param){
  return http.get("/authentication/selectAuthenticationPage",param)
}

/**
 * 实名认证详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function selectById(param){
  return http.get("/authentication/selectById",param)
}

/**
 * 实名认证信息审核
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function examine(param){
  return http.post("/authentication/examine",param)
}
