import {http} from '@/api/request'

/**
 * 认证码 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function pageList(param) {
  return http.get('authenticationCodeManage/selectAuthenticationCodePage',param)
}

/**
 * 认证码 新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function add(param) {
  return http.post('authenticationCodeManage/addAuthenticationCode',
  param)
}

/**
 * 认证码 更新
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function update(param) {
  return http.put('authenticationCodeManage/update',
  param)
}
/**
 * 认证码 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function detail(param) {
  return http.get('authenticationCodeManage/selectById',
  param)
}

/**
 * 认证码 生成邀请码
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function genRandomNum(param) {
  return http.get('authenticationCodeManage/genRandomNum',
  param)
}
