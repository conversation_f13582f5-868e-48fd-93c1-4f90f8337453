import { http } from "@/api/request";

/**
 * 获取课程列表
 * @param {Object} params 查询参数
 * @param {string} params.courseName 课程名称
 * @param {string} params.courseType 课程类型
 * @param {number} params.current 当前页
 * @param {number} params.size 每页数量
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getCourseList(params) {
  return http.get("/course/list", params);
}

/**
 * 获取课程详情
 * @param {string|number} id 课程ID
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getCourseDetail(id) {
  return http.get(`/course/detail?id=${id}`);
}

/**
 * 新增课程
 * @param {Object} data 课程数据
 * @param {string} data.courseName 课程名称
 * @param {string} data.courseDifficulty 课程难度
 * @param {string} data.courseType 课程类型
 * @param {string} data.courseIntroduce 课程简介
 * @param {string} data.coverPath 封面地址
 * @param {string} data.videoPath 视频地址
 * @returns {Promise<AxiosResponse<any>>}
 */
export function addCourse(data) {
  return http.post("/course/addCourse", data);
}

/**
 * 更新课程
 * @param {Object} data 课程数据
 * @param {number} data.id 课程ID
 * @param {string} data.courseName 课程名称
 * @param {string} data.courseDifficulty 课程难度
 * @param {string} data.courseType 课程类型
 * @param {string} data.courseIntroduce 课程简介
 * @param {string} data.coverPath 封面地址
 * @param {string} data.videoPath 视频地址
 * @returns {Promise<AxiosResponse<any>>}
 */
export function updateCourse(data) {
  return http.put("/course/update", data);
}

/**
 * 删除课程
 * @param {string|number} id 课程ID
 * @returns {Promise<AxiosResponse<any>>}
 */
export function deleteCourse(id) {
  return http.delete(`/course/delete/${id}`);
}

/**
 * 上传文件到OSS
 * @param {FormData} formData 包含文件的FormData对象
 * @returns {Promise<AxiosResponse<any>>}
 */
export function uploadFile(formData) {
  return http.post("/oss/endpoint/putPublicFile", formData);
}

/**
 * 获取所有机构
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getAllOrganizations() {
  return http.get("/sysOrganization/all");
}

/**
 * 获取所有人员
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getAllUsers() {
  return http.get("/user/getAll");
}

/**
 * 线下购买课程
 * @param {number} courseId 课程ID
 * @param {Object} data 购买数据
 * @param {number} data.id 记录ID（可选，新增时不传）
 * @param {number} data.orgId 付费机构ID
 * @param {string} data.orgName 付费机构名称
 * @param {number} data.userId 付费用户ID
 * @param {string} data.userName 付费用户名称
 * @param {string} data.paidTime 付费时间
 * @param {number} data.price 付费金额
 * @param {number} data.paidType 付费类型
 * @param {number} data.courseId 课程ID
 * @returns {Promise<AxiosResponse<any>>}
 */
export function offlinePurchaseCourse(courseId, data) {
  return http.post(`/course/${courseId}/paid`, data);
}

/**
 * 课程支付 - 获取微信支付二维码
 * @param {string|number} courseId 课程ID
 * @returns {Promise<AxiosResponse<any>>}
 */
export function payForCourse(courseId) {
  return http.post(`/course/${courseId}/pay`);
}

/**
 * 查询用户对课程的付费状态
 * @param {string|number} courseId 课程ID
 * @returns {Promise<AxiosResponse<any>>} 返回格式：{code: 200, success: true, data: boolean, msg: "操作成功"}
 */
export function checkCoursePaidStatus(courseId) {
  return http.get(`/course/${courseId}/paid`);
}

/**
 * 查询课程支付是否成功
 * @param {string|number} courseId 课程ID
 * @returns {Promise<AxiosResponse<any>>} 返回格式：{code: 1, success: true, data: boolean, msg: ""}
 */
export function checkPaymentSuccess(courseId) {
  return http.get(`/course/${courseId}/pay/success`);
}
