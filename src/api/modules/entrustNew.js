import { http } from "@/api/request";

/**
 * 计划基本信息列表查询
 * @param param
 * @returns {Promise | Promise<unknown>}
 */

export function planCount(param) {
  return http.get("/plan-count", param);
}

export function addPlan(param) {
  return http.post("/sampling-plan-details", param);
}

export function editPlan(param) {
  return http.put("/sampling-plan-details", param);
}

export function getClassA() {
  return http.get("/sampleTask/list-class-tree");
}

export function getInfo(param) {
  return http.get("/sampling-plan-details/info", param);
}

/**
 * 根据 classA 和 classB 获取已存在的计划总批次数
 * @param params { classA: string, classB: string }
 * @returns {Promise | Promise<unknown>}
 */
export function getTotalBatchCount(params) {
  return http.get("/sampling-plan-details/count/exist-total", params);
}

/**
 * 删除计划
 */
export function delPlan(params) {
  // 将 params 对象转换为查询字符串
  const queryString = Object.keys(params).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join('&');
  return http.delete(`/sampling-plan-details/task?${queryString}`);
}

/**
 * 部署计划
 */
export function deploymentPlan(params) {
  return http.put("/sampling-plan-details/deploy-status", params);
}

/**
 * 删除单个食品分类
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function deleteDetectionItem(param) {
  return http.delete('/sampling-plan-details', param)
}
