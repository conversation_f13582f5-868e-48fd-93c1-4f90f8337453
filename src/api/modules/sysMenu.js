import { http } from '@/api/request'

/**
 * 菜单分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function menuPageList(params) {
  return http.get("/system/menu/all", params)
}

/**
 * 新增菜单
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function menuSave(param) {
  return http.post("/system/menu", param)
}

/**
 * 编辑菜单
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function menuEdit(param) {
  return http.put("/system/menu", param)
}

/**
 * 删除菜单
 * @returns {Promise | Promise<unknown>}
 */
export function menuRemove(id) {
  return http.delete(`/system/menu/${id}`)
}
// /**
//  * 菜单分页
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function menuPageList(param){
//   return http.get("/menu/pageList",param)
// }

// /**
//  * 菜单详情
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function menuDetail(param){
//   return http.get("/menu/detail",param)
// }

// /**
//  * 新增菜单
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function menuSave(param){
//   return http.post("/menu/save",param)
// }

// /**
//  * 编辑菜单
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function menuEdit(param){
//   return http.put("/menu/edit",param)
// }

// /**
//  * 删除菜单
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function menuRemove(param){
//   return http.delete("/menu/remove",param)
// }
