import { http } from "@/api/request";

/**
 * 获取机构列表
 * @param {Object} params 查询参数
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getOrganizationList(params) {
  return http.get("/sysOrganization/page", params);
}

/**
 * 新增机构
 * @param {Object} params
 * @returns {Promise<AxiosResponse<any>>}
 */
export function updateOrganization(params) {
  return http.post("/sysOrganization/save", params);
}

/**
 * 删除机构
 * @param {Object} params
 * @returns {Promise<AxiosResponse<any>>}
 */
export function deleteOrganization(id) {
  return http.delete(`/sysOrganization/del?id=${id}`);
}

/**
 * 获取机构信息
 * @param {Object} params 包含机构id
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getOrgInfo(params) {
  return http.get("/sysOrganization/selectOrg", params);
}

/**
 * 更新机构信息
 * @param {Object} params 机构信息
 * @returns {Promise<AxiosResponse<any>>}
 */
export function updateOrgInfo(params) {
  return http.post("/sysOrganization/save", params);
}
