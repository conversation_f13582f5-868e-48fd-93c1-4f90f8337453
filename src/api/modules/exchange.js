import {http} from '@/api/request'

/**
 * 智能币管理-智能币兑换 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function pageList(param) {
  return http.get('/smartCoinsManagement/exchange/pageList',param)
}

/**
 * 智能币管理-智能币兑换 新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function add(param) {
  return http.post('/smartCoinsManagement/exchange/add',
  param)
}

/**
 * 智能币管理-智能币兑换 更新
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function update(param) {
  return http.put('/smartCoinsManagement/exchange/update',
  param)
}
/**
 * 智能币管理-智能币兑换 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function detail(param) {
  return http.get('/smartCoinsManagement/exchange/detail',
  param)
}

/**
 * 智能币管理-智能币兑换 删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function exchangeDelete(param) {
  return http.delete('/smartCoinsManagement/exchange/delete',
  param)
}

/**
 * 智能币管理-智能币兑换 下架
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function down(param) {
  return http.put('/smartCoinsManagement/exchange/down',
  param)
}