import { http } from '@/api/request'

/**
 * 抽检校验-重复抽样 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function repeatedList(param) {
  return http.post('/sampleRepeat/getPage', param)
}

/**
 * 抽检校验-重复抽样 新增/更新
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function repeatedAdd(param) {
  return http.post('/sampleRepeat/saveOrUpdate',
    param)
}

/**
 * 抽检校验-重复抽样 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function repeatedDetail(param) {
  return http.get('/sampleRepeat/selectById',
    param)
}

/**
 * 抽检校验-重复抽样 删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function repeatedDelete(param) {
  return http.get('/sampleRepeat/deleteById',
    param)
}

/**
 * 抽检校验-重复抽样 启用
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function repeatedStart(param) {
  return http.get('/sampleRepeat/startById',
    param)
}
/**
 * 抽检校验-重复抽样 停用
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function repeatedStop(param) {
  return http.get('/sampleRepeat/stopById',
    param)
}



/**
 * 抽检校验-集中抽样 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function concentratedList(param) {
  return http.get('/sample-focus', param)
}

/**
 * 抽检校验-集中抽样 新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function concentratedAdd(param) {
  return http.post("/sample-focus", param)
}

/**
 * 抽检校验-集中抽样 更新
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function concentratedUpdate(ruleId, param) {
  return http.put(`/sample-focus/${ruleId}`,
    param)
}
/**
 * 抽检校验-集中抽样 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function concentratedDetail(ruleId) {
  return http.get(`/sample-focus/${ruleId}`)
}

/**
 * 抽检校验-集中抽样 删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function concentratedDelete(id) {
  return http.delete(`/sample-focus/${id}`)
}

/**
 * 抽检校验-集中抽样 启用
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function concentratedStart(id) {
  return http.put(`/sample-focus/status/${id}?status=1`)
}
/**
 * 抽检校验-集中抽样 停用
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function concentratedStop(id) {
  return http.put(`/sample-focus/status/${id}?status=0`)
}