import { http } from '@/api/request'

/**
 * 筛查分页Get
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function screeningDataPage(param) {
  return http.get('/screening/screeningDataPcPage',param)
 }
/**
 * 筛查详情Get
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function detailsData(param) {
  return http.get('/screening/detailsData', param)
}

/**
 * 映射分页Get
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function screeningReflexPage(param) {
  return http.get('/screening/screeningReflexPage', param)
}

/**
 * 编辑映射Post
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function saveReflex(param) {
  return http.post('/screening/saveReflex',param)
 }

 /**
 * 映射详情Get
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function detailsReflex(param) {
  return http.get('/screening/detailsReflex',param)
 }

/**
 * 删除映射Get
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function delReflex(param) {
  return http.get('/screening/delReflex',param)
 }
