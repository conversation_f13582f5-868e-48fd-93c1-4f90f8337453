import { http } from "@/api/request";

/** 获取用户类型*/
export function getUserType() {
  return http.get("/user/getUserType");
}

/**
 * 修改用户密码
 * @param {Object} param - 包含id和password的对象
 * @returns {Promise}
 */
export function updatePassword(param) {
  return http.post("/user/updatePassword", param);
}

/**
 * 用户列表分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function userPageList(param) {
  return http.get("/user/pageList", param);
}

/**
 * 用户详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function userDetail(param) {
  return http.get("/user/detail", param);
}

/**
 * 用户信息编辑
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function userEdit(param) {
  return http.put("/user/edit", param);
}

/**
 * 用户信息保存
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function userSave(param) {
  return http.post("/user/save", param);
}

/**
 * 删除一个用户
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function userRemove(param) {
  return http.delete("/user/save", param);
}

/**
 * 用户登录日志分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function userLoginPageList(param) {
  return http.get("/loginLog/pageList", param);
}
/**
 * 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function selectUserById(id) {
  return http.get("/user/selectUserById/" + id);
}
/**
 * 用户信息编辑
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function updateUserApp(param) {
  return http.put("/user/updateUserApp", param);
}

/**
 * 用户中心-用户信息分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function userCenterListPage(param) {
  return http.get("/user/userListPage", param);
}

/**
 * 用户中心-用户信息详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function userCenterDetail(param) {
  return http.get("/user/selectById", param);
}

/**
 * 智能币信息
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function specieChangePageList(param) {
  return http.get("/user/smartCoins/info/pageList", param);
}

/**
 * 只能比兑换记录
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function specieConsumePageList(param) {
  return http.get("/user/smartCoins/exRecord/pageList", param);
}

/**
 * 会员变更记录
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function vipList(param) {
  return http.get("/order/adjustVIPPage", param);
}

/**
 * 调整VIP
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function adjustVIP(param) {
  return http.put("/user/adjustVIP", param);
}
