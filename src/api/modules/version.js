import {http} from '@/api/request'

/**
 * 版本管理 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function pageList(param) {
  return http.get('/system/version/pageList',param)
}

/**
 * 版本管理 新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function add(param) {
  return http.post('/system/version/add',
  param)
}

/**
 * 版本管理 更新
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function update(param) {
  return http.put('/system/version/update',
  param)
}
/**
 * 版本管理 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function detail(param) {
  return http.get('/system/version/detail',
  param)
}

/**
 * 版本管理 删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function versionDelete(param) {
  return http.delete('/system/version/delete',
  param)
}
