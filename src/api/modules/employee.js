import { http } from "@/api/request";

/**
 * 获取员工列表
 * @param {Object} params 查询参数
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getEmployeeList(params) {
  return http.get("/user/getList", params);
}

/**
 * 添加员工
 * @param {Object} data 员工数据
 * @returns {Promise<AxiosResponse<any>>}
 */
export function addEmployee(data) {
  return http.post("/auth/register", data);
}

/**
 * 编辑员工
 * @param {Object} data 员工数据
 * @returns {Promise<AxiosResponse<any>>}
 */
export function updateEmployee(data) {
  return http.put("/user/update", data);
}
/**
 * 编辑员工
 * @param {Object} data 员工数据
 * @returns {Promise<AxiosResponse<any>>}
 */
export function updateEmployeeUser(data) {
  return http.put("/user/userRole/update", data);
}

/**
 * 删除员工
 * @param {string} id 员工ID
 * @returns {Promise<AxiosResponse<any>>}
 */
export function deleteEmployee(id) {
  return http.delete(`/user/del?id=${id}`);
}

/**
 * 获取抽样队列表
 * @param {Object} params 查询参数
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getSamplingTeamList(params) {
  return http.get("/sampleTeam/page", {
    pageNum: params.current,
    pageSize: params.size,
    name: params.name,
    leader: params.leader,
  });
}

/**
 * 添加抽样队
 * @param {Object} data 抽样队数据
 * @returns {Promise<AxiosResponse<any>>}
 */
export function addSamplingTeam(data) {
  return http.post("/sampleTeam/save", data);
}

/**
 * 编辑抽样队
 * @param {Object} data 抽样队数据
 * @returns {Promise<AxiosResponse<any>>}
 */
export function updateSamplingTeam(data) {
  return http.put("/sampleTeam/update", data);
}

/**
 * 删除抽样队
 * @param {string} id 抽样队ID
 * @returns {Promise<AxiosResponse<any>>}
 */
export function deleteSamplingTeam(id) {
  return http.delete(`/sampleTeam/${id}`);
}

/**
 * 获取所有用户列表（不分页）
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getAllUsers() {
  return http.get("/user/getAll");
}

/**
 * 重置密码
 * @returns {Promise<AxiosResponse<any>>}
 */
export function resetPassword(params) {
  return http.get("/user/resetPassword", params);
}

/**
 * 获取角色列表
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getRole(params) {
  return http.get(`/user/role/${params.userId}`);
}

/**
 * 所属机构枚举
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getOrgList() {
  return http.get("/sysOrganization/all");
}
