import {http} from '@/api/request'

//分页查询
export function pageSampleVideo(param) {
  return http.post('/curriculum/page', param)
}

//新增
export function insertSampleVideo(param) {
  return http.post('/curriculum/insert', param)
}

//获取详情(用于更新)
export function preSampleVideo(id) {
  return http.get('/curriculum/edit/' + id)
}

//获取详情
export function detailSampleVideo(id) {
  return http.get('/curriculum/details/' + id)
}

//更新
export function updateSampleVideo(param) {
  return http.put('/curriculum/update', param)
}

//删除
export function delSampleVideo(id) {
  return http.delete('/curriculum/delete/' + id)
}

//分页查询
export function pageVideoOrder(param) {
  return http.post('/curriculumOrder/page', param)
}

//获取详情
export function detailVideoOrder(id) {
  return http.get('/curriculumOrder/details/' + id)
}
