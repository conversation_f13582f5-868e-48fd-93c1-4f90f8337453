import { http } from '@/api/request'

/**
 * 抽样进度
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function selectSgList(param) {
  return http.get('/ampleSchedule/selectSgList',param)
 }
/**
 * 总进度
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function selectHnyList(param) {
  return http.get('/ampleSchedule/selectHnyList', param)
}

/**
 * 总进度
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function selectNewscbname() {
  return http.get('/sampleTask/selectNewscbname')
}

