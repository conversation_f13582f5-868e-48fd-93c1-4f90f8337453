import { http } from '@/api/request'

/**
 * 所有角色
 */
export function getRolesList(params) {
  return http.get('/system/role/list', params)
}

/**
 * 角色编辑/新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function roleEdit(params) {
  return http.post("/system/role/roleMenu", params)
}

/**
 * 查询角色绑定的菜单
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function selectRoleMenuId(roleId) {
  return http.get(`/system/role/${roleId}/menu`)
}

/**
 * 删除一个角色
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function roleRemove(id) {
  return http.delete(`/system/role/${id}`)
}
// /**
//  * 所有角色
//  */
// export function getRoles(){
//   return http.get('/role/roleList')
// }

// /**
//  * 角色分页
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function rolePageList(param){
//   return http.get('/role/pageList',param)
// }

// /**
//  * 角色查看
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function roleDetail(param){
//   return http.get("/role/detail",param)
// }

// /**
//  * 角色保存
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function roleSave(param){
//   return http.post("/role/save",param)
// }

// /**
//  * 角色编辑
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function roleEdit(param){
//   return http.put("/role/edit",param)
// }

// /**
//  * 角色绑定菜单
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function roleBindMenu(param){
//   return http.post("/role/bindRoleMenu",param)
// }

// /**
//  * 查询角色绑定的菜单
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function selectRoleMenuId(param){
//   return http.get("/role/selectRoleMenuId",param)
// }

// /**
//  * 删除一个角色
//  * @param param
//  * @returns {Promise | Promise<unknown>}
//  */
// export function roleRemove(param){
//   return http.delete("/role/remove",param)
// }


