import {http} from '@/api/request'

//会员套餐-分页查询
export function pageMemberPackage(param) {
  return http.post('/memberMeal/page', param)
}

//会员套餐-新增
export function insertMemberPackage(param) {
  return http.post('/memberMeal/insert', param)
}

//会员套餐-获取详情(用于更新)
export function preMemberPackage(id) {
  return http.get('/memberMeal/edit/' + id)
}

//会员套餐-获取详情
export function detailMemberPackage(id) {
  return http.get('/memberMeal/details/' + id)
}

//会员套餐-更新
export function updateMemberPackage(param) {
  return http.put('/memberMeal/update', param)
}

//会员套餐-下架
export function takeoffMemberPackage(id) {
  return http.put('/memberMeal/offTheShelf/' + id)
}

//会员套餐-删除
export function delMemberPackage(id) {
  return http.delete('/memberMeal/delete/' + id)
}

//权益类型-新增
export function insertEquityType(param) {
  return http.post('/equityType/insert', param)
}

//权益类型-列表
export function listEquityType(param) {
  return http.get('/equityType/getEquityTypeList', param)
}

//会员权益-分页查询
export function pageEquity(param) {
  return http.post('/equity/page', param)
}

//会员权益-新增
export function insertEquity(param) {
  return http.post('/equity/insert', param)
}

//会员权益-获取详情(用于更新)
export function preEquity(id) {
  return http.get('/equity/edit/' + id)
}

//会员权益-更新
export function updateEquity(param) {
  return http.put('/equity/update', param)
}

//会员权益-删除
export function delEquity(id) {
  return http.delete('/equity/delete/' + id)
}

//会员订单-分页查询
export function pageMemberOrder(param) {
  return http.post('/order/page', param)
}

//会员订单-获取详情
export function detailMemberOrder(id) {
  return http.get('/order/details/' + id)
}
