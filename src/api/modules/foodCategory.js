import { http } from "@/api/request";

export function firstFoodCategory() {
  return http.get("/system/foodCategory/listChildren");
}

export function treeFoodCategory() {
  return http.get("/system/foodCategory/tree");
}

export function othorFoodCategory(param) {
  return http.get("/system/foodCategory/listChildren", param);
}

/**
 * 食品分类分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function fcPageList(param) {
  return http.get("/system/foodCategory/pageList", param);
}

/**
 * 食品分类详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function fcDetail(param) {
  return http.get("/system/foodCategory/detail", param);
}

/**
 * 食品分类保存
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function fcSave(param) {
  return http.post("/system/foodCategory/add", param);
}

/**
 * 食品分类编辑
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function fcEdit(param) {
  return http.put("/system/foodCategory/update", param);
}

/**
 * 食品分类删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function fcRemove(param) {
  return http.delete("/system/foodCategory/delete", param);
}

/**
 * 获取食品类别级联数据
 * @returns {Promise | Promise<unknown>}
 */
export function getFoodCategoryCascade() {
  return http.get("/system/foodCategory/cascade");
}

/**
 * 获取数据维护-食品类别列表
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function getFoodCategoryList(param) {
  return http.get("/dataMaintenance/list", param);
}

/**
 * 获取食品类别列表
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function getFoodCategoryListNew(param) {
  return http.get("/foodCategory/list", param);
}

/**
 * 导出食品类别数据
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function exportFoodCategoryData(param) {
  return http.get("/foodCategory/export", param, { responseType: "blob" });
}

/**
 * 新增数据维护-食品类别
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function addFoodCategory(param) {
  return http.post("/dataMaintenance/add", param);
}

/**
 * 更新数据维护-食品类别
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function updateFoodCategory(param) {
  return http.put("/dataMaintenance/update", param);
}

/**
 * 删除数据维护-食品类别
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function deleteFoodCategory(param) {
  return http.delete("/dataMaintenance/delete", param);
}
