import {http} from '@/api/request'

//分页查询
export function pageHelper(param) {
  return http.get('/app/assistant/pageList', param)
}

//新增
export function insertHelper(param) {
  return http.post('/app/assistant/add', param)
}

//获取详情(用于更新)
export function preHelper(param) {
  return http.get('/app/assistant/detail', param)
}

//更新
export function updateHelper(param) {
  return http.put('/app/assistant/update', param)
}

//删除
export function delHelper(param) {
  return http.delete('/app/assistant/delete', param)
}
