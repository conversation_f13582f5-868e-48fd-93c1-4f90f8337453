import { http } from '@/api/request'

/**
 * 计划基本信息列表查询
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function selectXrHnyList(param) {
  return http.get('/sampleTask/selectXrHnyList',param)
 }
/**
 * 计划基本信息列表查询
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function selectXrHnyById(param) {
  return http.get('/sampleTask/selectXrHnyById', param)
}

/**
 * 删除计划基本信息
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function deleteXrHny(param) {
  return http.delete('/sampleTask/deleteXrHny', param)
}

/**
 * 新增计划基本信息
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function insertXrHny(param) {
  return http.post('/sampleTask/insertXrHny',param)
 }

 /**
 * 编辑计划基本信息
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function updateXrHny(param) {
  return http.put('/sampleTask/updateXrHny',param)
 }




/**
 * 计划管理列表查询
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function selectSampleCateData(param) {
  return http.get('/sampleTask/selectSampleCateData',param)
 }

/**
 * 删除计划管理
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function deleteSampleCate(param) {
  return http.delete('/sampleTask/deleteSampleCate', param)
}

/**
 * 新增计划管理
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function insertSampleCate(param) {
  return http.post('/sampleTask/insertSampleCate',param)
 }

 /**
 * 修改数量
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function updateTotal(param) {
  return http.post('/sampleTask/updateTotal',param)
 }

/**
 * excel批量导入
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function sampleCatesImports(param) {
  return http.post('/sampleTask/sampleCatesImports',param)
}

/**
 * 模板导入
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
// export function downTemplate(param) {
//   return http.post('/sampleTask/downTemplate',param)
// }

export function downTemplate() {
  return http.get('/sampleTask/downTemplate')
}

// 报送分类 列表
export function selectClassB() {
  return http.get('/sampleTask/selectClassB')
}

// 食品大类书级结构
export function findTreeListAll() {
  return http.get('/sampleTask/findTreeListAll')
}

/**
 * 抽样计划管理-抽检数据列表查询
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function selectXrSamList(param) {
  return http.get('/publicityInfo/list', param)
}

/**
 * 抽样计划管理-抽检数据查询-省份枚举值
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function queryProvincesList(param) {
  return http.get('/sampleTask/findTreeListAll', param)
}

/**
 * 抽样计划管理-抽检数据查询-企业名称枚举值
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function queryEnterprisesList(param) {
  return http.get('/sampleTask/findTreeListAll', param)
}

/**
 * 抽样计划管理-抽检数据查询-样品名称枚举值
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function querySampleList(param) {
  return http.get('/sampleTask/findTreeListAll', param)
}

/**
 * 抽样计划管理-抽检数据查询-检测项目枚举值
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function queryDetectionList(param) {
  return http.get('/sampleTask/findTreeListAll', param)
}