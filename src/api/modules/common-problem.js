import {http} from '@/api/request'

/**
 * 常见问题 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function pageList(param) {
  return http.get('/learnPublish/commonProblem/pageList',param)
}

/**
 * 常见问题 新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function add(param) {
  return http.post('/learnPublish/commonProblem/add',
  param)
}

/**
 * 常见问题 更新
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function update(param) {
  return http.put('/learnPublish/commonProblem/update',
  param)
}
/**
 * 常见问题 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function detail(param) {
  return http.get('/learnPublish/commonProblem/detail',
  param)
}

/**
 * 常见问题 删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function commonProblemDelete(param) {
  return http.delete('/learnPublish/commonProblem/delete',
  param)
}

/**
 * 常见问题类型集合
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function commonProblemTypeList(param) {
  return http.get('/learnPublish/commonProblem/typeList',
  param)
}

/**
 * 常见问题类型新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function adjustType(param) {
  return http.post('/learnPublish/commonProblem/adjustType',
  param)
}
