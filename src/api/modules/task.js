import { http } from "@/api/request";

// 模拟抽样队列表数据
const mockSampleTeams = {
  code: 200,
  msg: "操作成功",
  data: [
    {
      id: "101",
      name: "山西省第一抽样队",
    },
    {
      id: "102",
      name: "山西省第二抽样队",
    },
    {
      id: "103",
      name: "湖南省第一抽样队",
    },
  ],
};

// 模拟队员列表数据
const mockTeamMembers = {
  code: 200,
  msg: "操作成功",
  data: [
    {
      id: "1001",
      realName: "张三",
      phone: "13800138001",
      job: "抽样员",
    },
    {
      id: "1002",
      realName: "李四",
      phone: "13800138002",
      job: "抽样员",
    },
    {
      id: "1003",
      realName: "王五",
      phone: "13800138003",
      job: "抽样员",
    },
    {
      id: "1004",
      realName: "赵六",
      phone: "13800138004",
      job: "记录员",
    },
    {
      id: "1005",
      realName: "钱七",
      phone: "13800138005",
      job: "队长",
    },
  ],
};

// 模拟批次列表数据
const mockBatchList = {
  code: 200,
  msg: "操作成功",
  data: [
    {
      id: 1,
      sampleId: "DBJ25500000623201001",
      samplers: "张三、李四",
    },
    {
      id: 2,
      sampleId: "DBJ25500000623201002",
      samplers: "张三、李四",
    },
    {
      id: 3,
      sampleId: "DBJ25500000623201003",
      samplers: "张三、李四",
    },
    {
      id: 4,
      sampleId: "DBJ25500000623201004",
      samplers: "张三、李四",
    },
    {
      id: 5,
      sampleId: "DBJ25500000623201005",
      samplers: "张三、李四",
    },
  ],
};

/**
 * 获取任务列表
 * @param {Object} params 查询参数
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getTaskList(params) {
  // 实际API调用
  return http.get("/sampleTask/selectSampleCateDataNew", params);
}

/**
 * 获取待执行任务列表
 * @param {Object} params 查询参数
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getPendingTaskList(params) {
  // 添加isFinish=0参数表示未完成任务
  const newParams = { ...params };
  // 使用与任务大厅相同的API
  return http.get("/planTask/page", newParams);
}

/**
 * 获取已完成任务列表
 * @param {Object} params 查询参数
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getCompletedTaskList(params) {
  // 添加isFinish=1参数表示已完成任务
  const newParams = { ...params, isFinish: 1 };
  // 使用与任务大厅相同的API
  return http.get("/planTask/page", newParams);
}

/**
 * 获取已完成任务列表 - 新API
 * @param {Object} params 查询参数
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getFinishedTaskList(params) {
  return http.get("/planTaskCode/finishPage", params);
}

/**
 * 分配任务
 * @param {Object} data 分配任务数据
 * @param {string|number} data.taskId - 任务ID
 * @param {string|number} data.teamId - 抽样队ID
 * @param {Array<string|number>} data.memberIds - 抽样人员ID数组
 * @param {number} data.batchNumber - 分配批次数
 * @returns {Promise<AxiosResponse<any>>}
 */
export function claimTask(data) {
  // 实际API调用
  return http.post("/planTask/assignTask", data);

  // 返回mock数据 - 已注释
  // return new Promise((resolve) => {
  //   setTimeout(() => {
  //     resolve({
  //       code: 200,
  //       msg: "操作成功",
  //       data: true,
  //     });
  //   }, 500);
  // });
}

/**
 * 删除任务
 * @param {string|number} id 任务ID
 * @returns {Promise<AxiosResponse<any>>}
 */
export function deleteTask(id) {
  // 实际API调用
  return http.delete(`/planTask/delete/${id}`);
}

/**
 * 获取抽样队列表
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getSampleTeams() {
  // 返回mock数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockSampleTeams);
    }, 300);
  });
  // 实际API调用
  // return http.get("/sampleTeam/list");
}

/**
 * 获取抽样队成员列表
 * @param {string} teamId 抽样队ID
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getTeamMembers(teamId) {
  // 返回mock数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockTeamMembers);
    }, 300);
  });
  // 实际API调用
  // return http.get(`/sampleTeam/members/${teamId}`);
}

/**
 * 获取任务详情
 * @param {string} id 任务ID
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getTaskDetail(id) {
  return http.get(`/planTask/detail?id=${id}`);
}

// 任务相关的API
export function getPendingTasks(params) {
  // 模拟接口，实际项目中应该调用request发送请求
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: "success",
        data: {
          total: 2,
          list: [
            {
              id: 1,
              reportTypeA: "常规监测",
              reportTypeB: "定期监测",
              foodCategory: "粮食加工品",
              foodSubcategory: "米粉制品",
              batchTotal: 8,
              batchCompleted: 3,
            },
            {
              id: 2,
              reportTypeA: "专项监测",
              reportTypeB: "省级专项",
              foodCategory: "调味品",
              foodSubcategory: "酱油",
              batchTotal: 12,
              batchCompleted: 5,
            },
          ],
          currentPage: 1,
          pageSize: 10,
        },
      });
    }, 500);
  });
}

export function getCompletedTasks(params) {
  // 模拟接口，实际项目中应该调用request发送请求
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: "success",
        data: {
          total: 3,
          list: [
            {
              id: 3,
              reportTypeA: "常规监测",
              reportTypeB: "定期监测",
              sampleNumber: 72,
              completeTime: "2023-07-15",
            },
            {
              id: 4,
              reportTypeA: "专项监测",
              reportTypeB: "省级专项",
              sampleNumber: 45,
              completeTime: "2023-07-10",
            },
            {
              id: 5,
              reportTypeA: "常规监测",
              reportTypeB: "定期监测",
              sampleNumber: 30,
              completeTime: "2023-06-28",
            },
          ],
          currentPage: 1,
          pageSize: 10,
        },
      });
    }, 500);
  });
}

/**
 * 获取批次列表
 * @param {Object} params - 查询参数
 * @param {string|number} params.taskId - 任务ID
 * @returns {Promise}
 */
export function getBatchList(params) {
  // 使用模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockBatchList);
    }, 500);
  });

  // 实际接口调用
  // return http.get("/task/batch/list", { params });
}

/**
 * 完成任务
 * @param {Object} data - 请求数据
 * @param {string|number} data.taskId - 任务ID
 * @param {Array<string|number>} data.batchIds - 批次ID数组
 * @returns {Promise}
 */
export function completeTask(data) {
  // 使用模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: "操作成功",
        data: true,
      });
    }, 500);
  });

  // 实际接口调用
  // return http.post("/task/complete", data);
}

/**
 * 获取任务统计数据
 * @param {Object} params - 筛选条件参数对象
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getTaskStatistics(params) {
  // 实际API调用
  return http.get("/planTask/statistics", params);
}

/**
 * 获取分类树数据
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getClassTree(params) {
  // 实际API调用
  return http.get("/sampleTask/list-class-tree", params);
}

/**
 * 获取食品大类列表数据
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getFoodCategoryTree() {
  // 实际API调用
  return http.get("/sampleTask/findTreeListAll");
}

// 获取所有用户列表（抽样人员）
export function getAllUsers() {
  return http.request({
    url: "/user/getAll",
    method: "get",
  });
}

// 提交任务分配
export function collectingTasks(data) {
  return http.request({
    url: "/planTask/collectingTasks",
    method: "post",
    data,
  });
}

// 获取抽样队列表
export function getSampleTeamList() {
  return http.request({
    url: "/sampleTeam/list",
    method: "get",
  });
}

/**
 * 获取抽样单列表
 * @param {Object} params - 查询参数
 * @param {string|number} params.taskId - 任务ID
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页条数
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getSampleCodeList(params) {
  return http.request({
    url: "/planTaskCode/page",
    method: "get",
    params,
  });
}

/**
 * 更新抽样单完成状态
 * @param {Array<string>} codeList - 抽样单号列表
 * @param {string} [sampleNo] - 可选的抽样单号，用于新建抽样单时使用
 * @returns {Promise<AxiosResponse<any>>}
 */
export function updateSampleFinishStatus(codeList, sampleNo) {
  let url = "/planTaskCode/updateFinish";

  // 如果提供了sampleNo，则添加到URL作为查询参数
  if (sampleNo) {
    url = `${url}?sampleNo=${encodeURIComponent(sampleNo)}`;
  }

  return http.request({
    url: url,
    method: "post",
    data: codeList,
  });
}

/**
 * 获取不合格产品排名前五
 * @param {Object} params - 查询参数
 * @param {string} params.year - 年份，如2022
 * @param {string} params.firstCategory - 食品大类，如食用农产品
 * @param {string} params.quarter - 季度，如3
 * @param {string} params.fourthlyCategory - 食品细类，可选
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getTopFiveFailedProducts(params) {
  return http.request({
    url: "/planTask/topFive",
    method: "get",
    params,
  });
}

/**
 * 获取生产企业不合格数据
 * @param {Object} params - 查询参数
 * @param {string} params.year - 年份，如2022
 * @param {string} params.firstCategory - 食品大类，如食用农产品
 * @param {string} params.quarter - 季度，如3
 * @param {string} params.current - 当前页码
 * @param {string} params.size - 每页条数
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getProductionFailedList(params) {
  return http.request({
    url: "/planTask/productionNoPass",
    method: "get",
    params,
  });
}

/**
 * 获取抽检统计数据
 * @param {Object} params - 请求参数
 * @param {string} params.planTaskId - 任务ID
 * @returns {Promise} - 请求结果
 */
export function getSampleTestCount(params) {
  return http.request({
    url: "/planTask/sampleTestCount",
    method: "get",
    params,
  });
}

/**
 * 获取抽检详情数据
 * @param {Object} params - 请求参数
 * @param {string} params.planTaskId - 任务ID
 * @param {string} params.cate - 样品类型
 * @param {string} params.period - 时间周期（week/month/quarter）
 * @returns {Promise} - 请求结果
 */
export function getSampleTestDetail(params) {
  return http.request({
    url: "/planTask/sampleTest",
    method: "get",
    params,
  });
}
