import {http} from '@/api/request'

/**
 * 智能币管理-智能币规则 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function pageList(param) {
  return http.get('/smartCoinsManagement/rule/pageList',param)
}

/**
 * 智能币管理-智能币规则 新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function add(param) {
  return http.post('/smartCoinsManagement/rule/add',
  param)
}

/**
 * 智能币管理-智能币规则 更新
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function update(param) {
  return http.put('/smartCoinsManagement/rule/update',
  param)
}
/**
 * 智能币管理-智能币规则 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function detail(param) {
  return http.get('/smartCoinsManagement/rule/detail',
  param)
}

/**
 * 智能币管理-智能币规则 删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function ruleDelete(param) {
  return http.delete('/smartCoinsManagement/rule/delete',
  param)
}

/**
 * 智能币管理-智能币规则 改变状态
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function changeStatus(param) {
  return http.put('/smartCoinsManagement/rule/changeStatus',
  param)
}