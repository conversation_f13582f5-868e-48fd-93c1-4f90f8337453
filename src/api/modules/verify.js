import {http} from '@/api/request'

/**
 * 社区 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function communityList(param) {
  return http.post('/community/pcPage',param)
}
/**
 * 社区 社区置顶/取消置顶
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function comTopping(param,type) {
  let str = type == 0 ? '/community/topping?id=' : '/community/cancelTopping?id='
  return http.put(str+param)
}
/**
 * 社区 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function communityDetail(param) {
  return http.get('/community/details/'+param)
}
/**
 * 社区 详情 主评论分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function pageAllCommunityCommentPC(param) {
  return http.post('/community/pageAllCommunityCommentPC',param)
}
/**
 * 社区 详情 子评论分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function pageChildCommentsPC(param) {
  return http.post('/community/pageChildCommentsPC',param)
}

/**
 * 社区 详情 收藏用户
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function collectionList(param) {
  return http.get('/community/getCommunityCollectionList',param)
}

/**
 * 社区 详情 点赞用户
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function likesList(param) {
  return http.get('/community/getCommunityLikesList',param)
}

/**
 * 社区 详情 分享用户
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function shareList(param) {
  return http.get('/community/getCommunityShareList',param)
}

/**
 * 社区 详情 评论点赞用户
 * @param param commentId
 * @returns {Promise | Promise<unknown>}
 */
 export function likesCommentList(param) {
  return http.get('/community/getCommentLikesList',param)
}
/**
 * 社区 详情 评论点赞用户
 * @param param commentId
 * @returns {Promise | Promise<unknown>}
 */
 export function deleteCommunityComment(param) {
  return http.delete('/community/adminDeleteCommunityComment?commentId=' + param)
}


/**
 * 社区 话题
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function topicList(param) {
  return http.post('/topic/page',param)
}
/**
 * 社区 话题置顶/取消置顶
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function topping(param,type) {
  let str = type == 0 ? '/topic/topping?id=' : '/topic/cancelTopping?id='
  return http.put(str+param)
}


/**
 * 社区 删除话题
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function deleteTopping(param) {
  return http.delete('/topic/delete/' + param)
}

/**
 * 社区 话题新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function topicAdd(param) {
  return http.post('/topic/insert',param)
}
/**
 * 社区 详情头部
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function topicDetail(param) {
  return http.get('/topic/detail?id='+param)

}
/**
 * 社区 详情列表
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function topicDetailList(param) {
  // return http.get('/topic/detailCommunityPage?id='+param.id+'&current='+param.current+'&size='+param.size)
  return http.get('/topic/detailCommunityPage',param)
}


/**
 * 审核 社区审核 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function pcApprovalPage(param) {
  return http.post('/community/pcApprovalPage',param)
}

/**
 * 审核 社区审核 审核通过
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function approvalCommunityAdopt(param) {
  return http.put('/community/approvalCommunityAdopt',
  param)
}

/**
 * 审核 社区审核 审核不通过
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function approvalCommunityNotAdopt(param) {
  return http.put('/community/approvalCommunityNotAdopt',
  param)
}

/**
 * 审核 社区审核 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function pcApprovalDetails(param) {
  return http.get('/community/pcApprovalDetails/'+param)
}


/**
 * 审核 问答审核 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function QAPageList(param) {
  return http.get('/learnPublish/commonProblem/qa/pageList',param)
}

/**
 * 审核 问答审核 审核通过 1-审核通过|2-审核不通过|3-待审核
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function QACheck(param) {
  return http.put('/learnPublish/commonProblem/qa/check',
  param)
}

/**
 * 审核 食安共享 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function sharePageList(param) {
  return http.get('/sample/check/pageList',param)
}

/**
 * 审核 食安共享 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function shareDetail(param) {
  return http.get('/sample/check/detail',param)
}

/**
 * 审核 食安共享 审核通过 1-审核通过|2-审核不通过|3-待审核
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function shareCheck(param) {
  return http.put('/sample/check/changeStatus',
  param)
}
/**
 * 常见问题-问答 置顶
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function QATop(param) {
  return http.put('/learnPublish/commonProblem/qa/top',param)
}

/**
 * 常见问题-问答 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function QADetail(param) {
  return http.get('/learnPublish/commonProblem/qa/detail',param)
}

/**
 * 常见问题-问答 回复
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function QAComment(param) {
  return http.put('/learnPublish/commonProblem/qa/comment',param)
}
/**
 * 常见问题-问答 删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function QADeleteReply(param) {
  return http.delete('/learnPublish/commonProblem/qa/deleteReply',param)
}
/**
 * 常见问题-问答 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function getComment(param) {
  return http.get('/app/center/qa/getComment',param)
}


/**
 * 食安共享 列表
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function miniList(param) {
  return http.get('/samplePut/pcSampleListPage',param)
}
/**
 * 食安共享 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function miniDetail(param) {
  return http.get('/samplePut/sampleDetail',param)
}

/**
 * 食安共享 编辑
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function miniUpdate(param) {
  return http.post('/samplePut/updateSampleAdmin',param)
}

/**
 * 食安共享 购买人员
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function buyUserInfo(param) {
  return http.post('/sample/share/buyUserInfo',param)
}

