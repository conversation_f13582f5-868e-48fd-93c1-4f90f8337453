import {http} from '@/api/request'

/**
 * 抽样规范 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function pageList(param) {
  return http.get('/learnPublish/samplingSpecification/pageList',param)
}

/**
 * 抽样规范 新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function add(param) {
  return http.post('/learnPublish/samplingSpecification/add',
  param)
}

/**
 * 抽样规范 更新
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function update(param) {
  return http.put('/learnPublish/samplingSpecification/update',
  param)
}
/**
 * 抽样规范 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function detail(param) {
  return http.get('/learnPublish/samplingSpecification/detail',
  param)
}

/**
 * 抽样规范 删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function samplingSpecificationDelete(param) {
  return http.delete('/learnPublish/samplingSpecification/delete',
  param)
}
