import {http} from '@/api/request'

/**
 * 每日鸡汤 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function pageList(param) {
  return http.get('/system/chickenSoup/pageList',param)
}

/**
 * 每日鸡汤 新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function add(param) {
  return http.post('/system/chickenSoup/add',
  param)
}

/**
 * 每日鸡汤 更新
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function update(param) {
  return http.put('/system/chickenSoup/update',
  param)
}
/**
 * 每日鸡汤 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function detail(param) {
  return http.get('/system/chickenSoup/detail',
  param)
}

/**
 * 每日鸡汤 删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function chickenSoupDelete(param) {
  return http.delete('/system/chickenSoup/delete',
  param)
}
