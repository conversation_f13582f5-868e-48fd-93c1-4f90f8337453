import {largeHttp} from '@/api/xRequest'
import {http} from '@/api/request'

/**
 * 今年累积任务和已完成任务数
 */
export function getThisYearTaskLSample(param) {
  return http.get("/spotCheck/getThisYearTask", param)
}

/**
 * 获取当前日期状态下的数据，包含抽样数量，人员，被抽样单位和计划
 */
export function getBaseInfoLSample(param) {
  return http.get("/spotCheck/getBaseInfo", param)
}

/**
 * 获取细类排行榜
 */
export function pageSubClassRank(param) {
  return http.get("/spotCheck/getSubclass", param)
}

/**
 * 获取抽样人员排行
 */
export function listPersonRank(param) {
  return http.get("/spotCheck/getUserInfoList", param)
}

/**
 * 获取被抽样单位排行数据列表
 */
export function listOrganizationRank(param) {
  return http.get("/spotCheck/getEntityUnderSamplingList", param)
}

/**
 * 获取抽样计划进度排行列表
 */
export function listPlanRank(param) {
  return http.get("/spotCheck/getSamplingPlanList", param)
}

/**
 * 根据细类获取抽样人员执行-人员
 */
export function listPersonNum(param) {
  return http.get("/spotCheck/getSamplingUserExecute", param)
}

/**
 * 根据细类获取抽样人员执行-场所地点
 */
export function listPlaceNum(param) {
  return http.get("/spotCheck/getSamplingRegion", param)
}

/**
 * 根据细类获取年度抽样概况
 */
export function listYearNum(param) {
  return http.get("/spotCheck/getSamplingOverview", param)
}

/**
 * 根据细类获取抽样计划进度
 */
export function listPlanNum(param) {
  return http.get("/spotCheck/getSamplePlanSchedule", param)
}

/**
 * 根据用户名或用户 ID 获取用户信息，包含姓名、电话等
 */
export function getPersonDetail(param) {
  return http.get("/spotCheck/getSamplePersonMessage", param)
}

/**
 * 抽样人员子模块中获取当前用户粘度抽样概况数据
 */
export function getOverviewPerson(param) {
  return http.get("/spotCheck/getUserOverview", param)
}

/**
 * 根据用户名或用户 ID 获取年度抽样情况等
 */
export function getPersonChartDetail(param) {
  return http.get("/spotCheck/getUserYearSamplingTrends", param)
}

/**
 * 被抽样单位子模块中获取当前日期下的抽样数量和环比
 */
export function getNumOrg(param) {
  return http.get("/spotCheck/getEntityCurrentSampling", param)
}

/**
 * 被抽样单位子模块中获取年度抽样概况数据
 */
export function getOverviewOrg(param) {
  return http.get("/spotCheck/getUnitOverviewVo", param)
}

/**
 * 被抽样单位子模块中获取年度抽样趋势数据
 */
export function getChartOrg(param) {
  return http.get("/spotCheck/getEntityYearSamplingTrends", param)
}

/**
 * 抽样计划的详情
 */
export function getDetailPlan(param) {
  return http.get("/spotCheck/getSamplePlanMessage", param)
}

/**
 * 抽样计划子模块获取抽样计划概况
 */
export function getOverviewPlan(param) {
  return http.get("/spotCheck/getPlanOverviewVo", param)
}

/**
 * 被抽样单位子模块中获取年度抽样趋势数据
 */
export function getChartPlan(param) {
  return http.get("/spotCheck/getPlanYearSamplingTrends", param)
}

/**
 * 获取被抽样单位单据列表
 */
export function getSamplingOrg(param) {
  return http.get("/spotCheck/getSamplingDetail", param)
}

/**
 * 根据细类名称查询当前日期范围内所有被抽样单位中包含该细类的单位列表信息
 */
export function getOrgMap(param) {
  return http.get("/spotCheck/getEntityUnderSampling", param)
}

/**
 * 根据UserID参数获取当前选择的人员运行轨迹
 */
export function getTrajectory(param) {
  return http.get("/spotCheck/getTrajectoryQuery", param)
}

/**
 * 被抽样单位-按地区-列表数据
 */
export function listAreasRank(param) {
  return http.get("/spotCheck/regions/batch", param)
}

/**
 * 被抽样单位-按地区-抽样数量
 */
export function listAreasNum(param) {
  return http.get("/spotCheck/regions/month", param)
}

/**
 * 被抽样单位-按地区-年度抽样概况
 */
export function listAreasOverview(param) {
  return http.get("/spotCheck/regions/overview", param)
}

/**
 * 被抽样单位-按地区-年度抽样趋势
 */
export function listAreasYear(param) {
  return http.get("/spotCheck/regions/trend", param)
}

/**
 * 被抽样单位-按地区-当前地区所有抽样单  / enterprise：【已抽企业数 / 企业数】
 */
export function queryAreasSampleList(param) {
  return http.get("/spotCheck/regions/details", param)
}
