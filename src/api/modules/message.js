import {http} from '@/api/request'

/**
 * 消息管理 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function pageList(param) {
  return http.get('/system/message/pageList',param)
}

/**
 * 消息管理 新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function add(param) {
  return http.post('/system/message/add',
  param)
}

/**
 * 消息管理 更新
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function update(param) {
  return http.put('/system/message/update',
  param)
}
/**
 * 消息管理 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function detail(param) {
  return http.get('/system/message/detail',
  param)
}

/**
 * 消息管理 删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function messageDelete(param) {
  return http.delete('/system/message/delete',
  param)
}
