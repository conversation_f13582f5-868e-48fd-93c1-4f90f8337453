import {http} from '@/api/request'

/**
 * 抽样细则 分页
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
export function pageList(param) {
  return http.get('/learnPublish/samplingRegulation/pageList',param)
}

/**
 * 抽样细则 新增
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function add(param) {
  return http.post('/learnPublish/samplingRegulation/add',
  param)
}

/**
 * 抽样细则 更新
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function update(param) {
  return http.put('/learnPublish/samplingRegulation/update',
  param)
}
/**
 * 抽样细则 详情
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function detail(param) {
  return http.get('/learnPublish/samplingRegulation/detail',
  param)
}

/**
 * 抽样细则 删除
 * @param param
 * @returns {Promise | Promise<unknown>}
 */
 export function samplingRegulationDelete(param) {
  return http.delete('/learnPublish/samplingRegulation/delete',
  param)
}
