import {http} from '@/api/request'

export function smPageList(param){
  return http.get("/system/samplingMethod/pageList",param)
}

export function smDetail(param){
  return http.get("/system/samplingMethod/detail",param)
}

export function smSave(param){
  return http.post("/system/samplingMethod/add",param)
}

export function smEdit(param){
  return http.put("/system/samplingMethod/update",param)
}


export function smDelete(param){
  return http.delete("/system/samplingMethod/delete",param)
}
export function upload(){
  return http.post("/system/samplingMethod/import",param)
}
