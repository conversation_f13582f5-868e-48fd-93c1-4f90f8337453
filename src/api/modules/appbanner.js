import {http} from '@/api/request'

//分页查询
export function pageAppBanner(param) {
  return http.get('/app/banner/pageList', param)
}

//新增
export function insertAppBanner(param) {
  return http.post('/app/banner/add', param)
}

//获取详情(用于更新)
export function preAppBanner(param) {
  return http.get('/app/banner/detail', param)
}

//更新
export function updateAppBanner(param) {
  return http.put('/app/banner/update', param)
}

//删除
export function delAppBanner(param) {
  return http.delete('/app/banner/delete', param)
}
