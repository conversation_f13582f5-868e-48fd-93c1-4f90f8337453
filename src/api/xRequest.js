import { config } from "@/config";
import axios from "axios";
import { Message } from "element-ui";

export class Http {
  constructor(url) {
    this.baseUrl = url || config.baseURL;
    this.instance = axios.create({
      baseURL: this.baseUrl,
      method: "get",
      timeout: 30000,
      withCredentials: false,
      headers: { "Content-Type": "application/json" },
    });
  }

  put(url, data) {
    return this.request({ method: "put", url, data });
  }

  delete(url, params) {
    return this.request({ method: "delete", url, params });
  }

  post(url, data) {
    return this.request({ method: "post", url, data });
  }

  get(url, params) {
    return this.request({ method: "get", url, params });
  }

  request(params = { method: "get" }) {
    return new Promise((resolve, reject) => {
      this.instance
        .request({
          ...params,
        })
        .then((response) => {
          const status = response.data.code;
          // code不是200的统一通过catch捕获
          if (status && +status !== 200) {
            reject(response.data);
            return;
          }
          resolve(response.data);
        })
        .catch((err) => {
          if (err.response) {
            if (+err.response.data.code >= 8080000) {
              Message.error(err.response.data.msg);
            } else if (err.response.data.msg) {
              Message.error(err.response.data.msg);
            } else {
              Message.error("网络异常,请重试!");
            }
          }
          reject(err);
        });
    });
  }
}

export const largeHttp = new Http("https://lims.hnssj.org.cn/visual-api");
export const xHttp = new Http(config.baseURL); // 使用配置中的API地址
