<template>
  <div id="app" class="layout">
    <keep-alive>
      <router-view v-if="keepAlive" />
    </keep-alive>
    <router-view v-if="!keepAlive" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  components: {},
  data() {
    return {
      unionid: '',
    }
  },
  computed: {
    ...mapGetters(['userTabNavs']),
    showHomeBar() {
      const userTabList = this.userTabNavs.map(item => item.path)
      return !userTabList.includes(this.$route.path)
    },
    keepAlive() {
      return this.$route.meta.keepAlive
    },
  },
  created() {
    localStorage.removeItem('code')
  },
  methods: {},
}
</script>

<style lang="scss">
#app {
  height: 100%;
  // overflow: hidden;
  // background: $background-color;
  // font-family: Avenir, Helvetica, Arial, sans-serif;
  // -webkit-font-smoothing: antialiased;
  // -moz-osx-font-smoothing: grayscale;
  // text-align: center;
  // color: #2c3e50;
}

#nav {
  padding: 30px;
  a {
    font-weight: bold;
    color: #2c3e50;

    &.router-link-exact-active {
      color: #42b983;
    }
  }
}
</style>
