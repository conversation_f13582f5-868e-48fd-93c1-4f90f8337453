
import defaultSettings from '@/settings'
import variables from '@/style/variables.scss'

const { showSettings, tagsView, fixedHeader, sidebarLogo, title } = defaultSettings
const state = {
  theme: variables.theme,
  showSettings: showSettings,
  tagsView: tagsView,
  fixedHeader: fixedHeader,
  sidebarLogo: sidebarLogo,
  title: title
}

const mutations = {
  CHANGE_SETTING: (state = {}, { key = '', value }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  }
}

const actions = {
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

