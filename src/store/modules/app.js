import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  size: 'mini', /*Cookies.get('size') || 'mini'*/
  menus: localStorage.getItem('sampling-assistant-menus') ? JSON.parse(localStorage.getItem('sampling-assistant-menus')) : [] // 菜单
}

const mutations = {
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_SIZE: (state, size) => {
    state.size = size
    Cookies.set('size', size)
  },
  SET_MENUS: (state, menus) => {
    localStorage.setItem('sampling-assistant-menus', JSON.stringify(menus))
    state.menus = menus
  },
  CLOSE_MENUS: (state) => {
    localStorage.removeItem('sampling-assistant-menus')
    state.menus = []
  },
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  },
  setMenus({ commit }, menus) {
    commit('SET_MENUS', menus)
  },
  closeMenus({ commit }, menus) {
    commit('CLOSE_MENUS')
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
