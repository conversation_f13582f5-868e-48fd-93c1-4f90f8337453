const state = {
  model: 2,
  show: true
}

const mutations = {
  TOGGLE_MODEL: (state, model) => {
    state.model = model
  },
  TOGGLE_SHOW: (state, show) => {
    state.show = show
  }
}

const actions = {
  toggleModel({commit}, model) {
    commit('TOGGLE_MODEL', model)
  },
  toggleShow({commit}, show) {
    commit('TOGGLE_SHOW', show)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
