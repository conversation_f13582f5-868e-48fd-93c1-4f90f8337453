import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getter'
import user from './modules/user'
import settings from './modules/settings'
import tagsView from './modules/tagsView'
import app from './modules/app'
import permission from './modules/permission'
import model from "@/store/modules/model";

Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    user,
    settings,
    tagsView,
    app,
    permission,
    model
  },
  getters
})
