<template>
  <el-card class="page-wrapper" shadow="hover">
    <div slot="header" class="card-header" style="text-align: left">
      <span class="header-title">{{ title }}</span>
      <el-button type="text" @click="toBack"><i class="el-icon-back"></i>返回</el-button>
    </div>
    <slot></slot>
  </el-card>
</template>

<script>
export default {
  name: 'Page',
  props: {
    title: {
      type: String,
    },
  },
  methods: {
    toBack() {
      this.$emit('back')
      this.$router.back(-1)
    },
  },
}
</script>

<style lang="scss" scoped>
.page-wrapper {
  .header-title {
    font-size: 20px;
    color: #121212;
  }
  .card-header {
    display: flex;
    justify-content: space-between;
  }
}
</style>
