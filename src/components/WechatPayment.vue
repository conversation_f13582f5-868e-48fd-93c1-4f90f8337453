<template>
  <el-dialog
    title="课程付费"
    :visible.sync="dialogVisible"
    width="400px"
    :close-on-click-modal="false"
    append-to-body
    @close="handleClose"
  >
    <div class="payment-content">
      <!-- 课程信息 -->
      <div class="course-info">
        <div class="company-name">北京信睿浩扬科技有限公司</div>
        <h3 class="course-title">{{ course?.courseName || '' }}</h3>
        <div class="course-price">
          <span class="price-label">课程价格：</span>
          <span class="price-amount">¥{{ formatPrice(course?.price) }}</span>
        </div>
      </div>

      <!-- 支付二维码区域 -->
      <div class="qr-code-section" v-if="qrCodeData">
        <div class="payment-header">
          <i class="el-icon-chat-dot-round wechat-icon"></i>
          <span class="payment-title">微信支付</span>
        </div>
        
        <div class="qr-code-container">
          <canvas ref="qrCanvas" class="qr-code"></canvas>
        </div>
        
        <div class="payment-tips">
          <p>打开手机微信，扫一扫上方二维码，即可完成支付</p>
        </div>

        <!-- 支付状态 -->
        <div class="payment-status" v-if="paymentStatus">
          <div class="status-success" v-if="paymentStatus === 'success'">
            <i class="el-icon-circle-check"></i>
            <span>支付成功</span>
          </div>
          <div class="status-failed" v-else-if="paymentStatus === 'failed'">
            <i class="el-icon-circle-close"></i>
            <span>支付失败</span>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div class="loading-section" v-else-if="loading">
        <el-loading :visible="true" text="正在生成支付二维码..."></el-loading>
      </div>

      <!-- 错误状态 -->
      <div class="error-section" v-else-if="error">
        <el-alert
          title="支付二维码生成失败"
          :description="error"
          type="error"
          show-icon
          :closable="false"
        ></el-alert>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="generateQRCode" 
        :loading="loading"
        v-if="!qrCodeData || error"
      >
        {{ error ? '重新生成' : '生成支付码' }}
      </el-button>
      <el-button 
        type="success" 
        @click="handleCheckPayment"
        :loading="checkingPayment"
        v-if="qrCodeData && !error && paymentStatus !== 'success'"
      >
        我已支付
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import QRCode from 'qrcode'
import { payForCourse, checkPaymentSuccess } from '@/api/modules/course'

export default {
  name: 'WechatPayment',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    course: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      qrCodeData: '',
      error: '',
      paymentStatus: '', // 'success', 'failed', ''
      checkingPayment: false, // 检查支付状态的加载状态
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.course) {
        // 弹窗打开时自动生成二维码
        this.generateQRCode()
      } else if (!newVal) {
        // 弹窗关闭时清理数据
        this.resetData()
      }
    }
  },
  methods: {
    /**
     * 格式化价格显示
     */
    formatPrice(price) {
      if (!price || price === 0) {
        return '0.00'
      }
      return (price / 100).toFixed(2)
    },

    /**
     * 生成支付二维码
     */
    async generateQRCode() {
      if (!this.course || !this.course.id) {
        this.$message.error('课程信息错误')
        return
      }

      this.loading = true
      this.error = ''
      this.qrCodeData = ''
      this.paymentStatus = ''

      try {
        // 调用后端支付接口
        const response = await payForCourse(this.course.id)
        
        if (response.code === 200 && response.success && response.data) {
          this.qrCodeData = response.data
          
          // 生成二维码到canvas
          await this.renderQRCode(response.data)
          
        } else {
          this.error = response.msg || '生成支付二维码失败'
        }
      } catch (err) {
        console.error('生成支付二维码失败:', err)
        this.error = '网络错误，请重试'
      } finally {
        this.loading = false
      }
    },

    /**
     * 渲染二维码到canvas
     */
    async renderQRCode(data) {
      try {
        await this.$nextTick() // 确保DOM已更新
        
        if (!this.$refs.qrCanvas) {
          throw new Error('二维码canvas元素未找到')
        }

        await QRCode.toCanvas(this.$refs.qrCanvas, data, {
          width: 200,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })
      } catch (err) {
        console.error('渲染二维码失败:', err)
        this.error = '二维码生成失败'
      }
    },

    /**
     * 处理"我已支付"按钮点击
     */
    async handleCheckPayment() {
      if (!this.course || !this.course.id) {
        this.$message.error('课程信息错误')
        return
      }

      this.checkingPayment = true
      
      try {
        const response = await checkPaymentSuccess(this.course.id)
        
        if (response.code === 1 && response.success && response.data === true) {
          // 支付成功
          this.handlePaymentSuccess()
        } else {
          // 支付未成功
          this.$message.warning('支付尚未完成，请确认已完成微信支付')
        }
      } catch (error) {
        console.error('检查支付状态失败:', error)
        this.$message.error('检查支付状态失败，请重试')
      } finally {
        this.checkingPayment = false
      }
    },

    /**
     * 支付成功处理
     */
    handlePaymentSuccess() {
      this.paymentStatus = 'success'
      
      this.$message.success('支付成功')
      
      // 通知父组件支付成功
      this.$emit('payment-success', this.course)
      
      // 延迟关闭弹窗
      setTimeout(() => {
        this.handleClose()
      }, 2000)
    },

    /**
     * 关闭弹窗
     */
    handleClose() {
      this.dialogVisible = false
    },

    /**
     * 重置数据
     */
    resetData() {
      this.loading = false
      this.qrCodeData = ''
      this.error = ''
      this.paymentStatus = ''
      this.checkingPayment = false
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-content {
  .course-info {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;

    .company-name {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: #909399;
      text-align: center;
    }

    .course-title {
      margin: 0 0 10px 0;
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }

    .course-price {
      display: flex;
      align-items: center;
      
      .price-label {
        color: #606266;
        margin-right: 8px;
      }
      
      .price-amount {
        font-size: 18px;
        font-weight: bold;
        color: #e6a23c;
      }
    }
  }

  .qr-code-section {
    text-align: center;
    
    .payment-header {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;
      
      .wechat-icon {
        font-size: 24px;
        color: #07c160;
        margin-right: 8px;
      }
      
      .payment-title {
        font-size: 16px;
        font-weight: bold;
        color: #07c160;
      }
    }
    
    .qr-code-container {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
      
      .qr-code {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
      }
    }
    
    .payment-tips {
      margin-bottom: 20px;
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }
    }
    
    .payment-status {
      .status-success {
        color: #67c23a;
        font-size: 16px;
        
        i {
          margin-right: 8px;
          font-size: 18px;
        }
      }
      
      .status-failed {
        color: #f56c6c;
        font-size: 16px;
        
        i {
          margin-right: 8px;
          font-size: 18px;
        }
      }
    }
  }

  .loading-section {
    text-align: center;
    padding: 40px 20px;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .error-section {
    margin: 20px 0;
  }
}

.dialog-footer {
  text-align: right;
}
</style>