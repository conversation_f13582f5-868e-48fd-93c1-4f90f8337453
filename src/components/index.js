import LineCard from './LineCard'
import Table from './Table'
import DetailPanel from './DetailPanel'
import LabelColumn from './LabelColumn'
import Page from './Page'
import SvgIcon from './SvgIcon'
import ListPage from './ListPage'
import Pagination from './Pagination'
import Editor from './Editor'

const components = [Table, LineCard, DetailPanel, LabelColumn, Page, SvgIcon, ListPage, Pagination, Editor]

export default function(Vue) {
  components.forEach(component => {
    Vue.component('simple' + component.name, component)
  })
}
