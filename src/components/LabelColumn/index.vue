<template>
  <div class="label-column-wrapper">
    <label :style="{ width: parseInt(labelWidth) + 'px' }">{{ label }}</label>
    <p class="content">{{ value }}</p>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'LabelColumn',
  props: {
    label: {
      type: String,
    },
    value: {
      type: [String, Number],
    },
    labelWidth: {
      type: Number,
      default: 80,
    },
    colon: {
      type: Boolean,
      default: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.label-column-wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  color: #4c4c4c;
  padding-right: 10px;
  font-size: 14px;
  label {
    font-weight: normal;
  }
  .content {
    margin: 0;
    color: #121212;
    white-space: pre-wrap;
  }
}
</style>
