<template>
  <div class="card-wrapper">
    <el-card :header="header" :shadow="shadow">
      <div class="title" v-if="$slots.header">
        <slot name="header"></slot>
      </div>
      <slot></slot>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'LineCard',
  props: {
    header: {
      type: String,
      default: '',
    },
    shadow: {
      type: String,
      default: 'never',
    },
  },
}
</script>

<style lang="scss" scoped>
.card-wrapper {
  .title {
    font-weight: bold;
    font-size: 20px;
  }
  ::v-deep {
    .el-card {
      border: none;
    }
    .el-card__header {
      // font-weight: bold;
      border-bottom: 1px solid #e6ebf5;
      font-size: 16px;
      &::before {
        content: '';
        height: 20px;
        width: 4px;
        background: $primary;
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
        top: -1px;
        position: relative;
      }
    }
  }
}
</style>
