<template>
  <div class="detailPanel-wrapper">
    <template v-for="item in columns">
      <template v-if="!item.hide">
        <simple-label-column
          class="row"
          v-if="!item.slot"
          :label-width="labelWidth"
          :style="{ width: item.col ? getColWidth(item.col) : colWidth }"
          :label="item.label"
          :value="item.formatter ? item.formatter(data) : data[item.prop]"
          :key="item.label"
        ></simple-label-column>
        <simple-label-column
          class="row"
          v-else
          :label-width="labelWidth"
          :style="{ width: item.col ? getColWidth(item.col) : colWidth }"
          :label="item.label"
          :key="item.label"
        >
          <slot :name="item.prop"></slot>
        </simple-label-column>
      </template>
    </template>
  </div>
</template>

<script>
export default {
  name: 'DetailPanel',
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    col: {
      type: Number,
      default: 3,
    },
    labelWidth: {
      type: Number,
      default: 80,
    },
  },
  computed: {
    colWidth() {
      return parseInt(100 / this.col) + '%'
    },
  },
  methods: {
    getColWidth(col) {
      return parseInt(100 / col) + '%'
    },
  },
}
</script>

<style lang="scss" scoped>
.detailPanel-wrapper {
  display: flex;
  flex-flow: row wrap;
  margin-bottom: 10px;
  .row {
    margin-bottom: 20px;
  }
}
</style>
