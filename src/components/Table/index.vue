<template>
  <div class="table-wrapper">
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      :size="size"
      :tree-props="treeProps"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="showSelection"
        type="selection"
        align="center"
        width="55"
      >
      </el-table-column>
      <el-table-column
        v-if="showIndex"
        type="index"
        width="70"
        label="序号"
        align="center"
      />
      <template v-for="column in columns">
        <template v-if="!column.hide">
          <el-table-column
            v-if="column.slot"
            :key="column.label"
            :label="column.label"
            :min-width="column.width || '120'"
            align="center"
          >
            <template slot-scope="scope">
              <slot :name="column.prop" :row="scope.row" />
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="column.html"
            :key="column.label"
            :label="column.label"
            :min-width="column.width || '120'"
            align="center"
          >
            <template slot-scope="scope">
              <span v-html="scope.row[column.html]"></span>
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="column.prop"
            :key="column.label"
            :label="column.label"
            :prop="column.prop"
            align="center"
            :min-width="column.width || '120'"
            :formatter="column.formatter"
            :show-overflow-tooltip="column.showOverflowTooltip"
          />
        </template>
      </template>
      <el-table-column
        v-if="showOptions"
        :width="optionsWidth + 'px'"
        label="操作"
        align="center"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <slot name="options" :row="scope.row" />
        </template>
      </el-table-column>
    </el-table>
    <div
      style="height: 45px"
      v-show="page.total > 0"
      :class="lastPageHidden ? 'last-page-hidden' : ''"
    >
      <pagination
        :total="+page.total"
        :page.sync="page.current"
        :limit.sync="page.size"
        @pagination="handlePage"
        :layout="layoutPagination"
      />
    </div>
  </div>
</template>

<script>
import Pagination from "../Pagination";

export default {
  name: "Table",
  components: { Pagination },
  props: {
    treeProps: {
      type: Object,
      default: () => ({}),
    },
    columns: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: "small",
    },
    page: {
      type: Object,
      default: () => ({
        total: 0,
        current: 1,
        size: 10,
      }),
    },
    showIndex: {
      type: Boolean,
      default: true,
    },
    showOptions: {
      type: Boolean,
      default: true,
    },
    showSelection: {
      type: Boolean,
      default: false,
    },
    optionsWidth: {
      type: Number,
      default: 120,
    },
    layoutPagination: {
      // 分页配置项
      type: String,
      default: "total, sizes, prev, pager, next, jumper",
    },
    lastPageHidden: {
      // 是否隐藏分页的最后一页页码
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    this.columns.map((column) => {});
  },
  methods: {
    handlePage() {
      this.$emit("load");
    },
    handleSelectionChange(val) {
      this.$emit("selected", val);
    },
    clearSelection() {
      this.$refs.table.clearSelection();
    },
    toggleRowSelection(val) {
      this.$refs.table.toggleRowSelection(val);
    },
  },
};
</script>

<style lang="scss">
.last-page-hidden {
  .number:last-child {
    display: none !important;
  }
}
</style>
