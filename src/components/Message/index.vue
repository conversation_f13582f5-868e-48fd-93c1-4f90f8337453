<template>
  <div class="bgPage">
    <div v-if="count" class="numberCount">{{ this.count }}</div>
    <svg-icon class-name="bell" icon-class="bell" @click="click"></svg-icon>
  </div>
</template>

<script>
import { getNoticeCount, updateNoticeCount } from '@/api/system/message'
export default {
  name: 'bell',
  data() {
    return {
      count: 0,
    }
  },
  mounted() {
    this.getCount()
  },
  methods: {
    getCount() {
      getNoticeCount()
        .then(response => {
          if (+response.code === 200) {
            this.count = response.data
          }
        })
        .catch(() => {
          this.finished = true
        })
        .finally(() => {})
    },
    click() {
      this.$router.push({ path: '/message/message' })
      updateNoticeCount()
        .then(response => {
          if (+response.code === 200) {
            this.count = 0
          }
        })
        .catch(() => {
          this.finished = true
        })
        .finally(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.bgPage {
  position: relative;
}
.bell {
  cursor: pointer;
  font-size: 18px;
  vertical-align: middle;
  // margin-top: 10px;
  margin-top: 20px;
}
.numberCount {
  position: absolute;
  color: white;
  font-size: 12px;
  background-color: red;
  min-height: 18px;
  min-width: 18px;
  line-height: 14px;
  left: 55%;
  top: 10%;
  text-align: center;
  border-radius: 9px;
  padding: 2px;
}
</style>
