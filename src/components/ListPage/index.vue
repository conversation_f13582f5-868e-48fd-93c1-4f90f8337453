<template>
  <div class="list-page-wrapper">
    <el-card class="box-card" style="margin-top: 10px" shadow="hover">
      <div slot="header" class="list-header" style="text-align: left">
        <slot name="top-options"></slot>
      </div>
      <div class="table-btns">
        <slot name="bottom-options"></slot>
      </div>
      <slot></slot>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "ListPage",
  props: {
    showSearch: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {};
  },
  methods: {
    handleQuery() {
      this.$emit("query");
    },
    handleReset() {
      this.$emit("reset");
    },
  },
};
</script>

<style lang="scss">
.list-page-wrapper {
  .card-header {
    display: flex;
    justify-content: space-between;
  }
  .list-header {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
  }
  .table-btns {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
    margin-bottom: 10px;
  }
}
</style>
