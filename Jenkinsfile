@Library('pipeline-devops') _

pipeline {
    // 这里有需要可以选择web12 或者 14
    // jnlp-slave-web12 
    // jnlp-slave-web14
    agent { label 'jnlp-slave-web12'}

    options {
		buildDiscarder(logRotator(numToKeepStr: '10'))
		disableConcurrentBuilds()
		timeout(time: 30, unit: 'MINUTES')
	}

    environment {
        // 镜像的路径  中间的rule是为harbor中创建的项目名称
        IMAGE_REPO = "harborpro.huishian.com/test/sample-helper-hn-test/sample-helper-hn-web"
        // 不用管
        IMAGE_CREDENTIAL = "harbor"
        // 描述
        PROJECT = "抽样助手测试环境"
        // 所有文件的目录
        APPLICATION_RESOURCE_PATH = "deploy"
        // 不是初始化的情况下，只部署这个文件
        APPLICATION_WORKLOAD_FILE_PATH =  "deploy.yaml"
        // 是否初始化全部文件，如果是false只是部署APPLICATION_WORKLOAD_FILE_PATH的文件
        APPLICATION_WATCH = "true"
        // 空间名
        APPLICATION_NAMESPACE = "intelligent-test"
        // 应用的名称
        APPLICATION_NAME = "intelligent-web"
        // 域名
        APPLICATION_URL = "intelligent-test.huishian.com"
        // 接受Jenkins中的git参数
        TAG = "${TAG}"
    }

    stages {
         stage('checkout') {
            steps {
                container('tools-web') {
                    checkout([$class: 'GitSCM', branches: [[name: TAG]]])
                }
            }
        }
        stage('mvn-package') {
             steps {
                 container('tools-web') {
                     script{
                        sh 'npm cache clear --force'
                        sh 'rm -rf node_modules'
                        sh 'rm -rf package-lock.json'
                        sh 'npm install'
                        sh 'npm run prod'
                     }
                 }
             }
         }
         stage('docker-image') {
            steps {
                container('tools-web') {
                    script{
                        devops.docker(
                            "${IMAGE_REPO}",
                            TAG.split("/")[1],
                            IMAGE_CREDENTIAL
                        ).build().push()
                    }
                }
            }
        }
        stage('deploy') {
            steps {
                container('tools-web') {
                    script{
                        devops.simple_deploy(APPLICATION_RESOURCE_PATH,APPLICATION_NAMESPACE,
                         APPLICATION_NAME, APPLICATION_WORKLOAD_FILE_PATH,APPLICATION_URL,APPLICATION_WATCH).simpleStart()
                    }
                }
            }
        }
    }
}
